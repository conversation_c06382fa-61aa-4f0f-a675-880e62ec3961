


"use client";
import React, { useEffect, useState } from "react";
import "./style.scss";
import HotelProviderTable from "./components/HotelProviderTable/HotelProviderTable";
import HotelProviderCreate from "./components/HotelProviderCreate/HotelProviderCreate";
// import HotelProviderCreate from "./components/HotelProviderCreate/HotelProviderCreate";

// Sample hotel provider data for details view
const providerDetailsData = {
  1: {
    id: 1,
    name: "Hilton Hotels",
    username: "hilton_admin",
    email: "<EMAIL>",
    phone: "******-123-4567",
    countryCode: "US",
    status: "Active",
    address: "7930 Jones Branch Drive, McLean, VA 22102",
    website: "https://www.hilton.com",
    description: "Hilton Hotels & Resorts is a global brand of full-service hotels and resorts and the flagship brand of Hilton Worldwide.",
    joinDate: "2023-05-15",
    totalProperties: 6800,
    averageRating: 4.5
  },
  2: {
    id: 2,
    name: "Marriott International",
    username: "marriott_global",
    email: "<EMAIL>",
    phone: "******-380-3000",
    countryCode: "US",
    status: "Active",
    address: "10400 Fernwood Road, Bethesda, MD 20817",
    website: "https://www.marriott.com",
    description: "Marriott International is an American multinational diversified hospitality company that manages and franchises a broad portfolio of hotels and resorts.",
    joinDate: "2023-02-20",
    totalProperties: 7500,
    averageRating: 4.3
  },
  3: {
    id: 3,
    name: "InterContinental Hotels",
    username: "ihg_hotels",
    email: "<EMAIL>",
    phone: "+44-1895-512000",
    countryCode: "UK",
    status: "Active",
    address: "Broadwater Park, Denham, UB9 5HR",
    website: "https://www.ihg.com",
    description: "InterContinental Hotels Group PLC is a British multinational hospitality company headquartered in Denham, England.",
    joinDate: "2023-03-18",
    totalProperties: 5900,
    averageRating: 4.2
  }
  // More provider details could be added similarly
};

function Page() {
  const [showDetails, setShowDetails] = useState<boolean>(false);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [visible, setVisible] = useState<boolean>(false);
  const [showHotelProvider, setShowHotelProvider] = useState<boolean>(false);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [selectedProviderDetails, setSelectedProviderDetails] = useState<any>(null);
  
  const handleCreate = () => {
    setIsEditing(false);
    setSelectedUserId(null);
    setShowHotelProvider(true);
  };

  const handleEdit = (id: number) => {
    setIsEditing(true);
    setSelectedUserId(id);
    setShowHotelProvider(true);
  };

  const handleCloseHotelProvider = () => {
    setShowHotelProvider(false);
    setSelectedUserId(null);
    setIsEditing(false);
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const handleShowDetails = (id: number) => {
    setShowDetails(true);
    if (id) {
      setSelectedUserId(id);
      // Set the selected provider details based on the ID
      setSelectedProviderDetails(providerDetailsData[id] || null);
    }
  };

  const handleCloseDetails = () => {
    setShowDetails(false);
    setSelectedProviderDetails(null);
  };

  useEffect(() => {
    console.log("Hotel provider list page rendered");
  }, []);

  return (
    <>
      <div
        className={`agentList overall-list-padding ${visible ? "visible" : ""}`}
      >
        <div className="addUser">
          <div className="addButton" onClick={handleCreate}>
            Add Hotel Provider +
          </div>
        </div>

   
        <HotelProviderTable
          onEdit={handleEdit}
          onShowDetails={handleShowDetails}
          pageName="hotelProvider"
          showFilter={true}
        />
      </div>

      {/* Hotel Provider Create/Edit Component */}
      <HotelProviderCreate
        showCreate={showHotelProvider}
        handleCloseCreate={handleCloseHotelProvider}
        id={isEditing ? selectedUserId : undefined}
      />

      
    </>
  );
}

export default Page;