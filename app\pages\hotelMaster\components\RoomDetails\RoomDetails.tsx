

import React, { useState } from "react";
import {
  X, User, CheckCircle, XCircle,
  Trash2, DollarSign, Home,
  Wifi, Coffee, Calendar, Check,
  Droplet, Monitor, Shield, Tag, Star
} from "lucide-react";

// Demo data
const demoRoomData = {
  id: "RM001",
  masterRoomId: "MRM001",
  providerId: "PROV123",
  name: "Deluxe Ocean View Suite",
  roomsLeft: 3,
  extraBedText: "Extra bed available for $50/night",
  properties: [
    { code: "Size", data: "45 sqm" },
    { code: "View", data: "Ocean View" },
    { code: "Floor", data: "15th Floor" },
    { code: "Balcony", data: "Private Balcony" }
  ],
  roomLevelAmenities: [
    "Free WiFi",
    "55-inch Smart TV",
    "Mini Bar",
    "In-room Safe",
    "Ocean View Balcony",
    "Coffee Machine",
    "King Size Bed",
    "Marble Bathroom"
  ],
  imageList: [
    {
      url: "https://images.unsplash.com/photo-1611892440504-42a792e24d32?w=400&h=300&fit=crop",
      caption: "Bedroom with Ocean View"
    },
    {
      url: "https://images.unsplash.com/photo-1582719478250-c89cae4dc85b?w=400&h=300&fit=crop",
      caption: "Luxury Bathroom"
    },
    {
      url: "https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=400&h=300&fit=crop",
      caption: "Private Balcony"
    }
  ],
  fareDetail: {
    baseFare: 280,
    markUpFare: 42,
    taxesAndFees: 38,
    instantDiscount: 20,
    totalDiscount: 30,
    totalPrice: 310,
    numberOfPersons: 2,
    taxAndChargeMap: {
      "City Tax": 15,
      "Service Fee": 13,
      "Resort Fee": 10
    },
    discountMap: {
      "Early Bird Discount": 20,
      "Loyalty Discount": 10
    }
  },
  roomOptions: [
    {
      blockId: "BLK001",
      name: "Standard Rate",
      paymentType: "Pay at Hotel",
      fomoTag: "Best Value!",
      isRecommended: true,
      mealBenefits: ["Continental Breakfast", "Welcome Drink"],
      otherBenefits: ["Free WiFi", "Late Checkout", "Pool Access"],
      cancellationBenefits: {
        code: "FLEX",
        data: "Free cancellation up to 24 hours before check-in",
        policy: [
          {
            title: "Free Cancellation",
            subTitle: "Full Refund",
            startDate: "2024-12-01",
            endDate: "2024-12-24",
            cancellationAmount: 0
          },
          {
            title: "Late Cancellation",
            subTitle: "Partial Refund",
            startDate: "2024-12-25",
            endDate: "2024-12-31",
            cancellationAmount: 50
          }
        ]
      },
      fareDetail: {
        baseFare: 280,
        markUpFare: 42,
        taxesAndFees: 38,
        instantDiscount: 20,
        totalDiscount: 30,
        totalPrice: 310,
        numberOfPersons: 2
      }
    },
    {
      blockId: "BLK002",
      name: "Advance Purchase",
      paymentType: "Pay Now",
      fomoTag: "Save 15%!",
      isRecommended: false,
      mealBenefits: ["Continental Breakfast"],
      otherBenefits: ["Free WiFi"],
      cancellationBenefits: {
        code: "STRICT",
        data: "Non-refundable",
        policy: [
          {
            title: "Non-Refundable",
            subTitle: "No Cancellation",
            startDate: "2024-12-01",
            endDate: "2024-12-31",
            cancellationAmount: 280
          }
        ]
      },
      fareDetail: {
        baseFare: 240,
        markUpFare: 36,
        taxesAndFees: 38,
        instantDiscount: 50,
        totalDiscount: 50,
        totalPrice: 264,
        numberOfPersons: 2
      }
    }
  ]
};

// Custom icon components
const BedIcon = ({ size = 16 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
    <path d="M2 4v16"></path>
    <path d="M2 8h18a2 2 0 0 1 2 2v10"></path>
    <path d="M2 17h20"></path>
    <path d="M6 8V6a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v2"></path>
  </svg>
);

const CarIcon = ({ size = 16 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
    <path d="M14 16H9m10 0h3v-3.15a1 1 0 0 0-.84-.99L16 11l-2.7-3.6a1 1 0 0 0-.8-.4H5.24a2 2 0 0 0-1.8 1.1l-.8 1.63A6 6 0 0 0 2 12.42V16h2"></path>
    <circle cx="6.5" cy="16.5" r="2.5"></circle>
    <circle cx="16.5" cy="16.5" r="2.5"></circle>
  </svg>
);

const MinibarIcon = ({ size = 16 }) => (
  <svg width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
    <rect x="3" y="4" width="18" height="16" rx="2"/>
    <path d="M7 8h10"/>
    <path d="M7 12h6"/>
  </svg>
);

interface RoomProperty {
  code: string;
  data: string;
}

interface RoomOption {
  blockId: string;
  name: string;
  paymentType: string;
  fomoTag?: string;
  isRecommended?: boolean;
  mealBenefits?: string[];
  otherBenefits?: string[];
  cancellationBenefits?: {
    code: string;
    data: string;
    policy?: Array<{
      title: string;
      subTitle: string;
      startDate: string;
      endDate: string;
      cancellationAmount: number;
    }>;
  };
  fareDetail?: {
    baseFare: number;
    markUpFare: number;
    taxesAndFees: number;
    instantDiscount: number;
    totalDiscount: number;
    totalPrice: number;
    numberOfPersons: number;
  };
}

interface RoomImage {
  url: string;
  caption: string;
}

interface RoomData {
  id: string;
  masterRoomId: string;
  providerId: string;
  name: string;
  roomsLeft: number;
  extraBedText: string;
  properties: RoomProperty[];
  roomLevelAmenities: string[];
  imageList: RoomImage[];
  fareDetail: {
    baseFare: number;
    markUpFare: number;
    taxesAndFees: number;
    instantDiscount: number;
    totalDiscount: number;
    totalPrice: number;
    numberOfPersons: number;
    taxAndChargeMap: Record<string, number>;
    discountMap: Record<string, number>;
  };
  roomOptions: RoomOption[];
}

interface RoomDetailsProps {
  isOpen?: boolean;
  handleClose?: () => void;
  roomData?: RoomData;
}

const RoomDetailsDemo: React.FC<RoomDetailsProps> = ({
  isOpen = true,
  handleClose = () => {},
  roomData = null
}) => {
  // Use passed roomData or fallback to demo data
  const currentRoomData = roomData || demoRoomData;

  const [selectedRoomOption, setSelectedRoomOption] = useState(currentRoomData.roomOptions[0]);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [confirmAction, setConfirmAction] = useState("");

  const hotelName = "Grand Oceanview Resort";
  const hotelLocation = "Miami Beach, Florida";

  // Update selected room option when roomData changes
  React.useEffect(() => {
    if (currentRoomData?.roomOptions?.length > 0) {
      setSelectedRoomOption(currentRoomData.roomOptions[0]);
    }
  }, [currentRoomData]);

  const getAmenityIcon = (amenity: string) => {
    const lowerAmenity = amenity.toLowerCase();
    if (lowerAmenity.includes("wifi")) return <Wifi size={12} />;
    if (lowerAmenity.includes("tv") || lowerAmenity.includes("television")) return <Monitor size={12} />;
    if (lowerAmenity.includes("minibar") || lowerAmenity.includes("mini bar")) return <MinibarIcon size={12} />;
    if (lowerAmenity.includes("safe")) return <Shield size={12} />;
    if (lowerAmenity.includes("pool") || lowerAmenity.includes("jacuzzi") || lowerAmenity.includes("bath")) return <Droplet size={12} />;
    if (lowerAmenity.includes("parking") || lowerAmenity.includes("car")) return <CarIcon size={12} />;
    if (lowerAmenity.includes("coffee") || lowerAmenity.includes("restaurant") || lowerAmenity.includes("breakfast")) return <Coffee size={12} />;
    if (lowerAmenity.includes("bed")) return <BedIcon size={12} />;
    return <Check size={12} />;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const handleAction = (action: string) => {
    setConfirmAction(action);
    setShowConfirmDialog(true);
  };

  const confirmActionHandler = () => {
    console.log(`Action confirmed: ${confirmAction}`);
    setShowConfirmDialog(false);
    setConfirmAction("");
  };

  return (
    <div
      className={`room-details-full-overlay ${isOpen ? 'block' : 'hidden'}`}
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: '100vw',
        height: '100vh',
        backgroundColor: 'rgba(0, 0, 0, 0.95)',
        backdropFilter: 'blur(10px)',
        WebkitBackdropFilter: 'blur(10px)',
        zIndex: 2147483647,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '1rem'
      }}
    >
      <div
        className="w-full max-w-6xl max-h-[95vh] bg-white rounded-lg shadow-2xl overflow-hidden flex flex-col relative"
        style={{ zIndex: 1000000 }}
      >

        {/* Close Button - Fixed at top right */}
        <button
          className="absolute top-4 right-4 p-2 bg-white rounded-full shadow-lg hover:bg-gray-100 transition-colors"
          onClick={handleClose}
          style={{ zIndex: 1000001 }}
        >
          <X size={20} className="text-gray-600" />
        </button>

        {/* Scrollable Content */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-6 space-y-6">

            {/* Compact Header */}
            <div className="bg-gray-50 rounded-lg p-6">
              <div className="flex flex-col lg:flex-row lg:justify-between lg:items-start gap-4">
                <div className="flex items-start gap-4">
                  <div className="bg-blue-100 p-3 rounded-lg flex-shrink-0">
                    <Home size={24} className="text-blue-600" />
                  </div>
                  <div>
                    <h2 className="text-xl font-semibold text-gray-900 mb-1">{currentRoomData.name}</h2>
                    <div className="flex flex-wrap items-center gap-2 text-sm text-gray-600">
                      <span>{hotelName}</span>
                      <span>•</span>
                      <span>{hotelLocation}</span>
                      <span>•</span>
                      <span>ID: {currentRoomData.id}</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2 bg-green-50 text-green-600 px-3 py-1 rounded-full text-sm flex-shrink-0">
                  <CheckCircle size={12} />
                  Available
                </div>
              </div>

              <div className="flex flex-wrap gap-6 mt-4 text-sm">
                <div className="flex items-center gap-2">
                  <DollarSign size={16} className="text-blue-600" />
                  <span className="font-semibold">{formatCurrency(currentRoomData.fareDetail.totalPrice)}/night</span>
                </div>
                <div className="flex items-center gap-2">
                  <User size={16} className="text-blue-600" />
                  <span>{currentRoomData.fareDetail.numberOfPersons} guests</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar size={16} className="text-blue-600" />
                  <span>{currentRoomData.roomsLeft} available</span>
                </div>
              </div>
            </div>

            {/* Room Options */}
            <div>
              <h3 className="text-lg font-semibold mb-4">Room Options</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {currentRoomData.roomOptions.map((option: RoomOption) => (
                  <button
                    key={option.blockId}
                    className={`p-4 rounded-lg border-2 text-left transition-all ${
                      selectedRoomOption.blockId === option.blockId
                        ? 'border-blue-600 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedRoomOption(option)}
                  >
                    <div className="flex justify-between items-start mb-2">
                      <span className="font-medium">{option.name}</span>
                      {option.isRecommended && <Star size={12} className="text-yellow-500" />}
                    </div>
                    <div className="text-blue-600 font-semibold mb-2">
                      {option.fareDetail ? formatCurrency(option.fareDetail.totalPrice) : 'Price not available'}
                    </div>
                    {option.fomoTag && (
                      <div className="text-xs bg-orange-100 text-orange-700 px-2 py-1 rounded inline-block">
                        {option.fomoTag}
                      </div>
                    )}
                  </button>
                ))}
              </div>
            </div>

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">

              {/* Left Column */}
              <div className="space-y-6">

                {/* Room Information */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold mb-4">Room Information</h3>

                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Room Specifications</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Room ID:</span>
                          <span className="text-gray-900">{currentRoomData.id}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Provider ID:</span>
                          <span className="text-gray-900">{currentRoomData.providerId}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Guests:</span>
                          <span className="text-gray-900">{currentRoomData.fareDetail.numberOfPersons} guests</span>
                        </div>
                        {currentRoomData.extraBedText && (
                          <div className="flex justify-between">
                            <span className="text-gray-600">Extra Bed:</span>
                            <span className="text-gray-900">{currentRoomData.extraBedText}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Room Properties</h4>
                      <div className="space-y-2 text-sm">
                        {currentRoomData.properties.map((property: RoomProperty, index: number) => (
                          <div className="flex justify-between" key={index}>
                            <span className="text-gray-600">{property.code}:</span>
                            <span className="text-gray-900">{property.data}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div className="bg-green-50 p-3 rounded-lg">
                      <h4 className="font-medium text-gray-900 mb-2">Availability</h4>
                      <div className="flex items-center gap-2 text-green-600 text-sm">
                        <CheckCircle size={14} />
                        <span>{currentRoomData.roomsLeft} rooms available</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Room Amenities */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold mb-4">Room Amenities</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    {currentRoomData.roomLevelAmenities.map((amenity: string, index: number) => (
                      <div className="flex items-center gap-2 text-sm p-2 bg-gray-50 rounded" key={index}>
                        {getAmenityIcon(amenity)}
                        <span>{amenity}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Selected Option Details */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold mb-4">Selected Option: {selectedRoomOption.name}</h3>

                  <div className="space-y-4">
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Block ID:</span>
                        <span className="text-gray-900">{selectedRoomOption.blockId}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Payment Type:</span>
                        <span className="text-gray-900">{selectedRoomOption.paymentType}</span>
                      </div>
                      {selectedRoomOption.isRecommended && (
                        <div className="flex items-center gap-2 text-yellow-600 text-sm">
                          <Star size={12} />
                          <span>Recommended Option</span>
                        </div>
                      )}
                    </div>

                    {selectedRoomOption.mealBenefits && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Meal Benefits</h4>
                        <div className="space-y-1">
                          {selectedRoomOption.mealBenefits.map((benefit: string, index: number) => (
                            <div className="flex items-center gap-2 text-sm text-yellow-600" key={index}>
                              <Coffee size={12} />
                              <span>{benefit}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {selectedRoomOption.otherBenefits && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Other Benefits</h4>
                        <div className="space-y-1">
                          {selectedRoomOption.otherBenefits.map((benefit: string, index: number) => (
                            <div className="flex items-center gap-2 text-sm text-blue-600" key={index}>
                              <Tag size={12} />
                              <span>{benefit}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Cancellation Policy</h4>
                      <p className="text-sm text-gray-600 mb-2">
                        {selectedRoomOption.cancellationBenefits?.data || 'No cancellation policy available'}
                      </p>
                      {selectedRoomOption.cancellationBenefits?.policy && (
                        <div className="space-y-2">
                          {selectedRoomOption.cancellationBenefits.policy.map((policy, index: number) => (
                            <div className="bg-gray-50 p-3 rounded text-xs" key={index}>
                              <div className="font-medium mb-1">{policy.title} - {policy.subTitle}</div>
                              <div className="text-gray-600 mb-1">From: {policy.startDate} To: {policy.endDate}</div>
                              <div className="text-red-600">Fee: {formatCurrency(policy.cancellationAmount)}</div>
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Right Column */}
              <div className="space-y-6">

                {/* Room Images */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold mb-4">Room Images</h3>
                  <div className="space-y-4">
                    {currentRoomData.imageList.map((image: RoomImage, index: number) => (
                      <div key={index}>
                        {/* eslint-disable-next-line @next/next/no-img-element */}
                        <img
                          src={image.url}
                          alt={image.caption}
                          className="w-full h-48 object-cover rounded-lg"
                        />
                        <p className="text-sm text-gray-600 mt-2">{image.caption}</p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Pricing Information */}
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <h3 className="text-lg font-semibold mb-4">Pricing Information</h3>

                  {selectedRoomOption.fareDetail ? (
                    <div className="space-y-4">
                      <div className="text-center p-4 bg-blue-50 rounded-lg">
                        <div className="flex items-center justify-center gap-2 text-3xl font-bold text-blue-600">
                          <DollarSign size={24} />
                          <span>{formatCurrency(selectedRoomOption.fareDetail.totalPrice)}</span>
                        </div>
                        <div className="text-gray-600 text-sm mt-1">total price</div>
                      </div>

                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Fare Breakdown</h4>
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span>Base Fare:</span>
                            <span>{formatCurrency(selectedRoomOption.fareDetail.baseFare)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Markup:</span>
                            <span>{formatCurrency(selectedRoomOption.fareDetail.markUpFare)}</span>
                          </div>
                          <div className="flex justify-between">
                            <span>Taxes & Fees:</span>
                            <span>{formatCurrency(selectedRoomOption.fareDetail.taxesAndFees)}</span>
                          </div>
                          <div className="flex justify-between text-green-600">
                            <span>Total Discount:</span>
                            <span>-{formatCurrency(selectedRoomOption.fareDetail.totalDiscount)}</span>
                          </div>
                          <div className="flex justify-between font-semibold pt-2 border-t border-gray-200">
                            <span>Total Price:</span>
                            <span>{formatCurrency(selectedRoomOption.fareDetail.totalPrice)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="text-center p-4 bg-gray-50 rounded-lg">
                      <p className="text-gray-600">Pricing information not available</p>
                    </div>
                  )}

                  {currentRoomData.fareDetail.taxAndChargeMap && (
                    <div className="mt-4">
                      <h4 className="font-medium text-gray-900 mb-2">Tax & Charge Details</h4>
                      <div className="space-y-2 text-sm">
                        {Object.entries(currentRoomData.fareDetail.taxAndChargeMap).map(([key, value]) => (
                          <div className="flex justify-between" key={key}>
                            <span>{key}:</span>
                            <span>{formatCurrency(value as number)}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {currentRoomData.fareDetail.discountMap && (
                    <div className="mt-4">
                      <h4 className="font-medium text-gray-900 mb-2">Discount Details</h4>
                      <div className="space-y-2 text-sm text-green-600">
                        {Object.entries(currentRoomData.fareDetail.discountMap).map(([key, value]) => (
                          <div className="flex justify-between" key={key}>
                            <span>{key}:</span>
                            <span>-{formatCurrency(value as number)}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 pt-6 border-t border-gray-200">

              <button
                className="flex items-center justify-center gap-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
                onClick={() => handleAction('disable')}
              >
                <XCircle size={14} />
                Disable
              </button>
              <button
                className="flex items-center justify-center gap-2 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                onClick={() => handleAction('delete')}
              >
                <Trash2 size={14} />
                Delete
              </button>
            </div>
          </div>
        </div>

        {/* Confirmation Dialog */}
        {showConfirmDialog && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
              <h3 className="text-lg font-semibold mb-4">Confirm Action</h3>
              <p className="text-gray-600 mb-6">
                Are you sure you want to {confirmAction} this room?
              </p>
              <div className="flex gap-3 justify-start">
                <button
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  onClick={() => setShowConfirmDialog(false)}
                >
                  Cancel
                </button>
                <button
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  onClick={confirmActionHandler}
                >
                  Confirm
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default RoomDetailsDemo;