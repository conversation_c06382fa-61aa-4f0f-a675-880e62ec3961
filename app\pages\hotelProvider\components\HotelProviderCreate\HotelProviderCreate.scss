@import '/styles/variables.scss';
.agent-create-container{
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
   right: 0;
   background-color: rgba(0, 0, 0, 0.7);
   backdrop-filter: blur(3px);
   z-index: 5001;
   display: flex;
   justify-content: center;
   align-items: center;
   opacity: 0;
   z-index: -1;
   transition: opacity 0.3s ease, z-index 0s linear 0.3s;
   overflow-y: auto;

   &.show{
    opacity: 1;
    z-index: 1000;
}


    .agent-create-form{
        background-color: $white_color;
        padding: 28px 60px 20px 60px;
        position: relative;
        border-radius: 6px;

        h3{
            text-align: center;
            margin-bottom: 20px;
            color: darken( $black_color2, 15%);
            font-weight: 600;
            font-size: 18px;
        }

        .closeIcon{
            position: absolute;
            top: 15px;
            bottom: 0;
            right: 10px;
            //left: 0;
            font-size: 15px;
            cursor: pointer;
            font-weight: 600;

        }


        .input-field-container{
            display: flex;
            flex-direction: row;
            justify-content: center;
            margin-bottom: 8px;
      
       

            .input-field{
                display: flex;
                flex-direction: column;
                margin-bottom: 4px;

                
               

                label{
                    font-size: 11px;
                    color: $black_color2;
                    font-weight: 600;
                    margin-bottom: 5px;

                    
                }

                input{
                    padding: 8px;
                    border: 2px solid $black_color4;
                   border-radius: 6px;
                   

                   &::placeholder{
                    color: $black_color4;
                    font-size:11px;
                   }
                }

                
            }

            
        }

        .fileUploadField{
            align-items: center;
            gap: 13px;
        }

     

        .addMoreField{
            display: flex;
            justify-content: start;
            padding-bottom: 8px;

            p{
                font-size: 11px;
                text-decoration: underline;
                text-underline-offset: 2px;
                color: $black_color3;
                font-weight: 600;
                transition: color 0.2s ease;
                cursor: pointer;

                &:hover{
                    color: darken($black_color3, 10%);
                }
            }
        }

        .name{
            gap: 13px;
        }

        .input-field-container-dotted{
            margin-bottom: 12px;

            button{
                background-color: $white_color;
                border: 2px dotted $black_color4;
                padding: 10px 0;
                font-size: 10px;
                font-weight: 500;
                color: $black_color3;
                border-radius: 6px;
            }
        }

        .fileUpload{
            background-color: $white_color1;
            border: 2px dotted $black_color4;
            border-radius: 6px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            padding: 1px 0 12px 0;
            margin-bottom: 20px;

            .uploadIcon{
                color: $black_color2;
                margin-bottom: 6px;

                span{
                    font-size: 28px;
                    cursor: pointer;
                }
            }

            .desc{
                font-size: 9px;
                color: $black_color;
                font-weight: 600;
                padding-bottom: 3px;

                span{
                    color: $primary_color;
                    text-decoration: underline;
                    cursor: pointer;

                    &:hover{
                        color: darken($primary_color , 10%);
                    }
                }

            }

            
            .fileFormat{
                font-size: 8px;
                color: $black_color3;
                font-weight: 600;
            }


        }

        .SubmitBtn{
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
        }

    

    }
   }