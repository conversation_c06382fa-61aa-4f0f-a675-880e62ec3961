{"name": "dashboard-skeleton", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@babel/runtime": "^7.26.9", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/material": "^6.4.5", "axios": "^1.7.9", "echarts": "^5.6.0", "lucide-react": "^0.507.0", "next": "15.1.7", "react": "^18.3.1", "react-beautiful-dnd": "^13.1.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-hook-form": "^7.54.2", "react-icons": "^5.5.0", "sass": "^1.85.0", "scss": "^0.2.4"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^19", "babel-plugin-inline-react-svg": "^2.0.2", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}