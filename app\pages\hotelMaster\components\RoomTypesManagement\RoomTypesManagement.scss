/* Add these styles to your existing SCSS file */

.roomTypeCard {
  position: relative;
  /* Your existing card styles */
}

.roomTypeActions {
  position: absolute;
  bottom: 12px;
  right: 12px;
  display: flex;
  gap: 8px;

  // REMOVE opacity transition
  opacity: 1;
}

.roomTypeCard:hover .roomTypeActions {
  opacity: 1;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  
 
}

.view-btn {
  color: #6b7280;
  
  &:hover {
    background: rgba(107, 114, 128, 0.1);
    color: #4b5563;
  }
}

.edit-btn {
  color: #3b82f6;
  
  &:hover {
    background: rgba(59, 130, 246, 0.1);
    color: #2563eb;
  }
}

/* Alternative: Always visible buttons */
.roomTypeActions.always-visible {
  opacity: 1;
}

/* Alternative: Different positioning */
.roomTypeActions.top-right {
  top: 12px;
  right: 12px;
  bottom: auto;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .roomTypeActions {
    opacity: 1; /* Always show on mobile */
    position: relative;
    bottom: auto;
    right: auto;
    justify-content: flex-end;
    margin-top: 12px;
  }
  
  .action-btn {
    padding: 8px 16px;
    font-size: 14px;
  }
}