@use "/styles/variables" as *;

.searchBox {
  display: flex;
  align-items: center;
  border: 1px solid $black_color4;
  padding: 0 8px;
  border-radius: 6px;
  height: 35px;
  position: relative;

  input {
    border: 0;
    background-color: transparent;
    width: 150px;

    &:focus {
      border: 0;
      outline: none;
    }
  }

  .searchIcon {
    color: #2d364f99;
    margin-right: 3px;
    font-size: 20px;

    @media (max-width: $breakpoint-sm) {
      font-size: 17px;
    }
  }

  input::placeholder {
    color: #2d364f99;
    font-weight: 600;
    font-size: 10px;
    opacity: 1;
    transform: translateY(0); // Initial position
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
  }

  // Slide-up transition for placeholder effect
  .fadeOut::placeholder {
    opacity: 0;
    transform: translateY(-10px);
  }

  .fadeIn::placeholder {
    opacity: 1;
    transform: translateY(0);
  }
}
