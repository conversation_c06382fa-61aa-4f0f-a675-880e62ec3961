'use client';
import React from 'react';
import Header from '../components/Header/Header';
import Sidebar from '../components/Sidebar/Sidebar';
import Loader from '../components/Loader/Loader';
import { useCommonContext } from '../context/commonContext';


export default function Layout({ children }:  React.PropsWithChildren ) {
  const {isMenuExpanded} = useCommonContext()
  return (
    <div  className="page-container">
      <div className={`side-bar-section ${isMenuExpanded ? "expanded" : "collapse"}`}>
        <Sidebar />
      </div>
      <div className={`content-header-section ${isMenuExpanded ? 'menuExpanded' : 'menucollapsed'}`}>
        <Header/>
        
          <div className="content-container">
            {children}
          </div>
      </div>
      <Loader />
    </div>
  );
}

