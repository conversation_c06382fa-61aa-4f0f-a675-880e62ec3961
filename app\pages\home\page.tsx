

"use client";

import React, { useState } from 'react';
import './Home.scss';
import DashboardCharts from './components/Charts/Charts';
import BookingsList from './components/BookingsList/BookingsList';
import HotelList from './components/HotelList/HotelList';
import WalletBalance from './components/WalletBalance/WalletBalance';
import HotelProviderList from './components/HotelProviderList/HotelProviderList';

const App: React.FC = () => {

  return (
    <div className="dashboard">
      {/* Key Metrics Cards */}
      <div className="metrics-grid">
        <div className="metric-card">
          <div className="metric-header">
            <span className="metric-icon booking-icon">
              <i className="fas fa-calendar-check"></i>
            </span>
            <span className="metric-label">Total Bookings</span>
          </div>
          <span className="metric-value">40</span>
        </div>

        <div className="metric-card">
          <div className="metric-header">
            <span className="metric-icon revenue-icon">
              <i className="fas fa-chart-line"></i>
            </span>
            <span className="metric-label">Total Revenue</span>
          </div>
          <span className="metric-value">$25,000</span>
        </div>

        <div className="metric-card">
          <div className="metric-header">
            <span className="metric-icon hotel-icon">
              <i className="fas fa-hotel"></i>
            </span>
            <span className="metric-label">Active Hotels</span>
          </div>
          <span className="metric-value">15</span>
        </div>

        <div className="metric-card">
          <div className="metric-header">
            <span className="metric-icon provider-icon">
              <i className="fas fa-user-tie"></i>
            </span>
            <span className="metric-label">Active Providers</span>
          </div>
          <span className="metric-value">7</span>
        </div>
      </div>

      {/* Charts Section */}
      <DashboardCharts/>

      {/* Tables Section */}
      <div className="tables-grid">
        <BookingsList />
        <HotelList />
      </div>

      {/* Bottom Tables Section */}
      <div className="tables-grid">
        <WalletBalance />
        <HotelProviderList />
      </div>
    </div>
  );
};

export default App;