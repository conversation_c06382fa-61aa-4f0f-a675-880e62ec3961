"use client";
import React, { useEffect, useState } from "react";
import "./BookingDetails.scss";
import { useAlert } from "@/app/utilities/Alert/Alert";
import { FiX, FiArrowRight, FiUser, FiCheckCircle, FiXCircle, FiTrash2, FiCalendar, FiCreditCard } from "react-icons/fi";

interface BookingDetailsProps {
  showDetails: boolean;
  bookingId: number;
  handleClose: () => void;
}

interface RoomData {
  roomId: string;
  roomType: string;
  guests: number;
  price: string;
  profilePicture?: string;
  guestName?: string;
}

interface BookingData {
  id: string;
  guestName: string;
  hotelName: string;
  checkIn: string;
  checkOut: string;
  bookingDate: string;
  paymentStatus: string;
  bookingStatus: string;
  amountPaid: string;
  paymentMethod: string;
  is_active: boolean;
  rooms: RoomData[];
}

const BookingDetails: React.FC<BookingDetailsProps> = ({
  showDetails,
  bookingId,
  handleClose,
}) => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [booking, setBooking] = useState<BookingData | null>(null);
  const { fire } = useAlert();

  // Sample booking data (in a real app, you would fetch this data based on bookingId)
  const bookingData: BookingData[] = [
    {
      id: "BK1001",
      guestName: "John Doe",
      hotelName: "Seaside Resort & Spa",
      checkIn: "2025-05-10",
      checkOut: "2025-05-15",
      bookingDate: "2025-04-20",
      paymentStatus: "Paid",
      bookingStatus: "Confirmed",
      amountPaid: "$1,245.00",
      paymentMethod: "Credit Card",
      is_active: true,
      rooms: [
        {
          roomId: "R101",
          roomType: "Deluxe Ocean View",
          guests: 2,
          price: "$545.00",
          profilePicture: "/images/profile.png",
          guestName: "John Doe"
        },
        {
          roomId: "R102",
          roomType: "Standard Twin",
          guests: 2,
          price: "$350.00",
          profilePicture: "/images/profile.png",
          guestName: "Jane Doe"
        },
        {
          roomId: "R103",
          roomType: "Junior Suite",
          guests: 1,
          price: "$350.00",
          profilePicture: "/images/profile.png",
          guestName: "Michael Doe"
        }
      ]
    },
    {
      id: "BK1002",
      guestName: "Sarah Johnson",
      hotelName: "Mountain View Lodge",
      checkIn: "2025-05-08",
      checkOut: "2025-05-12",
      bookingDate: "2025-04-15",
      paymentStatus: "Pending",
      bookingStatus: "Awaiting Payment",
      amountPaid: "$0.00",
      paymentMethod: "Bank Transfer",
      is_active: true,
      rooms: [
        {
          roomId: "R201",
          roomType: "Standard Twin",
          guests: 2,
          price: "$290.00",
          guestName: "Sarah Johnson"
        }
      ]
    },
    {
      id: "BK1003",
      guestName: "Michael Smith",
      hotelName: "City Center Hotel",
      checkIn: "2025-05-20",
      checkOut: "2025-05-23",
      bookingDate: "2025-05-01",
      paymentStatus: "Paid",
      bookingStatus: "Confirmed",
      amountPaid: "$820.00",
      paymentMethod: "PayPal",
      is_active: true,
      rooms: [
        {
          roomId: "R301",
          roomType: "Executive Suite",
          guests: 1,
          price: "$520.00",
          guestName: "Michael Smith"
        },
        {
          roomId: "R302",
          roomType: "Standard King",
          guests: 2,
          price: "$300.00",
          guestName: "Lisa Smith"
        }
      ]
    }
  ];

  useEffect(() => {
    if (showDetails && bookingId !== undefined) {
      // In a real application, you would fetch the data here
      // For now, we'll simulate a delay and set the data from our sample
      setIsLoading(true);
      
      const timer = setTimeout(() => {
        if (bookingId >= 0 && bookingId < bookingData.length) {
          setBooking(bookingData[bookingId]);
        } else {
          setBooking(null);
        }
        setIsLoading(false);
      }, 800);
      
      return () => clearTimeout(timer);
    }
  }, [showDetails, bookingId]);

  // Calculate nights of stay
  const calculateNights = (checkIn: string, checkOut: string) => {
    const start = new Date(checkIn);
    const end = new Date(checkOut);
    const diffTime = Math.abs(end.getTime() - start.getTime());
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  };

  // Handle cancel booking
  const handleCancelBooking = () => {
    if (!booking) return;
    
    const action = booking.bookingStatus === "Confirmed" ? "cancel" : "confirm";
    
    fire({
      icon: "info",
      title: `${action.charAt(0).toUpperCase() + action.slice(1)} Booking`,
      text: `Are you sure you want to ${action} this booking?`,
      confirmButtonText: `Yes, ${action}`,
      cancelButtonText: "No, Go Back",
      onConfirm: () => {
        // In a real app, you would make an API call to cancel/confirm the booking
        setBooking({
          ...booking,
          bookingStatus: booking.bookingStatus === "Confirmed" ? "Cancelled" : "Confirmed"
        });
      }
    });
  };

  // Handle delete booking
  const handleDeleteBooking = () => {
    fire({
      icon: "error",
      title: "Delete Booking",
      text: "Are you sure you want to permanently delete this booking? This action cannot be undone.",
      confirmButtonText: "Yes, Delete",
      cancelButtonText: "No, Go Back",
      onConfirm: () => {
        // In a real app, you would make an API call to delete the booking
        // Here we'll simulate deletion by closing the details panel
        handleClose();
      }
    });
  };

  if (!showDetails) return null;

  return (
    <div className="bookingDetailsOverlay">
      <div className="bookingDetails">
        <div className="bookingDetailsContainer">
          <div className="bookingHeader">
            <h1>Booking Details</h1>
            <button className="closeButton" onClick={handleClose}>
              <FiX size={20} />
            </button>
          </div>

          {isLoading ? (
            <div className="bookingDetailsLoading">
              <div className="loader"></div>
              <p>Loading booking details...</p>
            </div>
          ) : booking ? (
            <div className="bookingContent">
              <div className="bookingTopSection">
                {/* Reorganized structure to put guest info and summary in same container */}
                <div className="bookingTopContent">
                  <div className="guestInfoCard">
                    <div className="guestImageContainer">
                      <FiUser size={24} color="#718096" />
                    </div>
                    <div className="guestInfo">
                      <h2>{booking.guestName}</h2>
                      <div className="bookingId">
                        <span className="idLabel">Booking ID:</span>
                        <span className="idValue">{booking.id}</span>
                      </div>
                      <div className="statusBadge">
                        <div
                          className={
                            booking.bookingStatus === "Confirmed"
                              ? "statusActive"
                              : booking.bookingStatus === "Cancelled"
                              ? "statusCancelled"
                              : "statusPending"
                          }
                        >
                          {booking.bookingStatus === "Confirmed" && <FiCheckCircle size={12} style={{ marginRight: '4px' }} />}
                          {booking.bookingStatus === "Cancelled" && <FiXCircle size={12} style={{ marginRight: '4px' }} />}
                          {booking.bookingStatus === "Awaiting Payment" && <FiCreditCard size={12} style={{ marginRight: '4px' }} />}
                          {booking.bookingStatus}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="summaryCard">
                    <div className="summaryHeader">
                      <h3>Stay Details</h3>
                      <FiCalendar size={14} />
                    </div>
                    <div className="summaryContent">
                      <div className="dateInfo">
                        <div className="checkDate">
                          <span className="dateLabel">Check-in</span>
                          <span className="dateValue">{booking.checkIn}</span>
                        </div>
                        <div className="dateDivider">
                          <FiArrowRight size={14} />
                        </div>
                        <div className="checkDate">
                          <span className="dateLabel">Check-out</span>
                          <span className="dateValue">{booking.checkOut}</span>
                        </div>
                        <div className="dateDivider">
                          <FiArrowRight size={14} />
                        </div>
                        <div className="checkDate">
                          <span className="dateLabel">Duration</span>
                          <span className="dateValue">{calculateNights(booking.checkIn, booking.checkOut)} Nights</span>
                        </div>
                      </div>
                    </div>
                  </div>
                   <div className="bookingPaymentSection">
                  <div className="summaryCard">
                    <div className="summaryHeader">
                      <h3>Payment Status</h3>
                      <FiCreditCard size={14} />
                    </div>
                    <div className="summaryContent">
                      <div className={`paymentStatus ${booking.paymentStatus.toLowerCase()}`}>
                        {booking.paymentStatus === "Paid" && <FiCheckCircle size={12} style={{ marginRight: '4px' }} />}
                        {booking.paymentStatus === "Pending" && <FiCreditCard size={12} style={{ marginRight: '4px' }} />}
                        {booking.paymentStatus}
                      </div>
                      <div className="amountPaid">
                        <span className="amountLabel">Amount:</span>
                        <span className="amountValue">{booking.amountPaid}</span>
                      </div>
                      <div className="detailItem" style={{ marginTop: '8px' }}>
                        <span className="detailLabel">Payment Method</span>
                        <span className="detailValue">{booking.paymentMethod}</span>
                      </div>
                    </div>
                  </div>
                </div>
                </div>
                
                {/* Payment info moved to the right */}
              
              </div>

              <div className="bookingDetailsGrid">
                <div className="detailsCard">
                  <div className="cardHeader">
                    <h3>Hotel Information</h3>
                  </div>
                  <div className="cardContent">
                    <div className="detailItem">
                      <span className="detailLabel">Hotel Name</span>
                      <span className="detailValue">{booking.hotelName}</span>
                    </div>
                    <div className="detailItem">
                      <span className="detailLabel">Total Rooms</span>
                      <span className="detailValue">{booking.rooms.length}</span>
                    </div>
                    <div className="detailItem">
                      <span className="detailLabel">Total Guests</span>
                      <span className="detailValue">{booking.rooms.reduce((total, room) => total + room.guests, 0)}</span>
                    </div>
                  </div>
                </div>

                <div className="detailsCard">
                  <div className="cardHeader">
                    <h3>Booking Information</h3>
                  </div>
                  <div className="cardContent">
                    <div className="detailItem">
                      <span className="detailLabel">Booking Date</span>
                      <span className="detailValue">{booking.bookingDate}</span>
                    </div>
                    <div className="detailItem">
                      <span className="detailLabel">Status</span>
                      <span className="detailValue">{booking.bookingStatus}</span>
                    </div>
                    <div className="detailItem">
                      <span className="detailLabel">Booking ID</span>
                      <span className="detailValue">{booking.id}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bookingRoomsSection">
                <h3 className="sectionTitle">Booked Rooms</h3>
                <div className="roomsGrid">
                  {booking.rooms.map((room, index) => (
                    <div className="roomCard" key={room.roomId}>
                      <div className="roomHeader">
                        <h4>Room {index + 1}: {room.roomType}</h4>
                        <div className="roomPrice">{room.price}</div>
                      </div>
                      <div className="roomContent">
                        <div className="guestProfileContainer">
                          <div className="guestProfileImage">
                            <FiUser size={16} color="#718096" />
                          </div>
                          <div className="guestProfileInfo">
                            <span className="guestName">{room.guestName || 'Guest'}</span>
                            <span className="guestCount">
                              <FiUser size={12} /> {room.guests} {room.guests > 1 ? 'Guests' : 'Guest'}
                            </span>
                          </div>
                        </div>
                        <div className="roomDetails">
                          <div className="roomDetail">
                            <span className="detailLabel">Room ID</span>
                            <span className="detailValue">{room.roomId}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="actionsContainer">
                <button 
                  className="actionButton secondary"
                  onClick={handleCancelBooking}
                  disabled={booking.bookingStatus === "Cancelled"}
                >
                  {booking.bookingStatus === "Confirmed" ? (
                    <>
                      <FiXCircle size={16} /> Cancel Booking
                    </>
                  ) : booking.bookingStatus === "Cancelled" ? (
                    <>
                      <FiXCircle size={16} /> Booking Cancelled
                    </>
                  ) : (
                    <>
                      <FiCheckCircle size={16} /> Confirm Booking
                    </>
                  )}
                </button>
                <button 
                  className="actionButton delete"
                  onClick={handleDeleteBooking}
                >
                  <FiTrash2 size={16} /> Delete Booking
                </button>
              </div>
            </div>
          ) : (
            <div className="noDataFound">
              <p>No booking data found.</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default BookingDetails;