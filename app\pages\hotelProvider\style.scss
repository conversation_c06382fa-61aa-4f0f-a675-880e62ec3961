@use '/styles/variables' as *;

.agentList{
    width: 100%;
    background-color: $white_color1;
    height: auto;
    



  .table-style{
     table{

      tbody{
        tr{
          td{

             .name-profilePic {
          display: flex;
          flex-direction: row;
          align-items: center;
          min-width: 140px;

          .name {
            color: $black_color;
            font-size: 12px;
            font-weight: 700;
          }

          .profile-pic {
            position: relative;
            width: 40px;
            height: 40px;
            background-color: $black_color2;
            border-radius: 50%;
            margin-right: 5px;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            img {
              object-fit: contain;
            }
          }
        }
            

            .empId{
              color: $black_color3;
              font-weight: 600;
              font-size: 12px;
            }

            .walletAmount{
              color: $green_color2;
              font-weight: 600;
              font-size: 12px;
            }

            

            .contact{
              color: $black_color;
              font-weight: 600;
              font-size: 12px;
            }


            .user-not-available{
              width: 95px;              
              color: $red_color;
              font-size: 10px;
              font-weight: 500;
              text-align: center;
              

             }
            .viewDetails{
              a{
                font-size: 12px;
              color: #637CBD;
              cursor: pointer;
              text-underline-offset: 4px;
              font-weight: 600;
             
     
             &:hover{
                  color: darken(#637CBD, 10%);
     
              }
              }
             }

          }

        }

      }
   
          
  
   
   
           
        }
    
     }
}

