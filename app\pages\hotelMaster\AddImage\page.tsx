

"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import { useForm, SubmitHandler } from "react-hook-form";
import { useCommonContext } from "@/app/context/commonContext";
import { useAlert } from "@/app/utilities/Alert/Alert";
import "./AddImage.scss";
import { useRouter, useSearchParams } from "next/navigation";
import ImageUploader from "@/app/components/image-uploader/ImageUploader";

export interface HotelFormData {
  // Basic Information
  name: string;
  hotel_code: string;
  location_id: string;
  hotel_logo: File | null;
  hotel_images: hotel_images[];
  hotel_chain: string;

  // Contact Information
  contactPerson: string;
  contactEmail: string;
  contactPhone: string;

  // Location Details
  address: string;
  locality: string;
  city: string;
  latitude: number;
  longitude: number;

  // Hotel Details
  accommodationType: string;
  starRating: number;
  establishedDate: string;
  totalRooms: number;
  availableRooms: number;
  description: string;

  // Amenities
  amenities: string[];

  // Policies
  checkinTime: string;
  checkoutTime: string;

  // Pricing
  baseFare: number;
  taxesAndFees: number;

  // Status
  status: string;

  // Spotlight Information
  spotlightInfo: SpotlightData[];
}

export interface hotel_images {
  url: File | string;
  priority: number;
  type: string;
  label: string;
}

export interface SpotlightData {
  title: string;
  description: string;
  icon: string;
}

// Expanded HotelPage props to match usage in parent component

function HotelPage() {
  const { fire } = useAlert();
  const router = useRouter();
  const searchParams = useSearchParams();
  const id = searchParams.get('id');
  const { setIsLoading } = useCommonContext();
  const [isEditMode, setIsEditMode] = useState<boolean>(!!id);
  const [hotelImages, setHotelImages] = useState<hotel_images[]>([]);
  const [showAddImageModal, setShowAddImageModal] = useState<boolean>(false);
  const [draggedIndex, setDraggedIndex] = useState<number | null>(null);
  const [dropIndex, setDropIndex] = useState<number | null>(null);

  // Note: Removed unused state variables for cleaner code
  // If you need amenities or spotlight functionality, implement the handlers

  // New image form state
  const [newImage, setNewImage] = useState<{
    file: File | null;
    label: string;
    type: string;
    priority: number;
  }>({
    file: null,
    label: "",
    type: "room",
    priority: 1,
  });

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
    reset,
    watch,
  } = useForm<HotelFormData>({
    defaultValues: {
      // Basic Information
      name: "",
      hotel_code: "",
      location_id: "",
      hotel_logo: null,
      hotel_images: [],
      hotel_chain: "",

      // Contact Information
      contactPerson: "",
      contactEmail: "",
      contactPhone: "",

      // Location Details
      address: "",
      locality: "",
      city: "",
      latitude: 0,
      longitude: 0,

      // Hotel Details
      accommodationType: "Hotel",
      starRating: 1,
      establishedDate: "",
      totalRooms: 0,
      availableRooms: 0,
      description: "",

      // Amenities
      amenities: [],

      // Policies
      checkinTime: "3:00 PM",
      checkoutTime: "11:00 AM",

      // Pricing
      baseFare: 0,
      taxesAndFees: 0,

      // Status
      status: "Available",

      // Spotlight Information
      spotlightInfo: [],
    },
  });

  // Watch hotel_logo to display preview
  const hotelLogo = watch("hotel_logo");

  // Handle logo file selection
  const handleLogoChange = (file: File | null) => {
    setValue("hotel_logo", file);
  };

  // Open add image modal
  const handleAddImageClick = () => {
    setNewImage({
      file: null,
      label: "",
      type: "room",
      priority: hotelImages.length + 1,
    });
    setShowAddImageModal(true);
  };

  // Handle new image file selection
  const handleImageFileChange = (file: File | null) => {
    setNewImage({
      ...newImage,
      file: file,
    });
  };

  // Add new image to hotel images
  const addNewImage = () => {
    if (newImage.file) {
      const imageToAdd: hotel_images = {
        url: newImage.file,
        label: newImage.label,
        type: newImage.type,
        priority: newImage.priority,
      };

      const updatedImages = [...hotelImages, imageToAdd];
      setHotelImages(updatedImages);
      setValue("hotel_images", updatedImages);
      setShowAddImageModal(false);
    } else {
      fire({
        title: "Error",
        text: "Please select an image file",
        icon: "error",
      });
    }
  };

  // Handle removing a hotel image
  const removeHotelImage = (index: number) => {
    const updatedImages = [...hotelImages];
    updatedImages.splice(index, 1);

    // Reorder priorities
    updatedImages.forEach((img, idx) => {
      img.priority = idx + 1;
    });

    setHotelImages(updatedImages);
    setValue("hotel_images", updatedImages);
  };

  // Handle drag start
  const handleDragStart = (index: number) => {
    setDraggedIndex(index);
  };

  // Handle drag over
  const handleDragOver = (e: React.DragEvent, index: number) => {
    e.preventDefault();
    setDropIndex(index);
  };

  // Handle drop to reorder images
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();

    if (
      draggedIndex !== null &&
      dropIndex !== null &&
      draggedIndex !== dropIndex
    ) {
      const updatedImages = [...hotelImages];
      const draggedItem = updatedImages[draggedIndex];

      // Remove the dragged item
      updatedImages.splice(draggedIndex, 1);

      // Insert at the drop position
      updatedImages.splice(dropIndex, 0, draggedItem);

      // Update priorities
      updatedImages.forEach((img, idx) => {
        img.priority = idx + 1;
      });

      setHotelImages(updatedImages);
      setValue("hotel_images", updatedImages);
    }

    setDraggedIndex(null);
    setDropIndex(null);
  };

  // Close modal handler
  const handleCloseModal = () => {
    setShowAddImageModal(false);
  };

  const onSubmit: SubmitHandler<HotelFormData> = (data) => {
    setIsLoading(true);

    // Form submission logic would go here
    console.log("Submitting form data:", data);

    // Example of how you might handle the submission
    setTimeout(() => {
      setIsLoading(false);
      fire({
        title: "Success",
        text: `Hotel ${isEditMode ? "updated" : "added"} successfully!`,
        icon: "success",
      });

      // Navigate back to hotel master page
      router.push("/pages/hotelMaster");
    }, 1000);
  };

  // Load hotel data when in edit mode
  useEffect(() => {
    if (id) {
      setIsEditMode(true);
      setIsLoading(true);

      // Fetch hotel data by ID
      setTimeout(() => {
        // Mocked data
        const mockHotelData = {
          name: "Example Hotel",
          hotel_code: id,
          location_id: "NYC",
          hotel_chain: "marriott",
          hotel_logo: null,
          hotel_images: [],
        };

        reset(mockHotelData);
        setHotelImages(mockHotelData.hotel_images);
        setIsLoading(false);
      }, 1000);
    } else {
      setIsEditMode(false);
      // Reset form for new hotel
      reset({
        name: "",
        hotel_code: "",
        location_id: "",
        hotel_chain: "",
        hotel_logo: null,
        hotel_images: []
      });
      setHotelImages([]);
    }
  }, [id, reset, setIsLoading]);

  // Handle cancel button
  const handleCancel = () => {
    router.push("/pages/hotelMaster");
  };

  return (
    <div className="hotel-page-container">
      <div className="hotel-form-wrapper">
        <button
          className="closeButton1"
          type="button"
          onClick={handleCancel}
        >
          Cancel
        </button>

        <h2 className="page-title">{isEditMode ? "Edit Hotel" : "Add Hotel"}</h2>

        <div className="form-content">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="form-fields-wrapper">
              <div className="form-field half-width">
                <label htmlFor="name">Hotel Name</label>
                <input
                  id="name"
                  type="text"
                  placeholder="Enter hotel name..."
                  {...register("name", {
                    required: "Please provide the hotel name.",
                  })}
                />
                {errors.name && (
                  <p className="field-error">{errors.name.message}</p>
                )}
              </div>
              <div className="form-field half-width">
                <label htmlFor="hotel_code">Hotel Code</label>
                <input
                  id="hotel_code"
                  type="text"
                  placeholder="Enter hotel code..."
                  {...register("hotel_code")}
                />
                {errors.hotel_code && (
                  <p className="field-error">{errors.hotel_code.message}</p>
                )}
              </div>
              <div className="form-field half-width">
                <label htmlFor="location_id">Hotel Location</label>
                <input
                  {...register("location_id", {
                    required: "Please enter the hotel's location.",
                  })}
                  id="location_id"
                  type="text"
                  placeholder="Enter hotel location..."
                />
                {errors.location_id && (
                  <p className="field-error">{errors.location_id.message}</p>
                )}
              </div>
              <div className="form-field half-width">
                <label htmlFor="hotel_chain">Hotel Chain</label>
                <select
                  {...register("hotel_chain", {
                    required: "Please select a hotel chain.",
                  })}
                  id="hotel_chain"
                  className="form-select"
                >
                  <option value="">Select a hotel chain...</option>
                  <option value="marriott">Marriott</option>
                  <option value="hilton">Hilton</option>
                  <option value="hyatt">Hyatt</option>
                  <option value="ihg">IHG</option>
                  <option value="accor">Accor</option>
                </select>
                {errors.hotel_chain && (
                  <p className="field-error">{errors.hotel_chain.message}</p>
                )}
              </div>

              {/* Contact Information Section */}
              <div className="form-section-header">
                <h3>Contact Information</h3>
              </div>

              <div className="form-field half-width">
                <label htmlFor="contactPerson">Contact Person</label>
                <input
                  {...register("contactPerson", {
                    required: "Please enter contact person name.",
                  })}
                  id="contactPerson"
                  type="text"
                  placeholder="Enter contact person name..."
                />
                {errors.contactPerson && (
                  <p className="field-error">{errors.contactPerson.message}</p>
                )}
              </div>

              <div className="form-field half-width">
                <label htmlFor="contactEmail">Contact Email</label>
                <input
                  {...register("contactEmail", {
                    required: "Please enter contact email.",
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: "Please enter a valid email address."
                    }
                  })}
                  id="contactEmail"
                  type="email"
                  placeholder="Enter contact email..."
                />
                {errors.contactEmail && (
                  <p className="field-error">{errors.contactEmail.message}</p>
                )}
              </div>

              <div className="form-field half-width">
                <label htmlFor="contactPhone">Contact Phone</label>
                <input
                  {...register("contactPhone", {
                    required: "Please enter contact phone number.",
                  })}
                  id="contactPhone"
                  type="tel"
                  placeholder="Enter contact phone..."
                />
                {errors.contactPhone && (
                  <p className="field-error">{errors.contactPhone.message}</p>
                )}
              </div>

              {/* Location Details Section */}
              <div className="form-section-header">
                <h3>Location Details</h3>
              </div>

              <div className="form-field full-width">
                <label htmlFor="address">Address</label>
                <textarea
                  {...register("address", {
                    required: "Please enter hotel address.",
                  })}
                  id="address"
                  placeholder="Enter complete hotel address..."
                  rows={3}
                />
                {errors.address && (
                  <p className="field-error">{errors.address.message}</p>
                )}
              </div>

              <div className="form-field half-width">
                <label htmlFor="locality">Locality</label>
                <input
                  {...register("locality", {
                    required: "Please enter locality.",
                  })}
                  id="locality"
                  type="text"
                  placeholder="Enter locality..."
                />
                {errors.locality && (
                  <p className="field-error">{errors.locality.message}</p>
                )}
              </div>

              <div className="form-field half-width">
                <label htmlFor="city">City</label>
                <input
                  {...register("city", {
                    required: "Please enter city.",
                  })}
                  id="city"
                  type="text"
                  placeholder="Enter city..."
                />
                {errors.city && (
                  <p className="field-error">{errors.city.message}</p>
                )}
              </div>

              <div className="form-field half-width">
                <label htmlFor="latitude">Latitude</label>
                <input
                  {...register("latitude", {
                    required: "Please enter latitude.",
                    valueAsNumber: true,
                  })}
                  id="latitude"
                  type="number"
                  step="any"
                  placeholder="Enter latitude..."
                />
                {errors.latitude && (
                  <p className="field-error">{errors.latitude.message}</p>
                )}
              </div>

              <div className="form-field half-width">
                <label htmlFor="longitude">Longitude</label>
                <input
                  {...register("longitude", {
                    required: "Please enter longitude.",
                    valueAsNumber: true,
                  })}
                  id="longitude"
                  type="number"
                  step="any"
                  placeholder="Enter longitude..."
                />
                {errors.longitude && (
                  <p className="field-error">{errors.longitude.message}</p>
                )}
              </div>

              {/* Hotel Details Section */}
              <div className="form-section-header">
                <h3>Hotel Details</h3>
              </div>

              <div className="form-field half-width">
                <label htmlFor="accommodationType">Accommodation Type</label>
                <select
                  {...register("accommodationType", {
                    required: "Please select accommodation type.",
                  })}
                  id="accommodationType"
                  className="form-select"
                >
                  <option value="Hotel">Hotel</option>
                  <option value="Resort">Resort</option>
                  <option value="Motel">Motel</option>
                  <option value="Inn">Inn</option>
                  <option value="Lodge">Lodge</option>
                  <option value="Hostel">Hostel</option>
                  <option value="Apartment">Apartment</option>
                  <option value="Villa">Villa</option>
                </select>
                {errors.accommodationType && (
                  <p className="field-error">{errors.accommodationType.message}</p>
                )}
              </div>

              <div className="form-field half-width">
                <label htmlFor="starRating">Star Rating</label>
                <select
                  {...register("starRating", {
                    required: "Please select star rating.",
                    valueAsNumber: true,
                  })}
                  id="starRating"
                  className="form-select"
                >
                  <option value={1}>1 Star</option>
                  <option value={2}>2 Stars</option>
                  <option value={3}>3 Stars</option>
                  <option value={4}>4 Stars</option>
                  <option value={5}>5 Stars</option>
                </select>
                {errors.starRating && (
                  <p className="field-error">{errors.starRating.message}</p>
                )}
              </div>

              <div className="form-field half-width">
                <label htmlFor="establishedDate">Established Date</label>
                <input
                  {...register("establishedDate", {
                    required: "Please enter established date.",
                  })}
                  id="establishedDate"
                  type="date"
                />
                {errors.establishedDate && (
                  <p className="field-error">{errors.establishedDate.message}</p>
                )}
              </div>

              <div className="form-field half-width">
                <label htmlFor="totalRooms">Total Rooms</label>
                <input
                  {...register("totalRooms", {
                    required: "Please enter total rooms.",
                    valueAsNumber: true,
                    min: { value: 1, message: "Total rooms must be at least 1." }
                  })}
                  id="totalRooms"
                  type="number"
                  placeholder="Enter total rooms..."
                />
                {errors.totalRooms && (
                  <p className="field-error">{errors.totalRooms.message}</p>
                )}
              </div>

              <div className="form-field half-width">
                <label htmlFor="availableRooms">Available Rooms</label>
                <input
                  {...register("availableRooms", {
                    required: "Please enter available rooms.",
                    valueAsNumber: true,
                    min: { value: 0, message: "Available rooms cannot be negative." }
                  })}
                  id="availableRooms"
                  type="number"
                  placeholder="Enter available rooms..."
                />
                {errors.availableRooms && (
                  <p className="field-error">{errors.availableRooms.message}</p>
                )}
              </div>

              <div className="form-field half-width">
                <label htmlFor="status">Status</label>
                <select
                  {...register("status", {
                    required: "Please select status.",
                  })}
                  id="status"
                  className="form-select"
                >
                  <option value="Available">Available</option>
                  <option value="Unavailable">Unavailable</option>
                  <option value="Maintenance">Under Maintenance</option>
                </select>
                {errors.status && (
                  <p className="field-error">{errors.status.message}</p>
                )}
              </div>

              <div className="form-field full-width">
                <label htmlFor="description">Description</label>
                <textarea
                  {...register("description", {
                    required: "Please enter hotel description.",
                  })}
                  id="description"
                  placeholder="Enter hotel description..."
                  rows={4}
                />
                {errors.description && (
                  <p className="field-error">{errors.description.message}</p>
                )}
              </div>

              <div className="form-field full-width">
                <label>Hotel Logo</label>
                <ImageUploader
                  uploadedImage={hotelLogo}
                  isOpen={true}
                  allowedTypes={["image/jpeg", "image/png"]}
                  maxSize={5 * 1024 * 1024} // 5MB
                  onImageUpload={handleLogoChange}
                  label="Upload Logo"
                  requiredMessage="Logo is required"
                  maxFileSizeMessage="Logo size exceeds maximum allowed"
                  invalidTypeMessage="Invalid image type"
                />
              </div>

              {/* Hotel Images Container */}
              <div className="form-field full-width">
                <h3>Hotel Images</h3>
                <div className="hotel-images-container">
                  {/* Rearranged image container with header */}
                  <div className="hotel-images-header">
                    <h4>
                      {hotelImages.length > 0
                        ? "Hotel Images (Drag to reorder)"
                        : "No Images Added Yet"}
                    </h4>
                  </div>

                  {/* Modal for adding a new image */}
                  {showAddImageModal && (
                    <div className="add-image-modal">
                      <div className="modal-content">
                        <div className="modal-header">
                          <h4>Add New Hotel Image</h4>
                          <button
                            type="button"
                            className="close-modal"
                            onClick={handleCloseModal}
                          >
                            &times;
                          </button>
                        </div>
                        <div className="modal-body">
                          <div className="form-field">
                            <label>Image</label>
                            <ImageUploader
                              uploadedImage={newImage.file}
                              isOpen={true}
                              allowedTypes={["image/jpeg", "image/png"]}
                              maxSize={5 * 1024 * 1024} // 5MB
                              onImageUpload={handleImageFileChange}
                              label="Upload Image"
                              requiredMessage="Image is required"
                              maxFileSizeMessage="Image size exceeds maximum allowed"
                              invalidTypeMessage="Invalid image type"
                            />
                          </div>
                          <div className="form-field">
                            <label>Label</label>
                            <input
                              type="text"
                              placeholder="e.g. Room View, Lobby, etc."
                              value={newImage.label}
                              onChange={(e) =>
                                setNewImage({...newImage, label: e.target.value})
                              }
                            />
                          </div>
                          <div className="form-field">
                            <label>Type</label>
                            <select
                              value={newImage.type}
                              onChange={(e) =>
                                setNewImage({...newImage, type: e.target.value})
                              }
                            >
                              <option value="room">Room</option>
                              <option value="exterior">Exterior</option>
                              <option value="lobby">Lobby</option>
                              <option value="amenity">Amenity</option>
                              <option value="restaurant">Restaurant</option>
                              <option value="other">Other</option>
                            </select>
                          </div>
                        </div>
                        <div className="modal-footer">
                          <button
                            type="button"
                            className="cancel-button"
                            onClick={handleCloseModal}
                          >
                            Cancel
                          </button>
                          <button
                            type="button"
                            className="add-button"
                            onClick={addNewImage}
                            disabled={!newImage.file}
                          >
                            Add Image
                          </button>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Display uploaded images with better drag indicators */}
                  <div className="hotel-images-list">
                    <div className="hotel-images-grid">
                      {hotelImages.map((image, index) => (
                        <div
                          key={index}
                          className={`hotel-image-item ${
                            draggedIndex === index ? "dragging" : ""
                          } ${dropIndex === index ? "drop-target" : ""}`}
                          draggable
                          onDragStart={() => handleDragStart(index)}
                          onDragOver={(e) => handleDragOver(e, index)}
                          onDrop={handleDrop}
                          onDragEnd={() => {
                            setDraggedIndex(null);
                            setDropIndex(null);
                          }}
                        >
                          <div className="drag-handle">
                            <svg
                              width="20"
                              height="20"
                              viewBox="0 0 24 24"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <path
                                d="M8 9H16M8 12H16M8 15H16"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                              />
                            </svg>
                          </div>
                          <div className="image-priority">{image.priority}</div>
                          <div className="image-preview">
                            {typeof image.url === "object" && (
                              <Image
                                src={URL.createObjectURL(image.url as File)}
                                alt={`Hotel Image ${index + 1}`}
                                width={120}
                                height={80}
                                style={{ objectFit: "cover" }}
                              />
                            )}
                            {typeof image.url === "string" && (
                              <Image
                                src={image.url}
                                alt={`Hotel Image ${index + 1}`}
                                width={120}
                                height={80}
                                style={{ objectFit: "cover" }}
                              />
                            )}
                          </div>

                          <div className="image-details">
                            <div className="image-info">
                              <p>
                                <strong>Label:</strong>{" "}
                                {image.label || "No label"}
                              </p>
                              <p>
                                <strong>Type:</strong> {image.type}
                              </p>
                            </div>

                            <button
                              type="button"
                              className="remove-image-btn"
                              onClick={() => removeHotelImage(index)}
                            >
                              Remove
                            </button>
                          </div>
                        </div>
                      ))}

                      {/* "Add Another Image" card styled element */}
                      <div
                        className="add-image-card"
                        onClick={handleAddImageClick}
                      >
                        <div className="add-icon">
                          <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <circle
                              cx="12"
                              cy="12"
                              r="10"
                              fill="rgba(var(--primary-rgb), 0.1)"
                            />
                            <path
                              d="M12 8v8M8 12h8"
                              stroke="var(--primary-color)"
                              strokeWidth="2"
                              strokeLinecap="round"
                            />
                          </svg>
                        </div>
                        <div className="add-text">
                          <span>
                            {hotelImages.length > 0
                              ? "Add Another Image"
                              : "Add Image"}
                          </span>
                          <p>Upload a new hotel image</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Amenities Section */}
              <div className="form-section-header">
                <h3>Amenities</h3>
              </div>

              <div className="form-field full-width">
                <label>Hotel Amenities</label>
                <div className="amenities-grid">
                  {[
                    "WiFi", "Parking", "Pool", "Gym", "Spa", "Restaurant",
                    "Bar", "Room Service", "Laundry", "Business Center",
                    "Conference Room", "Airport Shuttle", "Pet Friendly",
                    "Air Conditioning", "Elevator", "Concierge"
                  ].map((amenity) => (
                    <label key={amenity} className="amenity-checkbox">
                      <input
                        type="checkbox"
                        value={amenity}
                        {...register("amenities")}
                      />
                      <span className="checkmark"></span>
                      {amenity}
                    </label>
                  ))}
                </div>
              </div>

              {/* Policies Section */}
              <div className="form-section-header">
                <h3>Policies & Rules</h3>
              </div>

              <div className="form-field half-width">
                <label htmlFor="checkinTime">Check-in Time</label>
                <input
                  {...register("checkinTime", {
                    required: "Please enter check-in time.",
                  })}
                  id="checkinTime"
                  type="time"
                />
                {errors.checkinTime && (
                  <p className="field-error">{errors.checkinTime.message}</p>
                )}
              </div>

              <div className="form-field half-width">
                <label htmlFor="checkoutTime">Check-out Time</label>
                <input
                  {...register("checkoutTime", {
                    required: "Please enter check-out time.",
                  })}
                  id="checkoutTime"
                  type="time"
                />
                {errors.checkoutTime && (
                  <p className="field-error">{errors.checkoutTime.message}</p>
                )}
              </div>

              {/* Pricing Section */}
              <div className="form-section-header">
                <h3>Pricing Information</h3>
              </div>

              <div className="form-field half-width">
                <label htmlFor="baseFare">Base Fare (per night)</label>
                <input
                  {...register("baseFare", {
                    required: "Please enter base fare.",
                    valueAsNumber: true,
                    min: { value: 0, message: "Base fare cannot be negative." }
                  })}
                  id="baseFare"
                  type="number"
                  step="0.01"
                  placeholder="Enter base fare..."
                />
                {errors.baseFare && (
                  <p className="field-error">{errors.baseFare.message}</p>
                )}
              </div>

              <div className="form-field half-width">
                <label htmlFor="taxesAndFees">Taxes & Fees</label>
                <input
                  {...register("taxesAndFees", {
                    required: "Please enter taxes and fees.",
                    valueAsNumber: true,
                    min: { value: 0, message: "Taxes and fees cannot be negative." }
                  })}
                  id="taxesAndFees"
                  type="number"
                  step="0.01"
                  placeholder="Enter taxes and fees..."
                />
                {errors.taxesAndFees && (
                  <p className="field-error">{errors.taxesAndFees.message}</p>
                )}
              </div>
            </div>

            <div className="form-submit-wrapper">
              <button className="submit-button" type="submit">
                {isEditMode ? "Update Hotel" : "Add Hotel"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default HotelPage;