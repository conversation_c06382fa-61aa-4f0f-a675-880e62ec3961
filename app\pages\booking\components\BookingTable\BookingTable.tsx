"use client";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import Link from "next/link";
import TableWithShimmer from "@/app/components/TableWithShimmer/TableWithShimmer";
import Pagination from "@/app/utilities/Pagination/Pagination";
import TableMenuTwo from "@/app/components/TableMenuTwo/TableMenuTwo";
import SearchBox from "@/app/components/SearchBox/SearchBox";
import "./BookingTable.scss";
import profilePic from "@/public/images/profile.png";

export interface DropdownItem {
  id: string;
  label: string;
}

export interface Booking {
  id: string;
  guestName: string;
  hotelName: string;
  roomType: string;
  checkIn: string;
  checkOut: string;
  guests: number;
  bookingDate: string;
  paymentStatus: string;
  bookingStatus: string;
  amountPaid: string;
  paymentMethod: string;
  is_active: boolean;
}

interface BookingsTableProps {
  bookings: Booking[];
  isLoading?: boolean;
  onShowDetails: (id: number) => void;
  onEditBooking: (id: number) => void;
  showAddButton?: boolean;
  onAddBooking?: () => void;
  className?: string;
  title?: string;
  pageName?: string;
  showFilter?: boolean;
}

function BookingsTable({
  bookings,
  isLoading = false,
  onShowDetails,
  onEditBooking,
  showAddButton = true,
  onAddBooking,
  className = "",
  title = "Bookings List",
  pageName,
  showFilter,
}: BookingsTableProps) {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [visible, setVisible] = useState<boolean>(false);

  // Filter popup
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);

  // Filters
  const [status, setStatus] = useState<string>("");
  const [selectedDate, setSelectedDate] = useState<string>("");
  const [searchValue, setSearchValue] = useState<string>("");

  // Toggle filter popup
  const handleToggleFilter = () => {
    setIsFilterOpen((prev) => !prev);
  };

  const handleFilterSelect = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
    item: string
  ) => {
    const value = event.target.value;
    if (item === "status") {
      setStatus(value);
    } else if (item === "date") {
      setSelectedDate(value);
    }
    setCurrentPage(1); // Reset to first page when filter changes
  };

  // Simulate visibility for animations
  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(true);
    }, 300);

    return () => clearTimeout(timer);
  }, []);

  // Update total pages when filters change
  useEffect(() => {
    const filteredBookings = getFilteredBookings();
    const calculatedTotalPages = Math.ceil(
      filteredBookings.length / itemsPerPage
    );
    setTotalPages(calculatedTotalPages > 0 ? calculatedTotalPages : 1);
  }, [status, selectedDate, searchValue, itemsPerPage, bookings]);

  const handlePageChange = (pageNo: number) => {
    setCurrentPage(pageNo);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
    setCurrentPage(1); // Reset to first page on new search
  };

  const handleMenuItemClick = (item: DropdownItem, id: number) => {
    if (item.id === "edit-booking") {
      onEditBooking(id);
    }
  };

  // Filter bookings based on search and filters
  const getFilteredBookings = () => {
    return bookings.filter((booking) => {
      // Filter by search value
      const matchesSearch =
        !searchValue ||
        booking.id.toLowerCase().includes(searchValue.toLowerCase()) ||
        booking.guestName.toLowerCase().includes(searchValue.toLowerCase());

      // Filter by status - Fixed to handle case insensitive comparison
      const matchesStatus =
        !status || booking.bookingStatus.toLowerCase() === status.toLowerCase();

      // Filter by date
      const matchesDate =
        !selectedDate ||
        booking.bookingDate === selectedDate ||
        booking.checkIn === selectedDate ||
        booking.checkOut === selectedDate;

      return matchesSearch && matchesStatus && matchesDate;
    });
  };

  // Get current bookings for pagination
  const getCurrentBookings = () => {
    const filtered = getFilteredBookings();
    const startIndex = (currentPage - 1) * itemsPerPage;
    return filtered.slice(startIndex, startIndex + itemsPerPage);
  };

  const menuItems = [{ id: "edit-booking", label: "Edit Booking" }];

  const displayedBookings = getCurrentBookings();

  return (
    <div className={`${className} ${visible ? "visible" : ""}`}>
      <div className="user-list-table-container">
        <div className="tableBorder">
          <div className="table-header">
            <h5>{title}</h5>
            {showFilter && (
              <div className="filter-search-container">
                <div className="filterButton" onClick={handleToggleFilter}>
                  <button>
                    <i className="fa-solid fa-filter"></i>
                  </button>
                </div>

                <div
                  className={`filter-options-select-box ${
                    isFilterOpen ? "show" : ""
                  }`}
                >
                  <div className="filterOption">
                    <span>Status {status ? `(${status})` : ""}</span>
                    <select
                      className="dropdown"
                      value={status}
                      onChange={(e) => handleFilterSelect(e, "status")}
                    >
                      <option value="">All</option>
                      <option value="confirmed">Confirmed</option>
                      <option value="awaiting payment">Awaiting Payment</option>
                      <option value="cancelled">Cancelled</option>
                    </select>
                    <span className="material-icons">keyboard_arrow_down</span>
                  </div>
                  <input
                    className="date-picker"
                    type="date"
                    value={selectedDate}
                    onChange={(e) => handleFilterSelect(e, "date")}
                    placeholder="Filter by date"
                  />
                </div>

                <SearchBox
                  value={searchValue}
                  onChange={handleSearchChange}
                  placeholders={["Search by ID or name"]}
                />
              </div>
            )}
          </div>

          {isLoading ? (
            <TableWithShimmer
              no_of_rows={itemsPerPage < 8 ? itemsPerPage : 8}
              no_of_cols={7}
              colWidths={[1.5, 1.5, 1.5, 1, 1, 1, 1]}
            />
          ) : (
            <div className="table-style table-vertical-scroll">
              <table>
                <thead>
                  <tr>
                    {pageName === "booking-page" && (
                      <>
                        
                        <th>Booking ID</th>
                        <th>Guest Name</th>
                        <th>Payment Status</th>
                        <th>Booking Status</th>
                        <th>Amount Paid</th>
                        <th>Payment Method</th>
                        <th>Details</th>
                        <th>Actions</th>
                      
                      </>
                    )}

                    {pageName === "home-page" && (
                      <>
                        
                        <th>Booking ID</th>
                        <th>Guest Name</th>
                        <th>Payment Status</th>
                        <th>Booking Status</th>
                       
                      </>
                    )}
                  </tr>
                </thead>

                <tbody>
                  {displayedBookings.map((booking, index) => {
                    const originalIndex = bookings.findIndex(
                      (b) => b.id === booking.id
                    );
                    return (
                      <tr
                        key={booking.id}
                        onClick={() => onShowDetails(originalIndex)}
                      >
                

                        {pageName === "booking-page" && (
                          <>
                                  <td>
                          <span className="empId">{booking.id}</span>
                        </td>
                        <td>
                          <div className="name-profilePic">
                            <div className="profile-pic">
                              <Image
                                src={profilePic}
                                alt="Guest Picture"
                                fill
                                className="profileImage"
                                style={{ objectFit: "cover" }}
                                sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                                loading="lazy"
                              />
                            </div>
                            <div className="name">{booking.guestName}</div>
                          </div>
                        </td>
                        <td>
                          <div
                            className={`status-tag ${
                              booking.paymentStatus.toLowerCase() === "paid"
                                ? "available"
                                : "offline"
                            }`}
                          >
                            <span>{booking.paymentStatus}</span>
                          </div>
                        </td>
                        <td>
                          <span className="contact">
                            {booking.bookingStatus}
                          </span>
                        </td>
                          
                            <td>
                              <span className="walletAmount">
                                {booking.amountPaid}
                              </span>
                            </td>
                            <td>
                              <span>{booking.paymentMethod}</span>
                            </td>
                             <td>
                              <div className="viewDetails">
                                <Link
                                  href="#"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    onShowDetails(originalIndex);
                                  }}
                                >
                                  View Details
                                </Link>
                              </div>
                            </td>
                              <td onClick={(e) => e.stopPropagation()}>
                              <TableMenuTwo
                                items={menuItems}
                                onClick={(item, id) =>
                                  handleMenuItemClick(item, id)
                                }
                                id={originalIndex}
                              />
                            </td>
                          </>
                        ) }

                         {pageName === "home-page" && (
                          <>
                                  <td>
                          <span className="empId">{booking.id}</span>
                        </td>
                        <td>
                          <div className="name-profilePic">
                            <div className="profile-pic">
                              <Image
                                src={profilePic}
                                alt="Guest Picture"
                                fill
                                className="profileImage"
                                style={{ objectFit: "cover" }}
                                sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                                loading="lazy"
                              />
                            </div>
                            <div className="name">{booking.guestName}</div>
                          </div>
                        </td>
                        <td>
                          <div
                            className={`status-tag ${
                              booking.paymentStatus.toLowerCase() === "paid"
                                ? "available"
                                : "offline"
                            }`}
                          >
                            <span>{booking.paymentStatus}</span>
                          </div>
                        </td>
                        <td>
                          <span className="contact">
                            {booking.bookingStatus}
                          </span>
                        </td>
                           
                       
                          </>
                        ) }
                      </tr>
                    );
                  })}

                  {displayedBookings.length === 0 && (
                    <tr>
                      <td colSpan={8} className="no-data2">
                        <h5>
                          {!searchValue &&
                            !status &&
                            !selectedDate &&
                            `It looks like you don't have any bookings yet.`}
                          {(searchValue || status || selectedDate) &&
                            `No bookings match your search criteria.`}
                        </h5>
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          )}

          <Pagination
            totalPages={totalPages}
            handlePage={handlePageChange}
            itemsPerPage={itemsPerPage}
            page={currentPage}
            handleItemsPerPageChange={handleItemsPerPageChange}
          />
        </div>
      </div>
    </div>
  );
}

export default BookingsTable;
