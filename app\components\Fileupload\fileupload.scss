@use "/styles/variables" as *;

.fileUpload {
  background-color: $white_color1;
  border: 2px dotted $black_color4;
  border-radius: 6px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  padding: 1px 0 12px 0;
  margin-bottom: 30px;

  .uploadIcon {
    color: $black_color2;
    margin-bottom: 6px;

    span {
      font-size: 28px;
      cursor: pointer;
    }
  }

  .desc {
    font-size: 9px;
    color: $black_color;
    font-weight: 600;
    padding-bottom: 3px;

    span {
      color: $primary_color;
      text-decoration: underline;
      cursor: pointer;

      &:hover {
        color: darken($primary_color, 10%);
      }
    }
  }

  .fileFormat {
    font-size: 8px;
    color: $black_color3;
    font-weight: 600;
  }

  .filePreview {
    display: flex;
    justify-content: center;
    flex-direction: column;
    align-items: center;
    padding: 10px;

    img {
      max-width: 100%;
      height: 150px;
    }
    .replaceButton {
      background-color: $primary_color;
      color: $white_color1;
      font-size: 8px;
      font-weight: 600;
      padding: 2px 8px;
      border-radius: 3px;
      border: 0;
      cursor: pointer;
      transition: background-color 0.3s ease;

      &:hover {
        background-color: darken($primary_color, 10%);
      }

      
    }

    .filePreviewName{
      display: flex;
      justify-content: center;
      align-items: center;
      padding: 0 5px;
      margin-top: 5px;
      font-size: 12px;
      text-align: center;
    }
  }
}
