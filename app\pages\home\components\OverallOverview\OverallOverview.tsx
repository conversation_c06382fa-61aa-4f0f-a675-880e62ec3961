

"use client";
import React, { useEffect, useState } from "react";
import "./OverallOverview.scss";

// Define a specific type for the header data
interface HeaderData {
  totalBookings: number;
  totalRevenue: number;
  activeHotels: number;
  activeProviders: number;
}

interface OverallOverviewProps {
  handleHeaderReceived: (headerData: HeaderData) => void;
  isHeaderReceived: boolean;
}

const OverallOverview: React.FC<OverallOverviewProps> = ({
  handleHeaderReceived,
  
}) => {
  const [visible, setVisible] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Simulate data loading with a timeout
    const timer = setTimeout(() => {
      try {
        setVisible(true);
        
        // Mock header data to pass to parent component
        const headerData: HeaderData = {
          totalBookings: 40,
          totalRevenue: 25000,
          activeHotels: 15,
          activeProviders: 7
        };
        
        // Notify parent component that header data is ready
        handleHeaderReceived(headerData);
      } catch (err) {
        // Error handling
        setError("Failed to load overview data");
        console.error("Error loading overview data:", err);
      }
    }, 500);
    
    // Clean up timeout to prevent memory leaks
    return () => clearTimeout(timer);
  }, [handleHeaderReceived]);
  
  // If there's an error, display it
  if (error) {
    return <div className="error-message" role="alert">{error}</div>;
  }
  
  return (
    <div className="overall-overview-container" role="region" aria-label="Dashboard Overview">
      <div 
        className={`overall-overview overview1 ${visible ? 'visible' : ''}`}
        role="article"
        aria-label="Total Bookings"
      >
        <div className="header">
          <h3>
            <span className="material-icons groups" aria-hidden="true">groups</span>
            Total Bookings
          </h3>
        </div>
        <div className="content">
          <h2>40</h2>
        </div>
      </div>
      
      <div 
        className={`overall-overview overview2 ${visible ? 'visible' : ''}`}
        role="article"
        aria-label="Total Revenue"
      >
        <div className="header">
          <h3>
            <span className="material-icons person" aria-hidden="true">person</span>
            Total Revenue
          </h3>
        </div>
        <div className="content">
          <h2>$25,000</h2>
        </div>
      </div>
      
      <div 
        className={`overall-overview overview3 ${visible ? 'visible' : ''}`}
        role="article"
        aria-label="Active Hotels"
      >
        <div className="header">
          <h3>
            <span className="material-icons group" aria-hidden="true">group</span>
            Active Hotels
          </h3>
        </div>
        <div className="content">
          <h2>15</h2>
        </div>
      </div>
      
      <div 
        className={`overall-overview overview4 ${visible ? 'visible' : ''}`}
        role="article"
        aria-label="Active Providers"
      >
        <div className="header">
          <h3>
            <span className="material-icons group2" aria-hidden="true">local_activity</span>
            Active Providers
          </h3>
        </div>
        <div className="content">
          <h2>7</h2>
        </div>
      </div>
    </div>
  );
};

export default OverallOverview;