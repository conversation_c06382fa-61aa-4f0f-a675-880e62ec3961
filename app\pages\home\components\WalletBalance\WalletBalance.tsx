// WalletBalance Component (components/WalletBalance/WalletBalance.tsx)
import React from 'react';

const WalletBalance: React.FC = () => {
  return (
    <div className="table-card">
      <h2 className="table-title">Wallet Balance</h2>
      <div className="table-container">
        <table className="data-table">
          <thead>
            <tr>
              <th>Name</th>
              <th>Type</th>
              <th>Holdings ($ / %)</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>
                <div className="wallet-cell">
                  <div className="wallet-icon">
                    <i className="fas fa-wallet"></i>
                  </div>
                  <div className="wallet-name">Hotel ABC</div>
                </div>
              </td>
              <td>
                <span className="type-badge corporate">Corporate</span>
              </td>
              <td>$12,500 / 50%</td>
              <td className="status-active">Active</td>
            </tr>
            <tr>
              <td>
                <div className="wallet-cell">
                  <div className="wallet-icon">
                    <i className="fas fa-wallet"></i>
                  </div>
                  <div className="wallet-name">Resort XYZ</div>
                </div>
              </td>
              <td>
                <span className="type-badge premium">Premium</span>
              </td>
              <td>$12,500 / 50%</td>
              <td className="status-active">Active</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default WalletBalance;