import { NextResponse } from 'next/server';
import { writeFile } from 'fs/promises';
import path from 'path';

export async function POST(request: Request) {
  try {
    const formData = await request.formData();
    
    // Get form fields
    const hotelName = formData.get('hotel_name') as string;
    const hotelLocation = formData.get('hotel_location') as string;
    const hotelChain = formData.get('hotel_chain') as string;
    
    // Handle logo upload
    const logoFile = formData.get('hotel_logo') as File;
    let logoPath = '';
    if (logoFile) {
      const logoBytes = await logoFile.arrayBuffer();
      const logoBuffer = Buffer.from(logoBytes);
      const logoFileName = `${Date.now()}-${logoFile.name}`;
      logoPath = path.join(process.cwd(), 'public', 'uploads', 'logos', logoFileName);
      await writeFile(logoPath, logoBuffer);
    }

    // Handle multiple profile photos
    const profilePhotos = formData.getAll('profile_photos') as File[];
    const photoPaths: string[] = [];

    for (const photo of profilePhotos) {
      const photoBytes = await photo.arrayBuffer();
      const photoBuffer = Buffer.from(photoBytes);
      const photoFileName = `${Date.now()}-${photo.name}`;
      const photoPath = path.join(process.cwd(), 'public', 'uploads', 'photos', photoFileName);
      await writeFile(photoPath, photoBuffer);
      photoPaths.push(photoPath);
    }

    // Here you would typically save the data to your database
    // This is a placeholder for your database logic
    const hotelData = {
      name: hotelName,
      location: hotelLocation,
      chain: hotelChain,
      logo: logoPath,
      photos: photoPaths,
    };

    // TODO: Add your database save logic here
    // const savedHotel = await prisma.hotel.create({ data: hotelData });

    return NextResponse.json({
      success: true,
      message: 'Hotel created successfully',
      data: hotelData
    });

  } catch (error) {
    console.error('Error creating hotel:', error);
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : 'Failed to create hotel'
    }, { status: 500 });
  }
}