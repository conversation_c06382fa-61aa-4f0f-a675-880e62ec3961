import React, { useState } from "react";
import "./HotelProviderCreate.scss";
import { use<PERSON><PERSON>, SubmitH<PERSON><PERSON>, Controller } from "react-hook-form";
import { Autocomplete, TextField } from "@mui/material";
import { useCommonContext } from "@/app/context/commonContext";
import { useAlert } from "@/app/utilities/Alert/Alert";
import FileUpload from "@/app/components/Fileupload/FileUpload";

interface HotelProviderCreateProps {
  showCreate: boolean;
  handleCloseCreate: VoidFunction;
  id?: number;
}

interface OptionType {
  id: number;
  name: string;
}

interface CreateHotelProvider {
  name: string;
  provider_code: string;
  username: string;
  country: string;
  code_status: number;
  logo: File | null;
}

// Status options for the CodeStatus field
const statusOptions = [
  { id: 1, name: "Active" },
  { id: 2, name: "Inactive" },
  { id: 3, name: "Pending" }
];

// Country options
const countryOptions = [
  { id: 1, name: "United States" },
  { id: 2, name: "United Kingdom" },
  { id: 3, name: "Canada" },
  { id: 4, name: "Australia" },
  { id: 5, name: "Germany" },
  { id: 6, name: "France" },
  { id: 7, name: "Japan" },
  { id: 8, name: "India" },
  // Add more countries as needed
];

function HotelProviderCreate({ showCreate, handleCloseCreate, id }: HotelProviderCreateProps) {
  const { fire } = useAlert();
  const MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB
  const ALLOWED_TYPES = ["image/jpeg", "image/png"];
  const [logoImage, setLogoImage] = useState<string | File | null>(null);
  const { setIsLoading } = useCommonContext();
  
  const {
    register,
    handleSubmit,
    setValue,
    setError,
    control,
    formState: { errors },
  } = useForm<CreateHotelProvider>();

  const onSubmit: SubmitHandler<CreateHotelProvider> = (data) => {
    //setIsLoading(true);
    if (!data.logo) {
      fire({
        position: "center",
        icon: "error",
        title: "An Error Occurred",
        text: "Please Add Logo Image",
        confirmButtonText: "Ok",
        onConfirm: () => {
          // console.log("Confirmed:");
        },
      });
      setError("logo", {
        type: "manual",
        message: "Please upload a Logo Image.",
      });
      setIsLoading(false);
      return;
    }
    
    // Process the submission here
    console.log("Form data submitted:", data);
  };

  const onFileUpload = (file: File | null) => {
    setLogoImage(file);
    setValue("logo", file); // Update form value
  };

  return (
    <div className={`create-form-overlay ${showCreate ? "show" : ""}`}>
      <div className="create-form-container">
        <div className="create-form-header-div">
          <h3>{id ? "Edit" : "Add"} Hotel Provider</h3>
          <span
            className="material-icons closeIcon"
            onClick={handleCloseCreate}
          >
            close
          </span>
        </div>
        <div className="create-form-div scroll-bar-1">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="input-field-container">

              <div className="input-field wf-50">
                <label htmlFor="name">Hotel Provider Name</label>
                <input
                  id="name"
                  type="text"
                  placeholder="Enter hotel provider name..."
                  {...register("name", {
                    required: "Please provide the hotel provider's name.",
                  })}
                />
                <p className="error-message">{errors.name?.message}</p>
              </div>

              <div className="input-field wf-50">
                <label htmlFor="provider_code">Provider Code</label>
                <input
                  {...register("provider_code", {
                    required: "Please enter the 'Provider Code'.",
                    maxLength: {
                      value: 10,
                      message: "Provider Code cannot exceed 10 characters.",
                    },
                  })}
                  id="provider_code"
                  type="text"
                  placeholder="Enter code..."
                  maxLength={10}
                />
                <p className="error-message">{errors.provider_code?.message}</p>
              </div>

              <div className="input-field wf-50">
                <label htmlFor="username">Username</label>
                <input
                  {...register("username", {
                    required: "Please enter a username.",
                  })}
                  id="username"
                  type="text"
                  placeholder="Enter username..."
                />
                <p className="error-message">{errors.username?.message}</p>
              </div>

              <div className="select-input-field wf-50">
                <label htmlFor="country">Country</label>
                <Controller
                  name="country"
                  control={control}
                  rules={{ required: "Please select a Country." }}
                  render={({ field }) => (
                    <Autocomplete
                      {...field}
                      id="country"
                      options={countryOptions}
                      getOptionLabel={(option) =>
                        typeof option === "object" ? option.name : ""
                      }
                      value={null}
                      onChange={(_, value) => {
                        setValue("country", value?.name || "");
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Select Country"
                          error={!!errors?.country}
                          variant="outlined"
                          InputProps={{
                            ...params.InputProps,
                            style: {
                              padding: "2px",
                              borderRadius: "6px",
                              backgroundColor: "#ffffff",
                            },
                          }}
                          sx={{
                            "& .MuiInputBase-input::placeholder": {
                              opacity: 1,
                            },
                            "& .MuiOutlinedInput-root": {
                              "& fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&:hover fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                              "& input": {
                                padding: "10px",
                              },
                              "&.Mui-error fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused.Mui-error fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                            },
                          }}
                        />
                      )}
                    />
                  )}
                />
                <p className="error-message">{errors.country?.message}</p>
              </div>

              <div className="select-input-field wf-50">
                <label htmlFor="code_status">Code Status</label>
                <Controller
                  name="code_status"
                  control={control}
                  rules={{ required: "Please select a Status." }}
                  render={({ field }) => (
                    <Autocomplete
                      {...field}
                      id="code_status"
                      options={statusOptions}
                      getOptionLabel={(option) =>
                        typeof option === "object" ? option.name : ""
                      }
                      value={null}
                      onChange={(_, value) => {
                        setValue("code_status", value?.id || 0);
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Select Status"
                          error={!!errors?.code_status}
                          variant="outlined"
                          InputProps={{
                            ...params.InputProps,
                            style: {
                              padding: "2px",
                              borderRadius: "6px",
                              backgroundColor: "#ffffff",
                            },
                          }}
                          sx={{
                            "& .MuiInputBase-input::placeholder": {
                              opacity: 1,
                            },
                            "& .MuiOutlinedInput-root": {
                              "& fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&:hover fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                              "& input": {
                                padding: "10px",
                              },
                              "&.Mui-error fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused.Mui-error fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                            },
                          }}
                        />
                      )}
                    />
                  )}
                />
                <p className="error-message">{errors.code_status?.message}</p>
              </div>

              <div className="wf-100">
                <FileUpload
                  uploadedFile={logoImage}
                  isOpen={showCreate}
                  allowedTypes={ALLOWED_TYPES}
                  maxSize={MAX_FILE_SIZE}
                  onFileUpload={onFileUpload}
                  label="Logo Image"
                  requiredMessage="Please upload a Logo Image."
                  maxFileSizeMessage="Logo image size is too large."
                  invalidTypeMessage="Invalid Logo image type."
                />
                {errors.logo && (
                  <p className="error-message">
                    {errors.logo.message}
                  </p>
                )}
              </div>
            </div>
            <div className="SubmitBtn">
              <button className="submitButton">Submit</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default HotelProviderCreate;