// import React, { useState, useEffect } from "react";
// import { FileRejection, useDropzone } from "react-dropzone";
// import "./fileupload.scss";
// import Image from "next/image";

// interface FileUploadProps {
//   uploadedFile: string | File | null;
//   isOpen: boolean;
//   allowedTypes: string[];
//   maxSize: number;
//   onFileUpload: (file: File | null) => void;
//   label: string;
//   requiredMessage: string;
//   maxFileSizeMessage: string;
//   invalidTypeMessage: string;
// }

// const FileUpload: React.FC<FileUploadProps> = ({
//   uploadedFile,
//   isOpen,
//   allowedTypes,
//   maxSize,
//   onFileUpload,
//   label,
//   maxFileSizeMessage,
//   invalidTypeMessage,
// }) => {
//   const [error, setError] = useState<string | null>(null);
//   const [preview, setPreview] = useState<string | File | null>(null);
//   const media_base_url = process.env.NEXT_PUBLIC_MEDIA_PATH;

//   const onDrop = (acceptedFiles: File[], fileRejections: FileRejection[]) => {
//     if (fileRejections.length) {
//       const rejectionErrors = fileRejections
//         .map(({ file, errors }) => {
//           return errors
//             .map((error) => {
//               if (error.code === "file-too-large") {
//                 return `${maxFileSizeMessage}: ${file.name}`;
//               }
//               if (error.code === "file-invalid-type") {
//                 return `${invalidTypeMessage}: ${file.name}`;
//               }
//               return null;
//             })
//             .filter(Boolean)
//             .join(", ");
//         })
//         .join(", ");
//       setError(rejectionErrors);
//       onFileUpload(null);
//       return;
//     }

//     const errors = acceptedFiles.map((file) => {
//       if (!allowedTypes.includes(file.type)) {
//         return `${invalidTypeMessage}: ${file.name}`;
//       }
//       if (file.size > maxSize) {
//         return `${maxFileSizeMessage}: ${file.name}`;
//       }
//       return null;
//     }).filter(Boolean);

//     if (errors.length) {
//       setError(errors.join(", "));
//       onFileUpload(null);
//       return;
//     }

//     setError(null);
//     onFileUpload(acceptedFiles[0]);
//   };

//   const { getRootProps, getInputProps, open } = useDropzone({
//     onDrop,
//     accept: allowedTypes.reduce((acc, type) => {
//       acc[type] = []; // Populate each type as an object key with an empty array
//       return acc;
//     }, {} as Record<string, string[]>),
//     maxSize,
//     multiple: false,
//     noClick: true,
//     noKeyboard: true,
//   });

//   useEffect(() => {
//     if (!isOpen) {
//       onFileUpload(null); // Reset when modal is closed
//     }
//   }, [isOpen, onFileUpload]);

//   useEffect(() => {
//     if (uploadedFile) {
//       console.log(uploadedFile, "changed");

//       if (typeof uploadedFile === "string") {
//         // If the uploadedFile is a string (URL), ensure the base URL is attached correctly
//         const imageURL = uploadedFile.startsWith("http")
//           ? uploadedFile
//           : media_base_url + uploadedFile;
//         setPreview(imageURL);
//       } else if (uploadedFile instanceof File) {
//         if (uploadedFile.type.startsWith("image/")) {
//           // For File type, check if it's an image and create a URL for preview
//           setPreview(uploadedFile);
//         } else {
//           setPreview(null); // If not an image, clear preview
//         }
//       }
//     } else {
//       setPreview(null); // Reset preview when file is cleared
//     }
//   }, [uploadedFile,media_base_url]);

//   return (
//     <div className="input-field w-100">
//       <label htmlFor="file">{label}</label>
//       <div id="file" {...getRootProps()} className="fileUpload w-100">
//         <input {...getInputProps()} />
//         {!uploadedFile ? (
//           <>
//             <div className="uploadIcon" onClick={open}>
//               <span className="material-icons">upload</span>
//             </div>
//             <div className="desc">
//               Drag & Drop or <span onClick={open}>Choose File</span> to upload
//             </div>
//             <div className="fileFormat">
//               JPG, or PNG (Max: {maxSize / 1024 / 1024}MB)
//             </div>
//           </>
//         ) : (
//           <div className="filePreview">

//             {/* {preview && typeof preview === "string" && preview.startsWith("http") &&  (
//               // Ensure the preview is a URL and is an image
//               <Image src={preview} alt="Preview" className="filePreviewImage" />
//             )}
//             {preview && preview instanceof File && preview.type.startsWith("image/") && (
//               // If it's a file, create a preview URL
//               <Image src={URL.createObjectURL(preview)} alt="Preview" className="filePreviewImage" />
//             )} */}

// {preview && typeof preview === "string" && preview.startsWith("http") && (
//   <Image 
//     src={preview} 
//     alt="Preview" 
//     className="filePreviewImage" 
//     width={150} 
//     height={150} 
//   />
// )}
// {preview && preview instanceof File && preview.type.startsWith("image/") && (
//   <Image 
//     src={URL.createObjectURL(preview)} 
//     alt="Preview" 
//     className="filePreviewImage" 
//     width={150} 
//     height={150} 
//   />
// )}



//             <p className="filePreviewName">{(uploadedFile instanceof File) ? uploadedFile.name : String(uploadedFile)}</p>
//             <div className="fileActions">
//               <button type="button" className="replaceButton" onClick={open}>
//                 <i className="fa-solid fa-rotate"></i> Change File
//               </button>
//             </div>
//           </div>
//         )}
//       </div>
//       {error && <p className="error-message">{error}</p>}
//     </div>
//   );
// };

// export default FileUpload;


import React, { useState, useEffect } from "react";
import { FileRejection, useDropzone } from "react-dropzone";
import "./fileupload.scss";
import Image from "next/image";

interface FileUploadProps {
  uploadedFile: string | File | null;
  isOpen: boolean;
  allowedTypes?: string[]; // Make it optional
  maxSize: number;
  onFileUpload: (file: File | null) => void;
  label: string;
  requiredMessage?: string; // Make it optional as it's not used in the component
  maxFileSizeMessage: string;
  invalidTypeMessage: string;
}

const FileUpload: React.FC<FileUploadProps> = ({
  uploadedFile,
  isOpen,
  allowedTypes = [], // Provide default empty array
  maxSize,
  onFileUpload,
  label,
  maxFileSizeMessage,
  invalidTypeMessage,
}) => {
  const [error, setError] = useState<string | null>(null);
  const [preview, setPreview] = useState<string | File | null>(null);
  const media_base_url = process.env.NEXT_PUBLIC_MEDIA_PATH || '';

  const onDrop = (acceptedFiles: File[], fileRejections: FileRejection[]) => {
    if (fileRejections.length) {
      const rejectionErrors = fileRejections
        .map(({ file, errors }) => {
          return errors
            .map((error) => {
              if (error.code === "file-too-large") {
                return `${maxFileSizeMessage}: ${file.name}`;
              }
              if (error.code === "file-invalid-type") {
                return `${invalidTypeMessage}: ${file.name}`;
              }
              return null;
            })
            .filter(Boolean)
            .join(", ");
        })
        .join(", ");
      setError(rejectionErrors);
      onFileUpload(null);
      return;
    }

    const errors = acceptedFiles.map((file) => {
      if (allowedTypes.length > 0 && !allowedTypes.includes(file.type)) {
        return `${invalidTypeMessage}: ${file.name}`;
      }
      if (file.size > maxSize) {
        return `${maxFileSizeMessage}: ${file.name}`;
      }
      return null;
    }).filter(Boolean);

    if (errors.length) {
      setError(errors.join(", "));
      onFileUpload(null);
      return;
    }

    setError(null);
    onFileUpload(acceptedFiles[0]);
  };

  // Create accept object for react-dropzone
  const acceptObject: Record<string, string[]> = {};
  if (allowedTypes && allowedTypes.length > 0) {
    allowedTypes.forEach(type => {
      acceptObject[type] = [];
    });
  }

  const { getRootProps, getInputProps, open } = useDropzone({
    onDrop,
    accept: acceptObject,
    maxSize,
    multiple: false,
    noClick: true,
    noKeyboard: true,
  });

  useEffect(() => {
    if (!isOpen) {
      onFileUpload(null); // Reset when modal is closed
    }
  }, [isOpen, onFileUpload]);

  useEffect(() => {
    if (uploadedFile) {
      if (typeof uploadedFile === "string") {
        // If the uploadedFile is a string (URL), ensure the base URL is attached correctly
        const imageURL = uploadedFile.startsWith("http")
          ? uploadedFile
          : media_base_url + uploadedFile;
        setPreview(imageURL);
      } else if (uploadedFile instanceof File) {
        if (uploadedFile.type.startsWith("image/")) {
          // For File type, check if it's an image and create a URL for preview
          setPreview(uploadedFile);
        } else {
          setPreview(null); // If not an image, clear preview
        }
      }
    } else {
      setPreview(null); // Reset preview when file is cleared
    }
  }, [uploadedFile, media_base_url]);

  return (
    <div className="input-field w-100">
      <label htmlFor="file">{label}</label>
      <div id="file" {...getRootProps()} className="fileUpload w-100">
        <input {...getInputProps()} />
        {!uploadedFile ? (
          <>
            <div className="uploadIcon" onClick={open}>
              <span className="material-icons">upload</span>
            </div>
            <div className="desc">
              Drag & Drop or <span onClick={open}>Choose File</span> to upload
            </div>
            <div className="fileFormat">
              JPG, or PNG (Max: {maxSize / 1024 / 1024}MB)
            </div>
          </>
        ) : (
          <div className="filePreview">
            {preview && typeof preview === "string" && preview.startsWith("http") && (
              <Image 
                src={preview} 
                alt="Preview" 
                className="filePreviewImage" 
                width={150} 
                height={150} 
              />
            )}
            {preview && preview instanceof File && preview.type.startsWith("image/") && (
              <Image 
                src={URL.createObjectURL(preview)} 
                alt="Preview" 
                className="filePreviewImage" 
                width={150} 
                height={150} 
              />
            )}

            <p className="filePreviewName">{(uploadedFile instanceof File) ? uploadedFile.name : String(uploadedFile)}</p>
            <div className="fileActions">
              <button type="button" className="replaceButton" onClick={open}>
                <i className="fa-solid fa-rotate"></i> Change File
              </button>
            </div>
          </div>
        )}
      </div>
      {error && <p className="error-message">{error}</p>}
    </div>
  );
};

export default FileUpload;