// @use '/styles/variables' as *;

// // Core Variables
// $color-blue: #2563eb;      // Deeper blue
// $color-green: #059669;     // Richer green
// $color-purple: #7c3aed;    // Vibrant purple
// $color-orange: #ea580c;    // Warmer orange
// $color-red: #dc2626;       // Brighter red
// $color-yellow: #d97706;    // Deeper yellow

// $color-gray-50: #f8fafc;   // Slightly cooler gray
// $color-gray-100: #f1f5f9;
// $color-gray-200: #e2e8f0;
// $color-gray-300: #cbd5e1;
// $color-gray-400: #94a3b8;
// $color-gray-500: #64748b;
// $color-gray-600: #475569;
// $color-gray-700: #334155;
// $color-gray-800: #1e293b;
// $color-white: #ffffff;

// // Typography
// $font-family-base: -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
// $font-size-xs: 0.75rem;    // 12px
// $font-size-sm: 0.875rem;   // 14px
// $font-size-base: 1rem;     // 16px - Increased base font size
// $font-size-lg: 1.125rem;   // 18px
// $font-size-xl: 1.25rem;    // 20px
// $font-size-2xl: 1.5rem;    // 24px - Increased for better headings

// // Spacing - Increased for better visual separation
// $spacing-1: 0.25rem;  // 4px
// $spacing-2: 0.5rem;   // 8px
// $spacing-3: 1rem;     // 16px - Increased
// $spacing-4: 1.5rem;   // 24px - Increased
// $spacing-5: 2rem;     // 32px - Increased
// $spacing-6: 2.5rem;   // 40px - Increased
// $spacing-8: 3rem;     // 48px - Increased

// // Component Heights & Widths (standardized)
// $height-card: 140px;       // Increased height
// $height-section: 400px;    // Increased height
// $width-section: 100%;      // Ensure full width for all sections

// // Breakpoints
// $breakpoint-sm: 640px;
// $breakpoint-md: 768px;
// $breakpoint-lg: 1024px;
// $breakpoint-xl: 1280px;

// // Mixins
// @mixin rounded($size: $spacing-2) {  // Increased default border radius
//   border-radius: $size;
// }

// @mixin shadow($level: 1) {
//   @if $level == 1 {
//     box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08), 0 1px 2px rgba(0, 0, 0, 0.04);
//   } @else if $level == 2 {
//     box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.04);
//   }
// }

// @mixin flex-center {
//   display: flex;
//   align-items: center;
//   justify-content: center;
// }

// @mixin color-badge($bg-color, $text-color) {
//   background-color: rgba($bg-color, 0.15);  // Increased opacity
//   color: $text-color;
//   padding: $spacing-1 $spacing-3;  // Increased horizontal padding
//   border-radius: 9999px;
//   font-size: $font-size-xs;
//   font-weight: 600;  // Made badges bolder
// }

// // Reset styles
// * {
//   box-sizing: border-box;
//   margin: 0;
//   padding: 0;
// }

// html, body {
//   height: 100%;
//   width: 100%;
// }

// body {
//   font-family: $font-family-base;
//   line-height: 1.6;  // Increased line height
//   background-color: $color-gray-50;
//   font-size: $font-size-base;
//   color: $color-gray-700;  // Better default text color
// }

// a {
//   text-decoration: none;
//   color: inherit;
// }

// button {
//   background: none;
//   border: none;
//   cursor: pointer;
//   font-family: inherit;
//   font-size: inherit;
// }

// // Dashboard Component
// .dashboard {
//   width: 100%;
//   min-height: 100vh;
//   background-color: $color-gray-50;
//   display: flex;
//   flex-direction: column;
  
//   &__container {
//     width: 100%;
//     margin: 0 auto;
//     padding: $spacing-5;  // Increased padding
//     flex: 1;
//     display: flex;
//     flex-direction: column;
//     gap: $spacing-6;  // Increased gap between major sections
//   }
  
//   &__header {
//     margin-bottom: $spacing-5;  // Increased margin
//     width: $width-section;
//   }
  
//   &__title {
//     font-size: $font-size-2xl;  // Larger title
//     font-weight: bold;
//     color: $color-gray-800;
//     border-bottom: 3px solid $color-blue;  // Thicker border
//     display: inline-block;
//     padding-bottom: $spacing-2;
//   }
  
//   // Stats Cards
//   &__stats-grid {
//     display: grid;
//     grid-template-columns: repeat(2, 1fr);
//     gap: $spacing-4;  // Increased gap
//     margin-bottom: $spacing-5;  // Increased margin
//     width: $width-section;
    
//     @media (min-width: $breakpoint-md) {
//       grid-template-columns: repeat(4, 1fr);
//     }
//   }
  
//   &__stat-card {
//     background-color: $color-white;
//     @include rounded($spacing-3);  // Larger border radius
//     @include shadow(1);
//     padding: $spacing-4;  // Increased padding
//     display: flex;
//     justify-content: space-between;
//     align-items: flex-start;
//     transition: transform 0.3s ease, box-shadow 0.3s ease;  // Smoother transition
//     height: $height-card;
//     width: 100%;
    
//     &:hover {
//       transform: translateY(-4px);  // More pronounced hover effect
//       @include shadow(2);
//     }
//   }
  
//   &__stat-info {
//     flex: 1;
//     height: 100%;
//     display: flex;
//     flex-direction: column;
//     justify-content: center;
//   }
  
//   &__stat-label {
//     font-size: $font-size-sm;  // Slightly larger
//     color: $color-gray-600;
//     margin-bottom: $spacing-2;  // Increased spacing
//     text-transform: uppercase;  // Added text transform
//     letter-spacing: 0.05em;  // Added letter spacing
//     font-weight: 500;  // Semibold
//   }
  
//   &__stat-value {
//     font-size: $font-size-2xl;  // Larger stat value
//     font-weight: bold;
//     color: $color-gray-800;
//     margin-bottom: $spacing-2;  // Increased spacing
//   }
  
//   &__stat-change {
//     font-size: $font-size-sm;  // Slightly larger
//     display: flex;
//     align-items: center;
//     font-weight: 500;  // Semibold
    
//     &--positive {
//       color: $color-green;
      
//       span {
//         &:first-child {
//           margin-right: $spacing-1;
//           font-weight: 600;  // Bolder
//         }
        
//         &:last-child {
//           color: $color-gray-500;
//         }
//       }
//     }
    
//     &--negative {
//       color: $color-red;
//     }
//   }
  
//   &__stat-icon {
//     width: 2.5rem;  // Larger icon
//     height: 2.5rem;
//     border-radius: 9999px;
//     @include flex-center;
//     color: $color-white;
    
//     &--blue {
//       background-color: $color-blue;
//     }
    
//     &--green {
//       background-color: $color-green;
//     }
    
//     &--purple {
//       background-color: $color-purple;
//     }
    
//     &--orange {
//       background-color: $color-orange;
//     }
    
//     &--red {
//       background-color: $color-red;
//     }
//   }
  
//   // Content Grid Layout
//   &__content-grid {
//     display: grid;
//     grid-template-columns: 1fr;
//     gap: $spacing-5;  // Increased gap
//     margin-bottom: $spacing-5;
//     width: $width-section;
    
//     @media (min-width: $breakpoint-lg) {
//       grid-template-columns: 3fr 2fr;
//     }
//   }
  
//   // Modified - Secondary Grid with equal width columns (50% each)
//   &__secondary-grid {
//     display: grid;
//     grid-template-columns: 1fr;
//     gap: $spacing-5;  // Increased gap
//     width: $width-section;
    
//     @media (min-width: $breakpoint-md) {
//       grid-template-columns: 1fr 1fr;
//     }
//   }
  
//   // Section Styling
//   &__section {
//     background-color: $color-white;
//     @include rounded($spacing-3);  // Larger border radius
//     @include shadow(1);
//     overflow: hidden;
//     height: $height-section;
//     width: 100%;
//     display: flex;
//     flex-direction: column;
//     border: 1px solid $color-gray-100;  // Added subtle border
//   }
  
//   // Hotel and Booking List Sections
//   &__hotel-list,
//   &__booking-list {
//     width: 100%;
//   }
  
//   // Table Styling (General)
//   &__table-header {
//     display: flex;
//     justify-content: space-between;
//     align-items: center;
//     padding: $spacing-4;  // Increased padding
//     border-bottom: 1px solid $color-gray-200;
//     width: 100%;
    
//     h2 {
//       font-size: $font-size-xl;  // Larger heading
//       font-weight: 600;  // Semibold
//       color: $color-gray-800;
//     }
//   }
  
//   &__table-actions {
//     display: flex;
//     gap: $spacing-3;  // Increased gap
//     align-items: center;
//   }
  
//   &__table-wrapper {
//     overflow-x: auto;
//     overflow-y: auto;
//     flex: 1;
//     width: 100%;
//   }
  
//   &__table {
//     width: 100%;
//     border-collapse: collapse;
//     table-layout: fixed;
    
//     th {
//       text-align: left;
//       font-size: $font-size-sm;  // Slightly larger
//       color: $color-gray-500;
//       padding: $spacing-3 $spacing-4;  // Increased padding
//       font-weight: 600;  // Semibold
//       border-bottom: 1px solid $color-gray-200;
//       white-space: nowrap;
//       height: 40px;  // Increased height
//       position: sticky;
//       top: 0;
//       background-color: $color-white;
//       z-index: 1;
//     }
    
//     td {
//       padding: $spacing-3 $spacing-4;  // Increased padding
//       font-size: $font-size-base;  // Slightly larger
//       border-bottom: 1px solid $color-gray-200;
//       vertical-align: middle;
//       height: 50px;  // Increased height
//     }
    
//     tr {
//       &:hover {
//         background-color: $color-gray-100;  // Slightly darker hover
//       }
      
//       &:last-child td {
//         border-bottom: none;
//       }
//     }
//   }
  
//   // Status Badges
//   &__status {
//     display: inline-block;
//     font-size: $font-size-xs;
//     padding: $spacing-1 $spacing-3;  // Increased horizontal padding
//     border-radius: 9999px;
//     font-weight: 600;  // Semibold
    
//     &--confirmed,
//     &--active {
//       @include color-badge($color-green, darken($color-green, 30%));
//     }
    
//     &--pending,
//     &--awaiting {
//       @include color-badge($color-yellow, darken($color-yellow, 30%));
//     }
    
//     &--cancelled,
//     &--inactive {
//       @include color-badge($color-red, darken($color-red, 30%));
//     }
//   }
  
//   // Entity Icons (Hotels, Providers)
//   &__entity-cell {
//     display: flex;
//     align-items: center;
//     gap: $spacing-3;  // Increased gap
//   }
  
//   &__entity-icon {
//     width: 2rem;  // Larger icon
//     height: 2rem;
//     min-width: 2rem;
//     border-radius: 9999px;
//     @include flex-center;
//     color: $color-white;
//     font-weight: 600;  // Semibold
//     font-size: $font-size-sm;  // Slightly larger
    
//     &--blue {
//       background-color: $color-blue;
//     }
    
//     &--green {
//       background-color: $color-green;
//     }
    
//     &--purple {
//       background-color: $color-purple;
//     }
    
//     &--orange {
//       background-color: $color-orange;
//     }
//   }
  
//   &__entity-info {
//     display: flex;
//     flex-direction: column;
    
//     .name {
//       font-weight: 600;  // Semibold
//       color: $color-gray-800;
//       line-height: 1.3;  // Increased line height
//     }
    
//     .meta {
//       font-size: $font-size-sm;  // Slightly larger
//       color: $color-gray-500;
//       margin-top: $spacing-1;  // Added spacing
//     }
//   }
  
//   // Action Links & Buttons
//   &__action-link {
//     color: $color-blue;
//     font-size: $font-size-sm;  // Slightly larger
//     transition: color 0.2s ease;
//     font-weight: 500;  // Semibold
    
//     &:hover {
//       color: darken($color-blue, 10%);
//       text-decoration: underline;
//     }
//   }
  
//   &__action-button {
//     padding: $spacing-2 $spacing-3;  // Increased padding
//     font-size: $font-size-sm;  // Slightly larger
//     border-radius: $spacing-1;
//     transition: all 0.2s ease;
//     font-weight: 500;  // Semibold
    
//     &--primary {
//       background-color: $color-blue;
//       color: $color-white;
      
//       &:hover {
//         background-color: darken($color-blue, 10%);
//         transform: translateY(-1px);  // Subtle lift effect
//       }
//     }
    
//     &--secondary {
//       background-color: $color-gray-100;
//       color: $color-gray-700;
      
//       &:hover {
//         background-color: $color-gray-200;
//         transform: translateY(-1px);  // Subtle lift effect
//       }
//     }
//   }
  
//   // Search and filter components
//   &__search-input {
//     padding: $spacing-2 $spacing-3;  // Increased padding
//     border: 1px solid $color-gray-300;
//     border-radius: $spacing-2;  // Larger radius
//     font-size: $font-size-base;  // Slightly larger
//     width: 100%;
//     max-width: 250px;  // Increased width
    
//     &:focus {
//       outline: none;
//       border-color: $color-blue;
//       box-shadow: 0 0 0 2px rgba($color-blue, 0.2);  // Added focus ring
//     }
//   }
  
//   &__filter-dropdown {
//     position: relative;
    
//     &-button {
//       display: flex;
//       align-items: center;
//       gap: $spacing-2;  // Increased gap
//       padding: $spacing-2 $spacing-3;  // Increased padding
//       border: 1px solid $color-gray-300;
//       border-radius: $spacing-2;  // Larger radius
//       font-size: $font-size-base;  // Slightly larger
//       background-color: $color-white;
      
//       &:hover {
//         background-color: $color-gray-100;
//       }
//     }
    
//     &-menu {
//       position: absolute;
//       top: calc(100% + 4px);  // Added gap
//       right: 0;
//       background-color: $color-white;
//       border: 1px solid $color-gray-200;
//       border-radius: $spacing-2;  // Larger radius
//       @include shadow(1);
//       z-index: 10;
//       min-width: 180px;  // Increased width
//       overflow: hidden;  // Hide overflow
      
//       &-item {
//         padding: $spacing-3 $spacing-4;  // Increased padding
//         font-size: $font-size-base;  // Slightly larger
//         cursor: pointer;
        
//         &:hover {
//           background-color: $color-gray-100;
//         }
//       }
//     }
//   }
  
//   // Pagination
//   &__pagination {
//     display: flex;
//     justify-content: space-between;
//     align-items: center;
//     padding: $spacing-3 $spacing-4;  // Increased padding
//     border-top: 1px solid $color-gray-200;
//     width: 100%;
    
//     &-info {
//       font-size: $font-size-sm;  // Slightly larger
//       color: $color-gray-600;
//     }
    
//     &-controls {
//       display: flex;
//       gap: $spacing-2;  // Increased gap
      
//       button {
//         width: 2rem;  // Larger buttons
//         height: 2rem;
//         @include flex-center;
//         border: 1px solid $color-gray-300;
//         border-radius: $spacing-1;
//         font-size: $font-size-sm;  // Slightly larger
        
//         &:hover:not(:disabled) {
//           background-color: $color-gray-100;
//           border-color: $color-gray-400;  // Darker border on hover
//         }
        
//         &:disabled {
//           opacity: 0.5;
//           cursor: not-allowed;
//         }
        
//         &.active {
//           background-color: $color-blue;
//           color: $color-white;
//           border-color: $color-blue;
//           font-weight: 600;  // Semibold
//         }
//       }
//     }
//   }
  
//   // Ensure responsive layout maintains equal width columns
//   @media (max-width: $breakpoint-md - 1) {
//     &__container {
//       padding: $spacing-4 $spacing-3;  // Adjusted for mobile
//     }
    
//     &__content-grid,
//     &__secondary-grid {
//       gap: $spacing-4;  // Adjusted for mobile
//     }
    
//     &__section {
//       height: auto;
//       min-height: $height-section;
//     }
//   }
// }

// .dashboard {
//   padding: 20px;
//   background-color: #f5f7fa;
//   min-height: 100vh;

//   &__container {
//     display: flex;
//     flex-direction: column;
//     gap: 20px;
//   }

//   &__charts-grid {
//     display: grid;
//     grid-template-columns: repeat(2, 1fr);
//     gap: 20px;
//     margin-bottom: 20px;
//   }

//   &__chart-container {
//     background-color: white;
//     border-radius: 8px;
//     box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
//     padding: 16px;
//   }

//   &__secondary-grid {
//     display: grid;
//     grid-template-columns: repeat(2, 1fr);
//     gap: 20px;
//     margin-bottom: 20px;

//     @media (max-width: 992px) {
//       grid-template-columns: 1fr;
//     }
//   }

//   &__section {
//     background-color: white;
//     border-radius: 8px;
//     box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
//     padding: 16px;

//     &--table {
//       overflow-x: auto;
//     }
//   }
// }




// Variables
$primary-color: #4f46e5; // Indigo-500
$success-color: #10b981; // Green-500
$warning-color: #f59e0b; // Yellow-500
$danger-color: #ef4444;  // Red-500
$info-color: #3b82f6;    // Blue-500
$purple-color: #8b5cf6;  // Purple-500
$orange-color: #f97316;  // Orange-500

$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

$border-radius: 0.5rem;
$box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
$spacing-unit: 1rem;

// Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin card {
  background-color: white;
  border-radius: $border-radius;
  box-shadow: $box-shadow;
  padding: $spacing-unit * 1.25;
}

@mixin status-badge($color, $bg-color) {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  border-radius: 9999px;
  background-color: $bg-color;
  color: $color;
}

@mixin icon-container($bg-color, $color) {
  @include flex-center;
  height: 2rem;
  width: 2rem;
  border-radius: 9999px;
  background-color: $bg-color;
  color: $color;
  flex-shrink: 0;
}

// Base styles
body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
    Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  color: $gray-800;
  line-height: 1.5;
}

// Dashboard container
.dashboard {
  background-color: $gray-50;
  min-height: 100vh;
  padding: $spacing-unit;
}

// Metric cards
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  gap: $spacing-unit;
  margin-bottom: $spacing-unit * 1.5;
  
  @media (min-width: 768px) {
    grid-template-columns: repeat(2, 1fr);
  }
  
  @media (min-width: 1024px) {
    grid-template-columns: repeat(4, 1fr);
  }
}

.metric-card {
  @include card;
  display: flex;
  flex-direction: column;
  
  .metric-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    
    .metric-icon {
      margin-right: 0.5rem;
      padding: 0.25rem;
      border-radius: 0.375rem;
      
      &.booking-icon {
        background-color: rgba($success-color, 0.1);
        color: $success-color;
      }
      
      &.revenue-icon {
        background-color: rgba($danger-color, 0.1);
        color: $danger-color;
      }
      
      &.hotel-icon {
        background-color: rgba($info-color, 0.1);
        color: $info-color;
      }
      
      &.provider-icon {
        background-color: rgba($purple-color, 0.1);
        color: $purple-color;
      }
    }
    
    .metric-label {
      font-size: 0.875rem;
      color: $gray-500;
    }
  }
  
  .metric-value {
    font-size: 1.875rem;
    font-weight: 600;
    color: $gray-800;
  }
}

// Charts
.charts-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-unit * 1.5;
  margin-bottom: $spacing-unit * 1.5;
  
  @media (min-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
  }
}

.chart-card {
  @include card;
  
  .chart-header {
    @include flex-between;
    margin-bottom: 1rem;
    
    .chart-title {
      font-size: 1.125rem;
      font-weight: 500;
      color: $gray-800;
    }
    
    .timeframe-selector {
      display: flex;
      background-color: $gray-100;
      border-radius: 0.375rem;
      overflow: hidden;
      
      .timeframe-btn {
        padding: 0.25rem 0.75rem;
        font-size: 0.75rem;
        border: none;
        background: none;
        cursor: pointer;
        color: $gray-600;
        
        &.active {
          background-color: $primary-color;
          color: white;
        }
        
        &:hover:not(.active) {
          background-color: $gray-200;
        }
      }
    }
  }
  
  .chart-container {
    height: 12rem;
    width: 100%;
    border-radius: $border-radius;
    overflow: hidden;
    position: relative;
    
    &.booking-chart {
      background: linear-gradient(to bottom, rgba($primary-color, 0.1), white);
    }
    
    &.revenue-chart {
      background: white;
    }
    
    .chart-placeholder {
      @include flex-center;
      position: absolute;
      inset: 0;
      
      .chart-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }
  }
}

// Tables
.tables-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: $spacing-unit * 1.5;
  margin-bottom: $spacing-unit * 1.5;
  
  @media (min-width: 1024px) {
    grid-template-columns: repeat(2, 1fr);
  }
}

.table-card {
  @include card;
  
  .table-title {
    font-size: 1.125rem;
    font-weight: 500;
    color: $gray-800;
    margin-bottom: 1rem;
  }
  
  .table-container {
    overflow-x: auto;
  }
  
  .data-table {
    min-width: 100%;
    
    th {
      padding: 0.75rem 1rem;
      text-align: left;
      font-size: 0.75rem;
      font-weight: 500;
      text-transform: uppercase;
      color: $gray-500;
      border-bottom: 1px solid $gray-200;
    }
    
    td {
      padding: 0.75rem 1rem;
      font-size: 0.875rem;
      color: $gray-500;
      white-space: nowrap;
    }
    
    tbody {
      tr {
        border-bottom: 1px solid $gray-200;
        
        &:hover {
          background-color: $gray-50;
        }
      }
    }
    
    .user-cell, .hotel-cell, .wallet-cell, .provider-cell {
      display: flex;
      align-items: center;
      
      .user-avatar, .hotel-icon, .wallet-icon, .provider-icon {
        @include icon-container(rgba($info-color, 0.1), $info-color);
        margin-right: 0.75rem;
      }
      
      .user-name, .hotel-name, .wallet-name, .provider-name {
        font-size: 0.875rem;
        font-weight: 500;
        color: $gray-900;
      }
    }
    
    // Status badges
    .status-badge {
      &.paid {
        @include status-badge(darken($success-color, 20%), rgba($success-color, 0.1));
      }
      
      &.pending {
        @include status-badge(darken($warning-color, 20%), rgba($warning-color, 0.1));
      }
      
      &.active {
        @include status-badge(darken($success-color, 20%), rgba($success-color, 0.1));
      }
    }
    
    // Type badges
    .type-badge {
      &.corporate {
        @include status-badge(darken($success-color, 20%), rgba($success-color, 0.1));
      }
      
      &.premium {
        @include status-badge(darken($info-color, 20%), rgba($info-color, 0.1));
      }
    }
    
    // Status text colors
    .status-confirmed {
      color: $orange-color;
    }
    
    .status-awaiting {
      color: $warning-color;
    }
    
    .status-active {
      color: $success-color;
    }
  }
}