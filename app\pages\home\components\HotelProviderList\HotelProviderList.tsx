// HotelProviderList Component (components/HotelProviderList/HotelProviderList.tsx)
import React from 'react';

const HotelProviderList: React.FC = () => {
  return (
    <div className="table-card">
      <h2 className="table-title">Hotel Provider List</h2>
      <div className="table-container">
        <table className="data-table">
          <thead>
            <tr>
              <th>Hotel Provider</th>
              <th>Coverage</th>
              <th>Country Code</th>
              <th>Status</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>
                <div className="provider-cell">
                  <div className="provider-icon">
                    <i className="fas fa-building"></i>
                  </div>
                  <div className="provider-name">Playa Hotels</div>
                </div>
              </td>
              <td>North America, global</td>
              <td>US</td>
              <td>
                <span className="status-badge active">Active</span>
              </td>
            </tr>
            <tr>
              <td>
                <div className="provider-cell">
                  <div className="provider-icon">
                    <i className="fas fa-building"></i>
                  </div>
                  <div className="provider-name">Marriott International</div>
                </div>
              </td>
              <td>International, global</td>
              <td>US</td>
              <td>
                <span className="status-badge active">Active</span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default HotelProviderList;