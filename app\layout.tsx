import type { Metada<PERSON> } from "next";
import "./globals.scss";
import { CommonProvider } from "./context/commonContext";
import { AlertProvider } from "./utilities/Alert/Alert";


export const metadata: Metadata = {
  title: "dashboard-skeleton",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
     <head>
     <meta name="theme-color" content="#000000" />
     <link rel="stylesheet" href="https://fonts.googleapis.com/icon?family=Material+Icons" />
     <link href="https://fonts.googleapis.com/icon?family=Material+Icons+Outlined" rel="stylesheet"></link>
     <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossOrigin="anonymous" referrerPolicy="no-referrer" />
     </head>
      <body id="__next">
        <CommonProvider>
          <AlertProvider>
            {children}
          </AlertProvider>
        </CommonProvider>

      </body>
    </html>
  );
}
