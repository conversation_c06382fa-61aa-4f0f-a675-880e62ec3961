"use client";
import React, { useEffect, useState, useMemo } from "react";
import "./masterDetails.scss";
import { useAlert } from "@/app/utilities/Alert/Alert";
import { useSearchParams, useRouter } from "next/navigation";
import {
  FiArrowRight, FiUser, FiCheckCircle, FiXCircle,
  FiTrash2, FiMapPin, FiHome, FiPhone, FiMail,
  FiWifi, FiCoffee, FiDollarSign, FiCalendar,
  FiStar, FiCheck, FiDroplet, FiClock, FiMap, FiInfo
} from "react-icons/fi";
import Image from "next/image";
import RoomTypesSection from "../hotelMaster/components/RoomTypesManagement/RoomTypesManagement";

// Car icon component
const FiCar = ({ size }: { size: number }) => (
  <svg xmlns="http://www.w3.org/2000/svg" width={size} height={size} viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M14 16H9m10 0h3v-3.15a1 1 0 0 0-.84-.99L16 11l-2.7-3.6a1 1 0 0 0-.8-.4H5.24a2 2 0 0 0-1.8 1.1l-.8 1.63A6 6 0 0 0 2 12.42V16h2"></path>
    <circle cx="6.5" cy="16.5" r="2.5"></circle>
    <circle cx="16.5" cy="16.5" r="2.5"></circle>
  </svg>
);

interface RoomType {
  id: string;
  name: string;
  description: string;
  capacity: number;
  pricePerNight: string;
  amenities: string[];
  availability: number;
  images?: File[];
  // Additional comprehensive fields
  roomSize: string;
  bedType: string;
  viewType: string;
  floorRange: string;
  smokingPolicy: 'smoking' | 'non-smoking' | 'both';
  bathroomType: string;
  balcony: boolean;
  airConditioning: boolean;
  wifi: boolean;
  maxOccupancy: number;
  extraBedAvailable: boolean;
  extraBedPrice: string;
  roomFeatures: string[];
  cancellationPolicy: string;
  checkInTime: string;
  checkOutTime: string;
}

interface TravelDates {
  checkinDate: Date;
  checkoutDate: Date;
  formattedCheckinDate: string;
  formattedCheckoutDate: string;
  checkinTime: string;
  checkoutTime: string;
}

interface TravelDetails {
  travelDates: TravelDates;
  roomCount: number;
  adultCount: number;
  childCount: number;
  childAges: number[];
}

interface LocationDetail {
  lat: number;
  lon: number;
}

interface Landmark {
  name: string;
  distance: number;
  unit: string;
}

interface HotelFeaturesRating {
  Cleanliness: string;
  "Value for Money": string;
  Staff: string;
  "Room Comfort": string;
  Location: string;
  Food: string;
}

interface RatingView {
  averageRating: string;
  ratingCount: number;
  hotelFeaturesRating: HotelFeaturesRating;
  totalReviewCount: number;
}

interface GuestImpressionDatum {
  icon: string;
  impression: string;
}

interface GuestImpression {
  title: string;
  data: GuestImpressionDatum[];
}

interface HouseRule {
  policyType: string;
  heading: string;
  subRules: {
    subHeading?: string;
    details: string[];
  }[];
}

interface HouseRulesAndPolicies {
  checkinTime: string;
  checkoutTime: string;
  rules: HouseRule[];
}

interface SpotlightInfo {
  data: {
    title: string;
    description: string;
    icon: string;
  }[];
}

interface FareDetail {
  displayedBaseFare: number;
  baseFare: number;
  totalPrice: number;
  taxesAndFees: number;
  totalDiscount: number;
  instantDiscount: number;
}

interface HotelData {
  id: number;
  name: string;
  location: string;
  address: string;
  chainName: string;
  status: string;
  contactPerson: string;
  contactEmail: string;
  contactPhone: string;
  starRating: number;
  establishedDate: string;
  totalRooms: number;
  availableRooms: number;
  description: string;
  amenities: string[];
  images: string[];
  roomTypes: RoomType[];
  logo?: string | null;

  // Enhanced properties from your interface
  locationId: number;
  hotelId: number;
  locality: string;
  city: string;
  userRating: string;
  userRatingCategory: string;
  userRatingCount: number;
  locationDetail: LocationDetail;
  travelDetails?: TravelDetails;
  landmarks: Landmark[];
  ratingView: RatingView;
  guestImpressions: GuestImpression[];
  houseRulesAndPolicies: HouseRulesAndPolicies;
  spotlightInfo: SpotlightInfo;
  accommodationType: string;
  fareDetail?: FareDetail;
}

function Page() {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [hotel, setHotel] = useState<HotelData | null>(null);
  const [activeTab, setActiveTab] = useState<string>('overview');
  const { fire } = useAlert();
  const searchParams = useSearchParams();
  const router = useRouter();

  const hotelId = searchParams.get('id') ? parseInt(searchParams.get('id') as string, 10) : null;

  // Enhanced sample hotel data
  const hotelData: HotelData[] = useMemo(() => [
    {
      id: 1,
      locationId: 12345,
      hotelId: 67890,
      name: "Hotel Grand Palace",
      locality: "Manhattan",
      city: "New York",
      location: "New York, USA",
      address: "123 Broadway Ave, New York, NY 10001",
      chainName: "Grand Hotels Chain",
      status: "Available",
      contactPerson: "John Smith",
      contactEmail: "<EMAIL>",
      contactPhone: "+****************",
      starRating: 5,
      userRating: "9.2",
      userRatingCategory: "Exceptional",
      userRatingCount: 1542,
      establishedDate: "2005-06-12",
      totalRooms: 120,
      availableRooms: 45,
      accommodationType: "Hotel",
      description: "A luxurious 5-star hotel located in the heart of Manhattan, offering premium accommodations with spectacular city views and unparalleled service.",
      amenities: ["Free WiFi", "Pool", "Spa", "Fitness Center", "Restaurant", "Bar", "Conference Rooms", "Parking", "24/7 Room Service"],
      images: ["/images/profile.png", "/images/profile.png", "/images/profile.png"],
      locationDetail: {
        lat: 40.7589,
        lon: -73.9851
      },
      landmarks: [
        { name: "Times Square", distance: 0.5, unit: "km" },
        { name: "Central Park", distance: 1.2, unit: "km" },
        { name: "Empire State Building", distance: 0.8, unit: "km" },
        { name: "Broadway Theater District", distance: 0.3, unit: "km" }
      ],
      ratingView: {
        averageRating: "9.2",
        ratingCount: 1542,
        totalReviewCount: 1542,
        hotelFeaturesRating: {
          Cleanliness: "9.5",
          "Value for Money": "8.8",
          Staff: "9.3",
          "Room Comfort": "9.1",
          Location: "9.7",
          Food: "8.9"
        }
      },
      guestImpressions: [
        {
          title: "What guests loved most",
          data: [
            { icon: "👑", impression: "Exceptional service" },
            { icon: "🏙️", impression: "Amazing city views" },
            { icon: "🛏️", impression: "Comfortable beds" },
            { icon: "📍", impression: "Perfect location" }
          ]
        }
      ],
      houseRulesAndPolicies: {
        checkinTime: "3:00 PM",
        checkoutTime: "11:00 AM",
        rules: [
          {
            policyType: "general",
            heading: "General Policies",
            subRules: [
              {
                subHeading: "Check-in/Check-out",
                details: ["Check-in: 3:00 PM", "Check-out: 11:00 AM", "Early check-in subject to availability"]
              },
              {
                subHeading: "Cancellation",
                details: ["Free cancellation up to 24 hours before arrival", "Late cancellation fee applies"]
              }
            ]
          },
          {
            policyType: "pets",
            heading: "Pet Policy",
            subRules: [
              {
                details: ["Pets allowed with prior approval", "Pet fee: $50 per night", "Maximum 2 pets per room"]
              }
            ]
          }
        ]
      },
      spotlightInfo: {
        data: [
          {
            title: "Prime Location",
            description: "Located in the heart of Manhattan with easy access to major attractions",
            icon: "📍"
          },
          {
            title: "Luxury Amenities",
            description: "Full-service spa, rooftop pool, and fine dining restaurant",
            icon: "✨"
          },
          {
            title: "Business Ready",
            description: "State-of-the-art conference facilities and business center",
            icon: "💼"
          }
        ]
      },
      fareDetail: {
        displayedBaseFare: 350,
        baseFare: 350,
        totalPrice: 420,
        taxesAndFees: 70,
        totalDiscount: 50,
        instantDiscount: 25
      },
      roomTypes: [
        {
          id: "RT101",
          name: "Deluxe King",
          description: "Spacious room with king-sized bed and city view",
          capacity: 2,
          pricePerNight: "$350",
          amenities: ["Free WiFi", "Minibar", "40-inch TV", "Safe"],
          availability: 15,
          images: [],
          roomSize: "45 sqm",
          bedType: "King Size",
          viewType: "City View",
          floorRange: "10-15",
          smokingPolicy: 'non-smoking' as 'smoking' | 'non-smoking' | 'both',
          bathroomType: "Private Bathroom",
          balcony: true,
          airConditioning: true,
          wifi: true,
          maxOccupancy: 2,
          extraBedAvailable: false,
          extraBedPrice: "",
          roomFeatures: ["Mini Bar", "Safe", "Coffee Machine"],
          cancellationPolicy: "Free cancellation up to 24 hours before check-in",
          checkInTime: "15:00",
          checkOutTime: "11:00"
        },
        {
          id: "RT102",
          name: "Executive Suite",
          description: "Luxury suite with separate living area and panoramic views",
          capacity: 3,
          pricePerNight: "$550",
          amenities: ["Free WiFi", "Minibar", "50-inch TV", "Safe", "Jacuzzi"],
          availability: 8,
          images: [],
          roomSize: "75 sqm",
          bedType: "King Size + Sofa Bed",
          viewType: "Ocean View",
          floorRange: "20-25",
          smokingPolicy: 'non-smoking' as 'smoking' | 'non-smoking' | 'both',
          bathroomType: "Ensuite Bathroom",
          balcony: true,
          airConditioning: true,
          wifi: true,
          maxOccupancy: 4,
          extraBedAvailable: true,
          extraBedPrice: "$75/night",
          roomFeatures: ["Jacuzzi", "Living Area", "Dining Table", "Premium Minibar"],
          cancellationPolicy: "Free cancellation up to 48 hours before check-in",
          checkInTime: "15:00",
          checkOutTime: "12:00"
        }
      ],
      logo: "/images/profile.png"
    }
  ], []);

  useEffect(() => {
    if (hotelId) {
      setIsLoading(true);
      const timer = setTimeout(() => {
        const foundHotel = hotelData.find(h => h.id === hotelId);
        if (foundHotel) {
          setHotel(foundHotel);
        } else {
          setHotel(null);
        }
        setIsLoading(false);
      }, 800);

      return () => clearTimeout(timer);
    } else {
      setIsLoading(false);
    }
  }, [hotelId, hotelData]);

  const handleToggleStatus = () => {
    if (!hotel) return;

    const action = hotel.status === "Available" ? "disable" : "enable";

    fire({
      icon: "info",
      title: `${action.charAt(0).toUpperCase() + action.slice(1)} Hotel`,
      text: `Are you sure you want to ${action} this hotel?`,
      confirmButtonText: `Yes, ${action}`,
      cancelButtonText: "No, Go Back",
      onConfirm: () => {
        setHotel({
          ...hotel,
          status: hotel.status === "Available" ? "Unavailable" : "Available"
        });
      }
    });
  };

  const handleDeleteHotel = () => {
    fire({
      icon: "error",
      title: "Delete Hotel",
      text: "Are you sure you want to permanently delete this hotel? This action cannot be undone.",
      confirmButtonText: "Yes, Delete",
      cancelButtonText: "No, Go Back",
      onConfirm: () => {
        router.push('/hotels');
      }
    });
  };

  const handleClose = () => {
    router.back();
  };

  if (!hotelId) {
    return (
      <div className="hotelDetailsPage">
        <div className="hotelDetailsContainer">
          <div className="hotelHeader">
            <h1>Hotel Details</h1>
            <button className="backButton" onClick={handleClose}>
              <FiArrowRight size={18} style={{ transform: 'rotate(180deg)' }} />
              Back
            </button>
          </div>
          <div className="noDataFound">
            <p>No hotel ID provided in URL.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="hotelDetailsPage">
      <div className="hotelDetailsContainer">
        <div className="hotelHeader">
          <h1>Hotel Details</h1>
          <button className="backButton" onClick={handleClose}>
            <FiArrowRight size={18} style={{ transform: 'rotate(180deg)' }} />
            Back
          </button>
        </div>

          {isLoading ? (
            <div className="hotelDetailsLoading">
              <div className="loader"></div>
              <p>Loading hotel details...</p>
            </div>
          ) : hotel ? (
            <div className="hotelContent">
              {/* Enhanced Header with rating and user info */}
              <div className="hotelCompactHeader">
                <div className="hotelBasicInfo">
                  <div className="hotelImageAndInfo">
                    {hotel.logo ? (
                      <Image
                        src={hotel.logo}
                        alt={hotel.name}
                        width={60}
                        height={60}
                        className="hotelLogo"
                      />
                    ) : (
                      <FiHome size={24} />
                    )}
                    <div>
                      <h2>{hotel.name}</h2>
                      <div className="chainAndRating">
                        <span>{hotel.chainName} • {hotel.accommodationType}</span>
                        <div className="ratingStars">
                          {Array(5).fill(0).map((_, index) => (
                            <span key={index} className={index < hotel.starRating ? "starFilled" : "star"}>★</span>
                          ))}
                        </div>
                      </div>
                      <div className="userRatingInfo">
                        <span className="userRating">{hotel.userRating}</span>
                        <span className="userRatingCategory">{hotel.userRatingCategory}</span>
                        <span className="reviewCount">({hotel.userRatingCount} reviews)</span>
                      </div>
                    </div>
                  </div>
                  <div className="statusAndIds">
                    <div className={hotel.status === "Available" ? "statusActive" : "statusUnavailable"}>
                      {hotel.status === "Available" ? <FiCheckCircle size={12} /> : <FiXCircle size={12} />}
                      {hotel.status}
                    </div>
                    <div className="hotelIds">
                      <small>Hotel ID: {hotel.hotelId} | Location ID: {hotel.locationId}</small>
                    </div>
                  </div>
                </div>

                <div className="hotelQuickInfo">
                  <div className="infoItem">
                    <FiMapPin size={12} />
                    <span>{hotel.locality}, {hotel.city}</span>
                  </div>
                  <div className="infoItem">
                    <FiCalendar size={12} />
                    <span>Est. {hotel.establishedDate}</span>
                  </div>
                  <div className="infoItem">
                    <FiHome size={12} />
                    <span>{hotel.availableRooms}/{hotel.totalRooms} Rooms</span>
                  </div>
                </div>
              </div>

              {/* Tab Navigation */}
              <div className="tabNavigation">
                <button
                  className={`tabButton ${activeTab === 'overview' ? 'active' : ''}`}
                  onClick={() => setActiveTab('overview')}
                >
                  <FiInfo size={14} /> Overview
                </button>
                <button
                  className={`tabButton ${activeTab === 'ratings' ? 'active' : ''}`}
                  onClick={() => setActiveTab('ratings')}
                >
                  <FiStar size={14} /> Ratings & Reviews
                </button>
                <button
                  className={`tabButton ${activeTab === 'location' ? 'active' : ''}`}
                  onClick={() => setActiveTab('location')}
                >
                  <FiMap size={14} /> Location & Landmarks
                </button>
                <button
                  className={`tabButton ${activeTab === 'policies' ? 'active' : ''}`}
                  onClick={() => setActiveTab('policies')}
                >
                  <FiCheck size={14} /> Policies
                </button>
                <button
                  className={`tabButton ${activeTab === 'pricing' ? 'active' : ''}`}
                  onClick={() => setActiveTab('pricing')}
                >
                  <FiDollarSign size={14} /> Pricing
                </button>
              </div>

              {/* Tab Content */}
              <div className="tabContent">
                {activeTab === 'overview' && (
                  <div className="detailsGrid">
                    <div className="detailsLeftColumn">
                      <div className="infoCard">
                        <h3>Hotel Information</h3>
                        <div className="infoCardContent">
                          <div className="descriptionBox">
                            <p>{hotel.description}</p>
                          </div>

                          <div className="contactDetails">
                            <h4>Contact Information</h4>
                            <div className="contactItem">
                              <FiUser size={12} />
                              <span>{hotel.contactPerson}</span>
                            </div>
                            <div className="contactItem">
                              <FiMail size={12} />
                              <span>{hotel.contactEmail}</span>
                            </div>
                            <div className="contactItem">
                              <FiPhone size={12} />
                              <span>{hotel.contactPhone}</span>
                            </div>
                          </div>

                          <div className="addressBox">
                            <h4>Address</h4>
                            <p>{hotel.address}</p>
                          </div>
                        </div>
                      </div>

                      <div className="amenitiesCard">
                        <h3>Amenities</h3>
                        <div className="amenitiesGrid">
                          {hotel.amenities.map((amenity, index) => (
                            <div className="amenityTag" key={index}>
                              {amenity === "Free WiFi" && <FiWifi size={12} />}
                              {amenity === "Restaurant" && <FiCoffee size={12} />}
                              {amenity.includes("Pool") && <FiDroplet size={12} />}
                              {amenity.includes("Parking") && <FiCar size={12} />}
                              {!["Free WiFi", "Restaurant"].includes(amenity) &&
                                !amenity.includes("Pool") &&
                                !amenity.includes("Parking") &&
                                <FiCheck size={12} />}
                              <span>{amenity}</span>
                            </div>
                          ))}
                        </div>
                      </div>

                      {/* Spotlight Information */}
                      <div className="spotlightCard">
                        <h3>Highlights</h3>
                        <div className="spotlightItems">
                          {hotel.spotlightInfo.data.map((item, index) => (
                            <div className="spotlightItem" key={index}>
                              <span className="spotlightIcon">{item.icon}</span>
                              <div>
                                <h4>{item.title}</h4>
                                <p>{item.description}</p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="detailsRightColumn">
                      <RoomTypesSection roomTypes={hotel.roomTypes} />

                      <div className="imagesSection">
                        <h3>Hotel Images</h3>
                        <div className="imagesGrid">
                          {hotel.images.map((image, index) => (
                            <div className="imageItem" key={index}>
                              <Image
                                src={image}
                                alt={`${hotel.name} - Image ${index+1}`}
                                width={120}
                                height={80}
                                className="hotelImage"
                              />
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'ratings' && (
                  <div className="ratingsContent">
                    <div className="ratingsOverview">
                      <div className="overallRating">
                        <div className="ratingScore">
                          <span className="bigRating">{hotel.ratingView.averageRating}</span>
                          <div>
                            <div className="ratingCategory">{hotel.userRatingCategory}</div>
                            <div className="reviewCount">{hotel.ratingView.totalReviewCount} reviews</div>
                          </div>
                        </div>
                      </div>

                      <div className="detailedRatings">
                        <h4>Rating Breakdown</h4>
                        {Object.entries(hotel.ratingView.hotelFeaturesRating).map(([feature, rating]: [string, string]) => (
                          <div className="ratingRow" key={feature}>
                            <span className="featureName">{feature}</span>
                            <div className="ratingBar">
                              <div
                                className="ratingFill"
                                style={{ width: `${(parseFloat(rating) / 10) * 100}%` }}
                              ></div>
                            </div>
                            <span className="ratingValue">{rating}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Guest Impressions */}
                    <div className="guestImpressions">
                      {hotel.guestImpressions.map((impression, index) => (
                        <div className="impressionSection" key={index}>
                          <h4>{impression.title}</h4>
                          <div className="impressionItems">
                            {impression.data.map((item, itemIndex) => (
                              <div className="impressionItem" key={itemIndex}>
                                <span className="impressionIcon">{item.icon}</span>
                                <span>{item.impression}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {activeTab === 'location' && (
                  <div className="locationContent">
                    <div className="coordinatesInfo">
                      <h4>Coordinates</h4>
                      <p>Latitude: {hotel.locationDetail.lat}</p>
                      <p>Longitude: {hotel.locationDetail.lon}</p>
                    </div>

                    <div className="landmarksSection">
                      <h4>Nearby Landmarks</h4>
                      <div className="landmarksList">
                        {hotel.landmarks.map((landmark, index) => (
                          <div className="landmarkItem" key={index}>
                            <FiMapPin size={14} />
                            <span className="landmarkName">{landmark.name}</span>
                            <span className="landmarkDistance">{landmark.distance} {landmark.unit}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                )}

                {activeTab === 'policies' && (
                  <div className="policiesContent">
                    <div className="checkInOutTimes">
                      <div className="timeInfo">
                        <FiClock size={16} />
                        <div>
                          <strong>Check-in:</strong> {hotel.houseRulesAndPolicies.checkinTime}
                        </div>
                      </div>
                      <div className="timeInfo">
                        <FiClock size={16} />
                        <div>
                          <strong>Check-out:</strong> {hotel.houseRulesAndPolicies.checkoutTime}
                        </div>
                      </div>
                    </div>

                    <div className="rulesSection">
                      {hotel.houseRulesAndPolicies.rules.map((rule, index) => (
                        <div className="ruleCategory" key={index}>
                          <h4>{rule.heading}</h4>
                          {rule.subRules.map((subRule, subIndex) => (
                            <div className="subRule" key={subIndex}>
                              {subRule.subHeading && <h5>{subRule.subHeading}</h5>}
                              <ul>
                                {subRule.details.map((detail, detailIndex) => (
                                  <li key={detailIndex}>{detail}</li>
                                ))}
                              </ul>
                            </div>
                          ))}
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {activeTab === 'pricing' && hotel.fareDetail && (
                  <div className="pricingContent">
                    <div className="fareBreakdown">
                      <h4>Pricing Information</h4>
                      <div className="fareItem">
                        <span>Base Fare:</span>
                        <span>${hotel.fareDetail.baseFare}</span>
                      </div>
                      <div className="fareItem">
                        <span>Taxes & Fees:</span>
                        <span>${hotel.fareDetail.taxesAndFees}</span>
                      </div>
                      <div className="fareItem discount">
                        <span>Total Discount:</span>
                        <span>-${hotel.fareDetail.totalDiscount}</span>
                      </div>
                      <div className="fareItem instant">
                        <span>Instant Discount:</span>
                        <span>-${hotel.fareDetail.instantDiscount}</span>
                      </div>
                      <div className="fareItem total">
                        <span><strong>Total Price:</strong></span>
                        <span><strong>${hotel.fareDetail.totalPrice}</strong></span>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Action buttons */}
              <div className="actionsBar">

                <button className="actionButton secondary" onClick={handleToggleStatus}>
                  {hotel.status === "Available" ? (
                    <><FiXCircle size={14} /> Disable</>
                  ) : (
                    <><FiCheckCircle size={14} /> Enable</>
                  )}
                </button>
                <button className="actionButton delete" onClick={handleDeleteHotel}>
                  <FiTrash2 size={14} /> Delete
                </button>
              </div>
            </div>
          ) : (
            <div className="noDataFound">
              <p>Hotel not found.</p>
            </div>
          )}
        </div>
      </div>
    );
}

export default Page;
