
"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import "./style.scss";
import Image from "next/image";
import TableWithShimmer from "@/app/components/TableWithShimmer/TableWithShimmer";
import Pagination from "@/app/utilities/Pagination/Pagination";
import TableMenuTwo from "@/app/components/TableMenuTwo/TableMenuTwo";
import { useAlert } from "@/app/utilities/Alert/Alert";
import profilePic from "../../../public/images/profile.png";
import SearchBox from "@/app/components/SearchBox/SearchBox";
import { useRouter } from "next/navigation";

export interface DropdownItem {
  id: string;
  label: string;
}

export interface Hotel {
  id: number;
  name: string;
  location: string;
  chainName: string;
  status: string;
  providerCount: number;
  roomMapping: string;
  nameMapping: string;
  logo?: File | string | null;
}

function Page() {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [visible, setVisible] = useState<boolean>(false);
  const [isTableLoading] = useState<boolean>(false);
  const [status, setStatus] = useState<string>("");
  const [selectedDate, setSelectedDate] = useState<string>("");
  const [providerCountFilter, setProviderCountFilter] = useState<string>("");
  const [mappingStatusFilter, setMappingStatusFilter] = useState<string>("");

  const router = useRouter();

  //filter popup
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);

  // Hotel data
  const [hotels, setHotels] = useState<Hotel[]>([
    {
      "id": 1,
      "name": "Hotel Grand Palace",
      "location": "New York, USA",
      "chainName": "Grand Hotels Chain",
      "status": "Available",
      "providerCount": 5,
      "roomMapping": "Completed",
      "nameMapping": "Completed"
    },
    {
      "id": 2,
      "name": "Seaside Resort",
      "location": "Miami, USA",
      "chainName": "Coastal Resorts",
      "status": "Available",
      "providerCount": 3,
      "roomMapping": "In Progress",
      "nameMapping": "Completed"
    },
    {
      "id": 3,
      "name": "Mountain View Lodge",
      "location": "Denver, USA",
      "chainName": "Nature Retreats",
      "status": "Booked",
      "providerCount": 8,
      "roomMapping": "Completed",
      "nameMapping": "Pending"
    },
    {
      "id": 4,
      "name": "City Center Inn",
      "location": "Chicago, USA",
      "chainName": "Urban Stays",
      "status": "Available",
      "providerCount": 2,
      "roomMapping": "Pending",
      "nameMapping": "Pending"
    },
    {
      "id": 5,
      "name": "Royal Suites",
      "location": "Los Angeles, USA",
      "chainName": "Grand Hotels Chain",
      "status": "Maintenance",
      "providerCount": 6,
      "roomMapping": "Completed",
      "nameMapping": "In Progress"
    }
  ]);

  //filter pop up
  const handleToggleFilter = () => {
    setIsFilterOpen((prev) => !prev);
  };

  const handleFilterSelect = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
    item: string
  ) => {
    const value = event.target.value;
    if (item === "status") {
      setStatus(value);
    } else if (item === "date") {
      setSelectedDate(value);
    } else if (item === "providerCount") {
      setProviderCountFilter(value);
    } else if (item === "mappingStatus") {
      setMappingStatusFilter(value);
    }
  };

  const [searchValue, setSearchValue] = useState<string>("");
  const { fire } = useAlert();

  const handleCreate = () => {
    router.push("/pages/hotelMaster/AddImage");
  };

  const handleEditHotel = (id: number) => {
    router.push(`/pages/hotelMaster/AddImage?id=${id}`);
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);



  const handlePageChange = (pageNo: number) => {
    setCurrentPage(pageNo);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);

    // Recalculate total pages
    const total = Math.ceil(hotels.length / value);
    setTotalPages(total > 0 ? total : 1);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
  };

  const handleDeleteHotel = (id: number) => {
    fire({
      position: "center",
      icon: "info",
      title: `Are you sure you want to delete this hotel?`,
      text: "This action cannot be undone!",
      confirmButtonText: "Yes",
      cancelButtonText: "No",
      onConfirm: async () => {
        console.log("delete id:", id);

        // Remove hotel from list
        const updatedHotels = hotels.filter(hotel => hotel.id !== id);
        setHotels(updatedHotels);

        // Recalculate total pages
        const total = Math.ceil(updatedHotels.length / itemsPerPage);
        setTotalPages(total > 0 ? total : 1);

        // Show success message
        fire({
          position: "center",
          icon: "success",
          title: "Hotel Deleted",
          text: "The hotel has been successfully deleted.",
          confirmButtonText: "Ok"
        });
      },
    });
  };

  const handleToggleStatus = (id: number) => {
    // Find the hotel to toggle
    const hotel = hotels.find(h => h.id === id);

    if (!hotel) return;

    const newStatus = hotel.status === "Available" ? "Unavailable" : "Available";
    const actionType = newStatus === "Available" ? "enable" : "disable";

    fire({
      position: "center",
      icon: "info",
      title: `Are you sure?`,
      text: `To ${actionType} the hotel.`,
      confirmButtonText: "Yes",
      cancelButtonText: "No",
      onConfirm: async () => {
        // Update hotel status
        const updatedHotels = hotels.map(h =>
          h.id === id ? {...h, status: newStatus} : h
        );

        setHotels(updatedHotels);

        // Show success message
        fire({
          position: "center",
          icon: "success",
          title: "Status Updated",
          text: `Hotel status has been updated to ${newStatus}.`,
          confirmButtonText: "Ok"
        });
      },
    });
  };

  const handleMenuItemClick = (item: DropdownItem, id: number) => {
    if (item.id === "delete-hotel") {
      handleDeleteHotel(id);
    } else if (item.id === "disable-hotel" || item.id === "enable-hotel") {
      handleToggleStatus(id);
    } else if (item.id === "edit-hotel") {
      handleEditHotel(id);
    } else if (item.id === "manage-mapping") {
      handleMappingManagement(id);
    } else if (item.id === "add-image") {
      router.push(`/pages/hotelMaster/AddImage?id=${id}&tab=images`);
    }
  };

  // Handle mapping management
  const handleMappingManagement = (hotelId: number) => {
    const hotel = hotels.find(h => h.id === hotelId);
    if (hotel) {
      console.log("Managing mapping for:", hotel.name);
      console.log("Current room mapping:", hotel.roomMapping);
      console.log("Current name mapping:", hotel.nameMapping);

      fire({
        position: "center",
        icon: "info",
        title: "Mapping Management",
        text: `Manage room and name mapping for ${hotel.name}`,
        confirmButtonText: "Update Status",
        cancelButtonText: "Cancel",
        onConfirm: async () => {
          // Simulate updating the mapping status
          setHotels(prevHotels =>
            prevHotels.map(h =>
              h.id === hotelId
                ? {
                    ...h,
                    roomMapping: h.roomMapping === 'Pending' ? 'In Progress' :
                                h.roomMapping === 'In Progress' ? 'Completed' : 'Pending',
                    nameMapping: h.nameMapping === 'Pending' ? 'In Progress' :
                                h.nameMapping === 'In Progress' ? 'Completed' : 'Pending'
                  }
                : h
            )
          );

          // Show success message
          fire({
            position: "center",
            icon: "success",
            title: "Mapping Status Updated",
            text: "The mapping status has been successfully updated.",
            confirmButtonText: "Ok"
          });
        },
      });
    }
  };

  // Filter hotels based on search and status
  const filteredHotels = hotels.filter(hotel => {
    const matchesSearch = !searchValue ||
      hotel.name.toLowerCase().includes(searchValue.toLowerCase()) ||
      hotel.location.toLowerCase().includes(searchValue.toLowerCase()) ||
      hotel.chainName.toLowerCase().includes(searchValue.toLowerCase());

    const matchesStatus = !status ||
      (status === 'in_progress' && hotel.status === 'Available') ||
      (status === 'failed' && hotel.status === 'Maintenance') ||
      (status === 'success' && hotel.status === 'Available');

    const matchesProviderCount = !providerCountFilter ||
      (providerCountFilter === 'low' && hotel.providerCount <= 3) ||
      (providerCountFilter === 'medium' && hotel.providerCount >= 4 && hotel.providerCount <= 6) ||
      (providerCountFilter === 'high' && hotel.providerCount >= 7);

    const matchesMappingStatus = !mappingStatusFilter ||
      (mappingStatusFilter === 'active' && (hotel.roomMapping === 'Completed' && hotel.nameMapping === 'Completed')) ||
      (mappingStatusFilter === 'room_mapping' && hotel.roomMapping !== 'Completed') ||
      (mappingStatusFilter === 'name_mapping' && hotel.nameMapping !== 'Completed');

    return matchesSearch && matchesStatus && matchesProviderCount && matchesMappingStatus;
  });

  // Paginate hotels
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedHotels = filteredHotels.slice(startIndex, endIndex);

  // Calculate actual total pages
  useEffect(() => {
    const total = Math.ceil(filteredHotels.length / itemsPerPage);
    setTotalPages(total > 0 ? total : 1);
    // If current page is now beyond total pages, reset to page 1
    if (currentPage > total && total > 0) {
      setCurrentPage(1);
    }
  }, [filteredHotels.length, itemsPerPage, currentPage]);



    const handleViewDetails = (e: React.MouseEvent, id: number) => {
      e.preventDefault();
      router.push(`/pages/masterDetails?id=${id}`);
    }

  return (
    <>
      <div
        className={`agentList overall-list-padding ${visible ? "visible" : ""}`}
      >
        <div className="addUser">
          <div className="addButton" onClick={handleCreate}>
            Add Hotel +
          </div>
        </div>

        <div className="user-list-table-container">
          <div className="tableBorder">
            <div className="table-header">
              <h5>Hotel List</h5>

              <div className="filter-search-container">
                <div className="filterButton" onClick={handleToggleFilter}>
                  <button>
                    <i className="fa-solid fa-filter"></i>
                  </button>
                </div>

                <div
                  className={`filter-options-select-box ${
                    isFilterOpen ? "show" : ""
                  }`}
                >
                  <div className="filterOption">
                    <span>Status {status ? `(${status})` : ""}</span>
                    <select
                      className="dropdown"
                      value={status}
                      onChange={(e) => handleFilterSelect(e, "status")}
                    >
                      <option value="">None</option>
                      <option value="in_progress">In Progress</option>
                      <option value="failed">Failed</option>
                      <option value="success">Success</option>
                    </select>
                    <span className="material-icons">keyboard_arrow_down</span>
                  </div>

                  <div className="filterOption">
                    <span>Provider Count {providerCountFilter ? `(${providerCountFilter})` : ""}</span>
                    <select
                      className="dropdown"
                      value={providerCountFilter}
                      onChange={(e) => handleFilterSelect(e, "providerCount")}
                    >
                      <option value="">All</option>
                      <option value="low">Low (1-3)</option>
                      <option value="medium">Medium (4-6)</option>
                      <option value="high">High (7+)</option>
                    </select>
                    <span className="material-icons">keyboard_arrow_down</span>
                  </div>

                  <div className="filterOption">
                    <span>Mapping Status {mappingStatusFilter ? `(${mappingStatusFilter})` : ""}</span>
                    <select
                      className="dropdown"
                      value={mappingStatusFilter}
                      onChange={(e) => handleFilterSelect(e, "mappingStatus")}
                    >
                      <option value="">All</option>
                      <option value="active">Active (Both Complete)</option>
                      <option value="room_mapping">Room Mapping Needed</option>
                      <option value="name_mapping">Name Mapping Needed</option>
                    </select>
                    <span className="material-icons">keyboard_arrow_down</span>
                  </div>

                  <input
                    className="date-picker"
                    type="date"
                    value={selectedDate}
                    onChange={(e) => handleFilterSelect(e, "date")}
                  />
                </div>

                <SearchBox
                  value={searchValue}
                  onChange={handleSearchChange}
                  placeholders={["Search by hotel name"]}
                />
              </div>
            </div>

            {isTableLoading ? (
              <TableWithShimmer
                no_of_rows={itemsPerPage < 8 ? itemsPerPage : 8}
                no_of_cols={6}
                colWidths={[1.5, 1.5, 1, 1]}
              />
            ) : (
              <div className="table-style table-vertical-scroll">
                <table>
                  <thead>
                    <tr>
                      <th>Hotel ID</th>
                      <th>Hotel Name</th>
                      <th>Current Status</th>
                      <th>Location</th>
                      <th>Chain Name</th>
                      <th>Provider Count</th>
                      <th>Room Mapping</th>
                      <th>Name Mapping</th>
                      <th>Details</th>
                      <th>Actions</th>
                    </tr>
                  </thead>

                  <tbody>
                    {paginatedHotels.length > 0 ? (
                      paginatedHotels.map((hotel) => (
                        <tr key={hotel.id}>
                          {/* Hotel ID - First Column */}
                          <td>
                            <span className="hotel-id">{hotel.id}</span>
                          </td>

                          {/* Hotel Name with Image */}
                          <td>
                            <div className="name-profilePic">
                              <div className="profile-pic">
                                <Image
                                  src={profilePic}
                                  alt="Hotel Logo"
                                  fill
                                  className="profileImage"
                                  style={{ objectFit: "cover" }}
                                  sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                                  loading="lazy"
                                />
                              </div>
                              <div className="name">{hotel.name}</div>
                            </div>
                          </td>

                          {/* Current Status */}
                          <td>
                            <div className={hotel.status === "Available" ? "available" : "offline"}>
                              <span>{hotel.status}</span>
                            </div>
                          </td>

                          {/* Location */}
                          <td>
                            <span className="location">
                              {hotel.location}
                            </span>
                          </td>

                          {/* Chain Name */}
                          <td>
                            <span className="chain-name">{hotel.chainName}</span>
                          </td>

                          {/* Provider Count */}
                          <td>
                            <div className="provider-count">
                              <span className="count-badge">{hotel.providerCount}</span>
                              <span className="count-label">Providers</span>
                            </div>
                          </td>

                          {/* Room Mapping Status */}
                          <td>
                            <div className={`mapping-status ${hotel.roomMapping.toLowerCase().replace(/\s+/g, '_')}`}>
                              <span className="status-dot"></span>
                              <span className="status-text">
                                {hotel.roomMapping}
                              </span>
                            </div>
                          </td>

                          {/* Name Mapping Status */}
                          <td>
                            <div className={`mapping-status ${hotel.nameMapping.toLowerCase().replace(/\s+/g, '_')}`}>
                              <span className="status-dot"></span>
                              <span className="status-text">
                                {hotel.nameMapping}
                              </span>
                            </div>
                          </td>

                          {/* Details */}
                          <td>
                            <div className="viewDetails">
                              <Link
                                href="#"
                                onClick={(e) => {
                                 handleViewDetails(e, hotel?.id);
                                }}
                              >
                                View Details
                              </Link>
                            </div>
                          </td>

                          {/* Actions */}
                          <td onClick={(e) => e.stopPropagation()}>
                            <TableMenuTwo
                              items={[
                                { id: "edit-hotel", label: "Edit Hotel" },
                                { id: "manage-mapping", label: "Manage Mapping" },
                                {
                                  id: hotel.status === "Available" ? "disable-hotel" : "enable-hotel",
                                  label: hotel.status === "Available" ? "Disable Hotel" : "Enable Hotel",
                                },
                                { id: "delete-hotel", label: "Delete Hotel" },
                              ]}
                              onClick={(item) => handleMenuItemClick(item, hotel.id)}
                              id={hotel.id}
                            />
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={10} className="no-data2">
                          <h5>
                            {!searchValue && !status &&
                              `It looks like you don't have a hotel yet.`}
                            {(searchValue || status) &&
                              `No hotel matches your search for "${searchValue}".`}
                          </h5>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}

            <Pagination
              totalPages={totalPages}
              handlePage={handlePageChange}
              itemsPerPage={itemsPerPage}
              page={currentPage}
              handleItemsPerPageChange={handleItemsPerPageChange}
            />
          </div>

          {/* Here is the corrected HotelDetails component usage */}
          {/* {showDetails && selectedHotel && (
            <HotelDetails
              showDetails={showDetails}
              hotelId={selectedHotel.id}
              handleClose={handleCloseDetails}
            />
          )} */}
        </div>
      </div>
    </>
  );
}

export default Page;