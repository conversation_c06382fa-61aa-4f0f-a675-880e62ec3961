
"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import "./style.scss";
import Image from "next/image";
import TableWithShimmer from "@/app/components/TableWithShimmer/TableWithShimmer";
import Pagination from "@/app/utilities/Pagination/Pagination";
import TableMenuTwo from "@/app/components/TableMenuTwo/TableMenuTwo";
import { useAlert } from "@/app/utilities/Alert/Alert";
import profilePic from "../../../public/images/profile.png";
import SearchBox from "@/app/components/SearchBox/SearchBox";
import { useRouter } from "next/navigation";

export interface DropdownItem {
  id: string;
  label: string;
}

export interface Hotel {
  id: number;
  name: string;
  location: string;
  chainName: string;
  status: string;
  logo?: File | string | null;
}

function Page() {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [showDetails, setShowDetails] = useState<boolean>(false);
  const [visible, setVisible] = useState<boolean>(false);
  const [isTableLoading] = useState<boolean>(false);
  const [selectedHotelId, setSelectedHotelId] = useState<number | null>(null);
  const [status, setStatus] = useState<string>("");
  const [selectedDate, setSelectedDate] = useState<string>("");

  const router = useRouter();

  //filter popup
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);
  
  // Hotel data
  const [hotels, setHotels] = useState<Hotel[]>([
    {
    "id": 1,
    "name": "Hotel Grand Palace",
    "location": "New York, USA",
    "chainName": "Grand Hotels Chain",
    "status": "Available"
  },
  {
    "id": 2,
    "name": "Hotel Grand Palsssce",
    "location": "New York, ssUSA",
    "chainName": "Grand Hotels Chain",
    "status": "Available"
  },
  {
    "id": 3,
    "name": "Seaside Resort",
    "location": "Miami, USA",
    "chainName": "Coastal Resorts",
    "status": "Available"
  },
  {
    "id": 4, 
    "name": "Mountain View Lodge",
    "location": "Denver, USA",
    "chainName": "Nature Retreats",
    "status": "Booked"
  },
  {
    "id": 5,
    "name": "City Center Inn",
    "location": "Chicago, USA",
    "chainName": "Urban Stays",
    "status": "Available"
  },
  {
    "id": 6,
    "name": "Royal Suites",
    "location": "Los Angeles, USA",
    "chainName": "Grand Hotels Chain",
    "status": "Maintenance"
  },
  {
    "id": 7,
    "name": "Harbor View Hotel",
    "location": "San Francisco, USA",
    "chainName": "Coastal Resorts",
    "status": "Available"
  },
  {
    "id": 8,
    "name": "Desert Oasis Resort",
    "location": "Phoenix, USA",
    "chainName": "Nature Retreats",
    "status": "Available"
  },
  {
    "id": 9,
    "name": "Metropolitan Hotel",
    "location": "Boston, USA",
    "chainName": "Urban Stays",
    "status": "Booked"
  },
  {
    "id": 10,
    "name": "Golden Gate Lodge",
    "location": "San Francisco, USA",
    "chainName": "Grand Hotels Chain",
    "status": "Available"
  }
  ]);

  //filter pop up
  const handleToggleFilter = () => {
    setIsFilterOpen((prev) => !prev);
  };

  const handleFilterSelect = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
    item: string
  ) => {
    const value = event.target.value;
    if (item === "status") {
      setStatus(value);
    } else if (item === "date") {
      setSelectedDate(value);
    }
  };

  const [searchValue, setSearchValue] = useState<string>("");
  const { fire } = useAlert();

  const handleCreate = () => {
    router.push("/pages/hotelMaster/AddImage");
  };
  
  const handleEditHotel = (id: number) => {
    router.push(`/pages/hotelMaster/AddImage?id=${id}`);
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(true);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const handleShowDetails = (id: number) => {
    setSelectedHotelId(id);
    setShowDetails(true);
  };

  const handleCloseDetails = () => {
    setShowDetails(false);
    setSelectedHotelId(null);
  };

  const handlePageChange = (pageNo: number) => {
    setCurrentPage(pageNo);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
    
    // Recalculate total pages
    const total = Math.ceil(hotels.length / value);
    setTotalPages(total > 0 ? total : 1);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
  };

  const handleDeleteHotel = (id: number) => {
    fire({
      position: "center",
      icon: "info",
      title: `Are you sure you want to delete this hotel?`,
      text: "This action cannot be undone!",
      confirmButtonText: "Yes",
      cancelButtonText: "No",
      onConfirm: async () => {
        console.log("delete id:", id);
        
        // Remove hotel from list
        const updatedHotels = hotels.filter(hotel => hotel.id !== id);
        setHotels(updatedHotels);
        
        // Recalculate total pages
        const total = Math.ceil(updatedHotels.length / itemsPerPage);
        setTotalPages(total > 0 ? total : 1);

        // Show success message
        fire({
          position: "center",
          icon: "success",
          title: "Hotel Deleted",
          text: "The hotel has been successfully deleted.",
          confirmButtonText: "Ok"
        });
      },
    });
  };

  const handleToggleStatus = (id: number) => {
    // Find the hotel to toggle
    const hotel = hotels.find(h => h.id === id);
    
    if (!hotel) return;
    
    const newStatus = hotel.status === "Available" ? "Unavailable" : "Available";
    const actionType = newStatus === "Available" ? "enable" : "disable";
    
    fire({
      position: "center",
      icon: "info",
      title: `Are you sure?`,
      text: `To ${actionType} the hotel.`,
      confirmButtonText: "Yes",
      cancelButtonText: "No",
      onConfirm: async () => {
        // Update hotel status
        const updatedHotels = hotels.map(h => 
          h.id === id ? {...h, status: newStatus} : h
        );
        
        setHotels(updatedHotels);
        
        // Show success message
        fire({
          position: "center",
          icon: "success",
          title: "Status Updated",
          text: `Hotel status has been updated to ${newStatus}.`,
          confirmButtonText: "Ok"
        });
      },
    });
  };

  const handleMenuItemClick = (item: DropdownItem, id: number) => {
    if (item.id === "delete-hotel") {
      handleDeleteHotel(id);
    } else if (item.id === "disable-hotel" || item.id === "enable-hotel") {
      handleToggleStatus(id);
    } else if (item.id === "edit-hotel") {
      handleEditHotel(id);
    } else if (item.id === "add-image") {
      router.push(`/pages/hotelMaster/AddImage?id=${id}&tab=images`);
    }
  };

  // Filter hotels based on search and status
  const filteredHotels = hotels.filter(hotel => {
    const matchesSearch = !searchValue || 
      hotel.name.toLowerCase().includes(searchValue.toLowerCase()) ||
      hotel.location.toLowerCase().includes(searchValue.toLowerCase()) ||
      hotel.chainName.toLowerCase().includes(searchValue.toLowerCase());
      
    const matchesStatus = !status || 
      (status === 'in_progress' && hotel.status === 'Available') || 
      (status === 'failed' && hotel.status === 'Unavailable');
      
    return matchesSearch && matchesStatus;
  });

  // Paginate hotels
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedHotels = filteredHotels.slice(startIndex, endIndex);

  // Calculate actual total pages
  useEffect(() => {
    const total = Math.ceil(filteredHotels.length / itemsPerPage);
    setTotalPages(total > 0 ? total : 1);
    // If current page is now beyond total pages, reset to page 1
    if (currentPage > total && total > 0) {
      setCurrentPage(1);
    }
  }, [filteredHotels.length, itemsPerPage, currentPage]);

  // Find the selected hotel details
  const selectedHotel = selectedHotelId 
    ? hotels.find(hotel => hotel.id === selectedHotelId) 
    : null;

    const handleViewDetails = (e: React.MouseEvent, id: number) => {
      e.preventDefault();
      router.push(`/pages/masterDetails?id=${id}`);
    }

  return (
    <>
      <div
        className={`agentList overall-list-padding ${visible ? "visible" : ""}`}
      >
        <div className="addUser">
          <div className="addButton" onClick={handleCreate}>
            Add Hotel +
          </div>
        </div>

        <div className="user-list-table-container">
          <div className="tableBorder">
            <div className="table-header">
              <h5>Hotel List</h5>

              <div className="filter-search-container">
                <div className="filterButton" onClick={handleToggleFilter}>
                  <button>
                    <i className="fa-solid fa-filter"></i>
                  </button>
                </div>

                <div
                  className={`filter-options-select-box ${
                    isFilterOpen ? "show" : ""
                  }`}
                >
                  <div className="filterOption">
                    <span>Status {status ? `(${status})` : ""}</span>
                    <select
                      className="dropdown"
                      value={status}
                      onChange={(e) => handleFilterSelect(e, "status")}
                    >
                      <option value="">None</option>
                      <option value="in_progress">In Progress</option>
                      <option value="failed">Failed</option>
                      <option value="success">Success</option>
                    </select>
                    <span className="material-icons">keyboard_arrow_down</span>
                  </div>
                  <input
                    className="date-picker"
                    type="date"
                    value={selectedDate}
                    onChange={(e) => handleFilterSelect(e, "date")}
                  />
                </div>

                <SearchBox
                  value={searchValue}
                  onChange={handleSearchChange}
                  placeholders={["Search by hotel name"]}
                />
              </div>
            </div>

            {isTableLoading ? (
              <TableWithShimmer
                no_of_rows={itemsPerPage < 8 ? itemsPerPage : 8}
                no_of_cols={6}
                colWidths={[1.5, 1.5, 1, 1]}
              />
            ) : (
              <div className="table-style table-vertical-scroll">
                <table>
                  <thead>
                    <tr>
                      <th>Hotel Name</th>
                      <th>Current Status</th>
                      <th>Hotel ID</th>
                      <th>Location</th>
                      <th>Chain Name</th>
                      <th>Details</th>
                      <th>Actions</th>
                    </tr>
                  </thead>

                  <tbody>
                    {paginatedHotels.length > 0 ? (
                      paginatedHotels.map((hotel) => (
                        <tr key={hotel.id}>
                          <td>
                            <div className="name-profilePic">
                              <div className="profile-pic">
                                <Image
                                  src={profilePic}
                                  alt="Hotel Logo"
                                  fill
                                  className="profileImage"
                                  style={{ objectFit: "cover" }}
                                  sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                                  loading="lazy"
                                />
                              </div>
                              <div className="name">{hotel.name}</div>
                            </div>
                          </td>

                          <td>
                            <div className={hotel.status === "Available" ? "available" : "offline"}>
                              <span>{hotel.status}</span>
                            </div>
                          </td>

                          <td>
                            <span className="empId">{hotel.id}</span>
                          </td>
                          
                          <td>
                            <span className="walletAmount">
                              {hotel.location}
                            </span>
                          </td>
                          
                          <td>
                            <span className="contact">{hotel.chainName}</span>
                          </td>
                          
                          <td>
                            <div className="viewDetails">
                              <Link
                                href="#"
                                onClick={(e) => {
                                 handleViewDetails(e, hotel?.id);
                                }}
                              >
                                View Details
                              </Link>
                            </div>
                          </td>
                          
                          <td onClick={(e) => e.stopPropagation()}>
                            <TableMenuTwo
                              items={[
                                { id: "edit-hotel", label: "Edit Hotel" },
                                {
                                  id: hotel.status === "Available" ? "disable-hotel" : "enable-hotel",
                                  label: hotel.status === "Available" ? "Disable Hotel" : "Enable Hotel",
                                },
                                { id: "delete-hotel", label: "Delete Hotel" },
                              ]}
                              onClick={(item) => handleMenuItemClick(item, hotel.id)}
                              id={hotel.id}
                            />
                          </td>
                        </tr>
                      ))
                    ) : (
                      <tr>
                        <td colSpan={7} className="no-data2">
                          <h5>
                            {!searchValue && !status &&
                              `It looks like you don't have a hotel yet.`}
                            {(searchValue || status) &&
                              `No hotel matches your search for "${searchValue}".`}
                          </h5>
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            )}

            <Pagination
              totalPages={totalPages}
              handlePage={handlePageChange}
              itemsPerPage={itemsPerPage}
              page={currentPage}
              handleItemsPerPageChange={handleItemsPerPageChange}
            />
          </div>
          
          {/* Here is the corrected HotelDetails component usage */}
          {/* {showDetails && selectedHotel && (
            <HotelDetails 
              showDetails={showDetails}
              hotelId={selectedHotel.id}
              handleClose={handleCloseDetails}
            />
          )} */}
        </div>
      </div>
    </>
  );
}

export default Page;