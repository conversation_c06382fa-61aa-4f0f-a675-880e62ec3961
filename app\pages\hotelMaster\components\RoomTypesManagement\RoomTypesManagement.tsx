
import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>ollarSign, FiPlus, FiEdit, FiEye } from "react-icons/fi";
import RoomTypeModal from "../AddRoomType/AddRoomType"; // Updated import name
import RoomDetails from "../RoomDetails/RoomDetails"; // Import RoomDetails component
import "./RoomTypesManagement.scss";

interface RoomType {
  id: string;
  name: string;
  description: string;
  capacity: number;
  pricePerNight: string;
  amenities: string[];
  availability: number;
  images?: File[];
  // Additional comprehensive fields
  roomSize: string;
  bedType: string;
  viewType: string;
  floorRange: string;
  smokingPolicy: 'smoking' | 'non-smoking' | 'both';
  bathroomType: string;
  balcony: boolean;
  airConditioning: boolean;
  wifi: boolean;
  maxOccupancy: number;
  extraBedAvailable: boolean;
  extraBedPrice: string;
  roomFeatures: string[];
  cancellationPolicy: string;
  checkInTime: string;
  checkOutTime: string;
}

interface RoomTypesSectionProps {
  roomTypes: RoomType[];
  onAddRoomType?: (roomType: Omit<RoomType, "id">) => void;
  onEditRoomType?: (id: string, roomType: Omit<RoomType, "id">) => void;
  onViewRoomType?: (roomType: RoomType) => void;
}

const RoomTypesSection: React.FC<RoomTypesSectionProps> = ({
  roomTypes,
  onAddRoomType,
  onEditRoomType,
  onViewRoomType,
}) => {
  const [isModalOpen, setIsModalOpen] = React.useState<boolean>(false);
  const [modalMode, setModalMode] = React.useState<"add" | "edit" | "view">(
    "add"
  );
  const [selectedRoomType, setSelectedRoomType] =
    React.useState<RoomType | null>(null);

  // State for RoomDetails component
  const [showRoomDetails, setShowRoomDetails] = React.useState<boolean>(false);
  const [selectedRoomForDetails, setSelectedRoomForDetails] = React.useState<RoomType | null>(null);

  const handleAddRoomType = (newRoomType: Omit<RoomType, "id">) => {
    // Generate a unique ID for the new room type
    const roomTypeWithId: RoomType = {
      ...newRoomType,
      id: Date.now().toString(), // Simple ID generation, you might want to use a more robust method
    };

    // Call the parent's onAddRoomType function if provided
    if (onAddRoomType) {
      onAddRoomType(roomTypeWithId);
    }

    // Close the modal
    setIsModalOpen(false);
    setSelectedRoomType(null);
  };

  const handleEditRoomType = (updatedRoomType: Omit<RoomType, "id">) => {
    if (selectedRoomType && onEditRoomType) {
      onEditRoomType(selectedRoomType.id, updatedRoomType);
    }

    // Close the modal
    setIsModalOpen(false);
    setSelectedRoomType(null);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedRoomType(null);
    setModalMode("add");
  };

  const openAddModal = () => {
    setModalMode("add");
    setSelectedRoomType(null);
    setIsModalOpen(true);
  };

  const openEditModal = (roomType: RoomType) => {
    setModalMode("edit");
    setSelectedRoomType(roomType);
    setIsModalOpen(true);
  };

  const openViewModal = (roomType: RoomType) => {
    if (onViewRoomType) {
      onViewRoomType(roomType);
    } else {
      // Show RoomDetails component with the selected room data
      setSelectedRoomForDetails(roomType);
      setShowRoomDetails(true);
    }
  };

  const handleCloseRoomDetails = () => {
    setShowRoomDetails(false);
    setSelectedRoomForDetails(null);
  };

  const handleModalSubmit = (roomTypeData: Omit<RoomType, "id">) => {
    if (modalMode === "add") {
      handleAddRoomType(roomTypeData);
    } else if (modalMode === "edit") {
      handleEditRoomType(roomTypeData);
    }
    // View mode doesn't submit
  };

  // Function to convert RoomType to the format expected by RoomDetails
  const convertRoomTypeToRoomData = (roomType: RoomType) => {
    return {
      id: roomType.id,
      masterRoomId: `MRM${roomType.id}`,
      providerId: "PROV123", // Default provider ID
      name: roomType.name,
      roomsLeft: roomType.availability,
      extraBedText: roomType.extraBedAvailable && roomType.extraBedPrice
        ? `Extra bed available for ${roomType.extraBedPrice}`
        : roomType.capacity > 2 ? "Extra bed available for $50/night" : "",
      properties: [
        { code: "Capacity", data: `${roomType.capacity} guests` },
        { code: "Max Occupancy", data: `${roomType.maxOccupancy || roomType.capacity} guests` },
        { code: "Room Size", data: roomType.roomSize || "Not specified" },
        { code: "Bed Type", data: roomType.bedType || "Not specified" },
        { code: "View Type", data: roomType.viewType || "Not specified" },
        { code: "Floor Range", data: roomType.floorRange || "Not specified" },
        { code: "Bathroom", data: roomType.bathroomType || "Private Bathroom" },
        { code: "Smoking Policy", data: roomType.smokingPolicy === 'smoking' ? 'Smoking Allowed' : roomType.smokingPolicy === 'both' ? 'Both Available' : 'Non-Smoking' },
        { code: "Check-in Time", data: roomType.checkInTime || "15:00" },
        { code: "Check-out Time", data: roomType.checkOutTime || "11:00" },
        { code: "Price", data: roomType.pricePerNight },
        { code: "Description", data: roomType.description }
      ],
      roomLevelAmenities: [
        ...roomType.amenities,
        ...(roomType.roomFeatures || []).filter(feature => feature.trim() !== ""),
        ...(roomType.balcony ? ["Private Balcony"] : []),
        ...(roomType.airConditioning ? ["Air Conditioning"] : []),
        ...(roomType.wifi ? ["Free WiFi"] : [])
      ].filter(amenity => amenity.trim() !== ""),
      imageList: roomType.images && roomType.images.length > 0
        ? roomType.images.map((img, index) => ({
            url: URL.createObjectURL(img),
            caption: `Room Image ${index + 1}`
          }))
        : [
            {
              url: "https://images.unsplash.com/photo-1611892440504-42a792e24d32?w=400&h=300&fit=crop",
              caption: "Sample Room View"
            }
          ],
      fareDetail: {
        baseFare: parseInt(roomType.pricePerNight.replace(/[^0-9]/g, '')) || 280,
        markUpFare: 42,
        taxesAndFees: 38,
        instantDiscount: 20,
        totalDiscount: 30,
        totalPrice: parseInt(roomType.pricePerNight.replace(/[^0-9]/g, '')) || 310,
        numberOfPersons: roomType.capacity,
        taxAndChargeMap: {
          "City Tax": 15,
          "Service Fee": 13,
          "Resort Fee": 10
        },
        discountMap: {
          "Early Bird Discount": 20,
          "Loyalty Discount": 10
        }
      },
      roomOptions: [
        {
          blockId: `BLK${roomType.id}_1`,
          name: "Standard Rate",
          paymentType: "Pay at Hotel",
          fomoTag: "Best Value!",
          isRecommended: true,
          mealBenefits: ["Continental Breakfast", "Welcome Drink"],
          otherBenefits: ["Free WiFi", "Late Checkout", "Pool Access"],
          cancellationBenefits: {
            code: "FLEX",
            data: "Free cancellation up to 24 hours before check-in",
            policy: [
              {
                title: "Free Cancellation",
                subTitle: "Full Refund",
                startDate: "2024-12-01",
                endDate: "2024-12-24",
                cancellationAmount: 0
              }
            ]
          },
          fareDetail: {
            baseFare: parseInt(roomType.pricePerNight.replace(/[^0-9]/g, '')) || 280,
            markUpFare: 42,
            taxesAndFees: 38,
            instantDiscount: 20,
            totalDiscount: 30,
            totalPrice: parseInt(roomType.pricePerNight.replace(/[^0-9]/g, '')) || 310,
            numberOfPersons: roomType.capacity
          }
        }
      ]
    };
  };

  return (
    <>
      <div className="roomTypesSection">
        <div className="room-type-header">
          <h3>Room Types</h3>
          <button onClick={openAddModal} className="add-room-type-btn">
            <FiPlus size={14} />
            Add Room Type
          </button>
        </div>

        <div className="roomTypesList">
          {roomTypes.length === 0 ? (
            <div className="no-room-types">
              <p>
                No room types added yet. Click "Add Room Type" to get started.
              </p>
            </div>
          ) : (
            roomTypes.map((roomType) => (
              <div className="roomTypeCard" key={roomType.id}>
                <div className="roomTypeHeader">
                  <h4>{roomType.name}</h4>
                  <div className="priceTag">
                    <FiDollarSign size={12} />
                    {roomType.pricePerNight}
                  </div>
                </div>
                <div className="roomTypeBody">
                  <p>{roomType.description}</p>
                  <div className="roomTypeMetrics">
                    <div className="metric">
                      <FiUser size={12} />
                      <span>
                        {roomType.capacity}{" "}
                        {roomType.capacity > 1 ? "Guests" : "Guest"}
                      </span>
                    </div>
                    <div className="metric">
                      {roomType.availability > 0 ? (
                        <span className="available">
                          {roomType.availability} Available
                        </span>
                      ) : (
                        <span className="unavailable">Sold Out</span>
                      )}
                    </div>
                  </div>
                  {roomType.amenities.length > 0 && (
                    <div className="roomAmenities">
                      {roomType.amenities.join(", ")}
                    </div>
                  )}
                </div>

                {/* Action buttons in bottom right */}
                <div className="roomTypeActions">
                  <button
                    onClick={() => openViewModal(roomType)}
                    className="action-btn view-btn"
                    title="View Room Type"
                  >
                    <FiEye size={14} />
                    View
                  </button>

                  <button
                    onClick={() => openEditModal(roomType)}
                    className="action-btn edit-btn"
                    title="Edit Room Type"
                  >
                    <FiEdit size={14} />
                    Edit
                  </button>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      <RoomTypeModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSubmit={handleModalSubmit}
        mode={modalMode}
        roomType={selectedRoomType}
      />

      {/* RoomDetails Component - Only show when room is selected */}
      {showRoomDetails && selectedRoomForDetails && (
        <RoomDetails
          isOpen={showRoomDetails}
          handleClose={handleCloseRoomDetails}
          roomData={convertRoomTypeToRoomData(selectedRoomForDetails)}
        />
      )}
    </>
  );
};

export default RoomTypesSection;