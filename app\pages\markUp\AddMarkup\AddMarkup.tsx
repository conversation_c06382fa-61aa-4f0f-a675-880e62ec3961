


"use client";
import React, { useEffect, useRef } from "react";
import "./AddMarkup.scss";
import { useF<PERSON>, Submit<PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import { Autocomplete, TextField } from "@mui/material";
import { useCommonContext } from "@/app/context/commonContext";

interface Markup {
  id: number;
  name: string;
  type: string;
  value: string;
  status: string;
  markup_code?: string;
  remarks?: string;
}

interface AddMarkupProps {
  showCreate: boolean;
  handleCloseCreate: VoidFunction;
  editData: Markup | null;
  isEditMode: boolean;
  onSave: (data: Markup) => void;
}

interface OptionType {
  id: number;
  name: string;
}

interface CreateMarkupForm {
  name: string;
  markup_code: string;
  value: string;
  type: string;
  remarks: string;
  upload_file?: File;
}

function AddMarkup({ showCreate, handleCloseCreate, editData, isEditMode, onSave }: AddMarkupProps) {
  const { setIsLoading } = useCommonContext();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const {
    register,
    handleSubmit,
    setValue,
    setError,
    control,
    reset,
    formState: { errors },
  } = useForm<CreateMarkupForm>({
    defaultValues: {
      name: "",
      markup_code: "",
      value: "",
      type: "",
      remarks: "",
    }
  });

  // When editData changes, update form values
  useEffect(() => {
    if (editData && isEditMode) {
      reset({
        name: editData.name,
        markup_code: editData.markup_code || "",
        value: editData.value.replace(/[^0-9.]/g, ""), // Remove currency symbols and % signs
        type: editData.type,
        remarks: editData.remarks || "",
      });
    } else {
      reset({
        name: "",
        markup_code: "",
        value: "",
        type: "",
        remarks: "",
      });
    }
  }, [editData, isEditMode, reset]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setValue("upload_file", e.target.files[0]);
    }
  };

  const handleFileBrowse = () => {
    fileInputRef.current?.click();
  };

  const onSubmit: SubmitHandler<CreateMarkupForm> = (data) => {
    setIsLoading(true);

    // For this example we'll make file upload optional
    // In a real app you might want to enforce this requirement
    if (!isEditMode && !data.upload_file) {
      // Show error only for new submissions
      setError("upload_file", {
        type: "manual",
        message: "Please upload a file.",
      });
      setIsLoading(false);
      return;
    }

    try {
      // Format the value based on type (add % or ₹)
      const formattedValue = data.type === "Percentage" 
        ? `${data.value}%` 
        : `₹${data.value}`;

      // Create the markup object
      const markupData: Markup = {
        id: editData?.id || 0, // Will be set properly in the parent component for new items
        name: data.name,
        markup_code: data.markup_code,
        type: data.type,
        value: formattedValue,
        status: editData?.status || "Active", // Default to Active for new markups
        remarks: data.remarks,
      };

      // Call the parent save handler
      onSave(markupData);
      
      // Simulate API call delay
      setTimeout(() => {
        setIsLoading(false);
        handleCloseCreate();
      }, 500);
    } catch (error) {
      console.error("Error saving markup:", error);
      setIsLoading(false);
      // Show error alert
      // fire({
      //   position: "center",
      //   icon: "error",
      //   title: "An Error Occurred",
      //   text: "Failed to save markup data.",
      //   confirmButtonText: "Ok",
      // });
    }
  };

  const typeOptions: OptionType[] = [
    { id: 1, name: "Flat" },
    { id: 2, name: "Percentage" },
  ];

  return (
    <div className={`create-form-overlay ${showCreate ? "show" : ""}`}>
      <div className="create-form-container">
        <div className="create-form-header-div">
          <h3>{isEditMode ? "Edit" : "Add"} Markup</h3>
          <span className="material-icons closeIcon" onClick={handleCloseCreate}>
            close
          </span>
        </div>
        <div className="create-form-div scroll-bar-1">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="input-field-container">
              <div className="input-field wf-50">
                <label htmlFor="name">Markup Name</label>
                <input
                  id="name"
                  type="text"
                  placeholder="Type..."
                  {...register("name", {
                    required: "Please enter markup name.",
                  })}
                />
                <p className="error-message">{errors.name?.message}</p>
              </div>

              <div className="input-field wf-50">
                <label htmlFor="markup_code">Markup Code</label>
                <input
                  id="markup_code"
                  type="text"
                  maxLength={10}
                  placeholder="Type..."
                  {...register("markup_code", {
                    required: "Please enter markup code.",
                    maxLength: {
                      value: 10,
                      message: "Code cannot exceed 10 characters.",
                    },
                  })}
                />
                <p className="error-message">{errors.markup_code?.message}</p>
              </div>
            </div>
            
            <div className="input-field-container">
              <div className="input-field wf-50">
                <label htmlFor="value">Value</label>
                <input
                  id="value"
                  type="number"
                  step="0.01"
                  placeholder="Type..."
                  {...register("value", {
                    required: "Please enter a value.",
                    validate: (value) => 
                      parseFloat(value) > 0 || "Value must be greater than 0"
                  })}
                />
                <p className="error-message">{errors.value?.message}</p>
              </div>

              <div className="select-input-field wf-50">
                <label htmlFor="type">Type</label>
                <Controller
                  name="type"
                  control={control}
                  rules={{ required: "Please select a type." }}
                  render={({ field }) => (
                    <Autocomplete
                      id="type"
                      options={typeOptions}
                      getOptionLabel={(option) =>
                        typeof option === "object" && option !== null ? option.name : ""
                      }
                      value={
                        field.value
                          ? typeOptions.find((option) => option.name === field.value) || null
                          : null
                      }
                      onChange={(_, value) => {
                        setValue("type", value?.name ?? "");
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Select Type"
                          error={!!errors.type}
                          variant="outlined"
                          InputProps={{
                            ...params.InputProps,
                            style: {
                              padding: "2px",
                              borderRadius: "6px",
                              backgroundColor: "#ffffff",
                            },
                          }}
                          sx={{
                            "& .MuiInputBase-input::placeholder": {
                              opacity: 1,
                            },
                            "& .MuiOutlinedInput-root": {
                              "& fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&:hover fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                              "& input": {
                                padding: "10px",
                              },
                              "&.Mui-error fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused.Mui-error fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                            },
                          }}
                        />
                      )}
                    />
                  )}
                />
                <p className="error-message">{errors.type?.message}</p>
              </div>
            </div>

            <div className="input-field-container">
              <div className="input-field wf-100">
                <label htmlFor="remarks">Remarks</label>
                <input
                  id="remarks"
                  type="text"
                  placeholder="Type..."
                  {...register("remarks", {
                    required: "Please enter remarks.",
                  })}
                />
                <p className="error-message">{errors.remarks?.message}</p>
              </div>
            </div>

           

            <div className="SubmitBtn">
              <button type="submit" className="submitButton">Submit</button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default AddMarkup;