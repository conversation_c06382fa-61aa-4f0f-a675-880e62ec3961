import { loginForm, loginRes, userDetails } from "../models/common.models";
import axiosInstance from "./axiosInstance";

export const login = async (body:loginForm) : Promise<loginRes> =>{
    try{
        const responce = await axiosInstance.post<loginRes>('users/login/',body)
        return responce.data
    } catch (error){
        throw error;
    }
}

export const getUserDetails = async () : Promise<userDetails> =>{
    try{
        const responce = await axiosInstance.get<userDetails>('users/profile',)
        return responce.data
    }catch(error){
        throw error;
    }
}

