@keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
  
  .shimmer-container {
    display: flex;
    flex-direction: column;
    gap: 10px; // Space between rows
  }
  
  .shimmer-row {
    height: 40px; // Row height
    display: flex;
    gap: 10px; // Space between columns
  }
  
  .shimmer-col {
    flex: 1; // Default flex ratio for equal width
    height: 100%; // Inherit height from row
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
    border-radius: 4px; // Optional: Rounded edges
  }
  
  // Responsive styles for narrower screens
  @media (max-width: 768px) {
    .shimmer-row {
      height: 30px;
    }
    .shimmer-col {
      border-radius: 2px;
    }
  }
  