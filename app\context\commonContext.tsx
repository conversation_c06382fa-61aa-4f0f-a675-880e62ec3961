"use client";
import React, { createContext, useContext, useState, ReactNode } from "react";
import { userDetails } from "../models/common.models";

// Define the shape of the context
interface CommonContextProps {
  isMenuExpanded: boolean;
  setIsMenuExpanded: React.Dispatch<React.SetStateAction<boolean>>;
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  currentPage: string;
  setCurrentPage: React.Dispatch<React.SetStateAction<string>>;
  userData: userDetails | undefined;
  setUserData: React.Dispatch<React.SetStateAction<userDetails | undefined>>;
  screenSize: number;
  setScreenSize: React.Dispatch<React.SetStateAction<number>>;
}

// Create the context
const CommonContext = createContext<CommonContextProps | undefined>(undefined);

// Provider Component
export const CommonProvider = ({ children }: { children: ReactNode }) => {
  const [isMenuExpanded, setIsMenuExpanded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [currentPage, setCurrentPage]  = useState('');
  const [userData,setUserData] = useState<userDetails | undefined>()
  const [screenSize, setScreenSize] = useState<number>(() => {
    if (typeof window !== "undefined") {
      return window.innerWidth;
    }
    return 1600; // Default fallback for SSR
  });
  return (
    <CommonContext.Provider value={{ isMenuExpanded, setIsMenuExpanded, isLoading, setIsLoading, currentPage, setCurrentPage , userData , setUserData , screenSize , setScreenSize }}>
      {children}
    </CommonContext.Provider>
  );
};

// Hook for easy access
export const useCommonContext = () => {
  const context = useContext(CommonContext);
  if (!context) {
    throw new Error("useCommonContext must be used within a CommonProvider");
  }
  return context;
};
