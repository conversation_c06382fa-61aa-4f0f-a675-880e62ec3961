'use client'
import '../styles/variables.scss';
import './login.scss';
import { useForm, SubmitHandler } from 'react-hook-form';
import { useState } from 'react';
import Loader from './components/Loader/Loader';

type LoginFormInputs = {
  username:string;
  password:string;
};



export default function Page() {
    const { register, handleSubmit, formState: { errors } } = useForm<LoginFormInputs>();
    const [isPasswordVisible, setIsPasswordVisible] = useState(false); // State for password visibility

    const  onSubmit: SubmitHandler<LoginFormInputs> =async (data) => {
      //setIsLoading(true)
      console.log("data:", data);
      
     
    };
  return (
    <div className="login-container">
      <div className="login-box">
        <h1 className="login-title">Login to Your Account</h1>
        <form className="login-form" onSubmit={handleSubmit(onSubmit)}>
          <div className="form-group">
            <label htmlFor="username">Username</label>
            <input
              type="text"
              id="username"
              {...register('username', { required: 'Username is required' })}
              placeholder="Enter your username"
              className="form-input"
            />
            {errors.username && <p className="error">*{errors.username.message}</p>}
          </div>
          <div className="form-group">
            <label htmlFor="password">Password</label>
            <div className="password-wrapper">
              <input
                type={isPasswordVisible ? 'text' : 'password'} // Toggle password visibility
                id="password"
                {...register('password', { required: 'Password is required' })}
                placeholder="Enter your password"
                className="form-input"
              />
              <button
                type="button"
                className="toggle-password"
                onClick={() => setIsPasswordVisible(!isPasswordVisible)} // Toggle visibility
                aria-label="Toggle password visibility"
              >
                <span className="material-icons">
                  {isPasswordVisible ? 'visibility_off' : 'visibility'}
                </span>
              </button>
            </div>
            {errors.password && <p className="error">*{errors.password.message}</p>}
          </div>
          <button type="submit" className="login-button">
            Login
          </button>
        </form>
      </div>
      <Loader />
    </div>
  );
}
