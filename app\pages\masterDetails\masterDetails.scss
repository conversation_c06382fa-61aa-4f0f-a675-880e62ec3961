:root {
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  --success-50: #f0fdf4;
  --success-500: #22c55e;
  --success-600: #16a34a;
  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 8px 10px -6px rgb(0 0 0 / 0.1);
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --backdrop-blur: blur(8px);
}

.hotelDetailsOverlay {
  position: fixed;
  inset: 0;
  background: rgba(15, 23, 42, 0.4);
  backdrop-filter: var(--backdrop-blur);
  -webkit-backdrop-filter: var(--backdrop-blur);
  display: flex;
  justify-content: flex-end;
  align-items: stretch;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}
.hotelDetails {
  width: 100%;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  box-shadow: var(--shadow-xl);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  animation: slideIn 0.4s ease;
  border-left: 1px solid var(--gray-200);
}
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.hotelDetailsContainer {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}
// .hotelHeader {
//   display: flex;
//   justify-content: space-between;
//   align-items: center;
//   padding: 0.7rem 2.5rem;
//   border-bottom: 1px solid var(--gray-200);
//   background: linear-gradient(135deg, var(--gray-50) 0%, #ffffff 100%);
//   position: sticky;
//   top: 0;
//   z-index: 10;
//   backdrop-filter: var(--backdrop-blur);
//   -webkit-backdrop-filter: var(--backdrop-blur);
//   box-shadow: var(--shadow-sm);
//   h1 {
//     margin: 0;
//     font-size: 1.875rem;
//     font-weight: 700;
//     background: linear-gradient(
//       135deg,
//       var(--gray-900) 0%,
//       var(--gray-700) 100%
//     );
//     -webkit-background-clip: text;
//     -webkit-text-fill-color: transparent;
//     background-clip: text;
//     letter-spacing: -0.025em;
//   }
// }
// .closeButton {
//   background: var(--gray-100);
//   border: 1px solid var(--gray-200);
//   cursor: pointer;
//   padding: 0.75rem;
//   border-radius: var(--radius-xl);
//   display: flex;
//   align-items: center;
//   justify-content: center;
//   color: var(--gray-600);
//   transition: all 0.3s ease;
//   box-shadow: var(--shadow-sm);
//   &:hover {
//     background: var(--error-50);
//     border-color: var(--error-200);
//     color: var(--error-600);
//     transform: scale(1.05);
//     box-shadow: var(--shadow-md);
//   }
//   &:active {
//     transform: scale(0.95);
//   }
// }



.hotelHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.98) 50%,
    rgba(255, 255, 255, 0.95) 100%
  );
  position: sticky;
  top: 0;
  z-index: 100;
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  box-shadow:
    0 1px 3px rgba(0, 0, 0, 0.05),
    0 4px 20px rgba(0, 0, 0, 0.03),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border-radius: 0 0 0.5rem 0.5rem;
  margin-bottom: 0.25rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  /* Dark mode support */
  @media (prefers-color-scheme: dark) {
    background: linear-gradient(
      135deg,
      rgba(15, 23, 42, 0.95) 0%,
      rgba(30, 41, 59, 0.98) 50%,
      rgba(15, 23, 42, 0.95) 100%
    );
    border-bottom: 1px solid rgba(255, 255, 255, 0.06);
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.2),
      0 4px 20px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);
  }

  /* Glassmorphism effect on scroll */
  &.scrolled {
    background: linear-gradient(
      135deg,
      rgba(255, 255, 255, 0.85) 0%,
      rgba(248, 250, 252, 0.9) 50%,
      rgba(255, 255, 255, 0.85) 100%
    );
    backdrop-filter: blur(25px) saturate(200%);
    -webkit-backdrop-filter: blur(25px) saturate(200%);
    box-shadow:
      0 2px 8px rgba(0, 0, 0, 0.08),
      0 8px 32px rgba(0, 0, 0, 0.04),
      inset 0 1px 0 rgba(255, 255, 255, 1);
  }

  .headerContent {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
  }

  .dashboardIcon {
    width: 2rem;
    height: 2rem;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    box-shadow:
      0 4px 14px rgba(59, 130, 246, 0.25),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      transform: translateY(-1px) scale(1.02);
      box-shadow:
        0 6px 20px rgba(59, 130, 246, 0.35),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
  }

  h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 800;
    background: linear-gradient(
      135deg,
      #1e293b 0%,
      #475569 50%,
      #334155 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.03em;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    position: relative;

    /* Dark mode text */
    @media (prefers-color-scheme: dark) {
      background: linear-gradient(
        135deg,
        #f8fafc 0%,
        #e2e8f0 50%,
        #cbd5e1 100%
      );
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    /* Subtle glow effect */
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: inherit;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      filter: blur(1px);
      opacity: 0.3;
      z-index: -1;
    }
  }

  .headerActions {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .actionButton {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(0, 0, 0, 0.06);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #64748b;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.05),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;

    /* Hover ripple effect */
    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
      transition: all 0.6s ease;
      transform: translate(-50%, -50%);
    }

    &:hover {
      background: rgba(255, 255, 255, 0.95);
      border-color: rgba(59, 130, 246, 0.2);
      color: #3b82f6;
      transform: translateY(-1px);
      box-shadow:
        0 4px 12px rgba(0, 0, 0, 0.08),
        0 2px 4px rgba(59, 130, 246, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 1);

      &::before {
        width: 100px;
        height: 100px;
      }
    }

    &:active {
      transform: translateY(0) scale(0.98);
      transition: all 0.1s ease;
    }

    /* Notification badge */
    &.hasNotification {
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 0.25rem;
        right: 0.25rem;
        width: 0.5rem;
        height: 0.5rem;
        background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 0 0 1px rgba(239, 68, 68, 0.3);
        animation: pulse 2s infinite;
      }
    }

    /* Dark mode */
    @media (prefers-color-scheme: dark) {
      background: rgba(30, 41, 59, 0.8);
      border-color: rgba(255, 255, 255, 0.08);
      color: #94a3b8;

      &:hover {
        background: rgba(51, 65, 85, 0.9);
        border-color: rgba(59, 130, 246, 0.3);
        color: #60a5fa;
      }
    }
  }

  .closeButton {
    background: linear-gradient(135deg, rgba(255, 59, 48, 0.1) 0%, rgba(255, 69, 58, 0.05) 100%);
    border: 1px solid rgba(255, 59, 48, 0.15);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ef4444;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
      0 1px 3px rgba(239, 68, 68, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.8);
    font-size: 1rem;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: radial-gradient(circle, rgba(239, 68, 68, 0.15) 0%, transparent 70%);
      transition: all 0.6s ease;
      transform: translate(-50%, -50%);
    }

    &:hover {
      background: linear-gradient(135deg, rgba(255, 59, 48, 0.15) 0%, rgba(255, 69, 58, 0.1) 100%);
      border-color: rgba(239, 68, 68, 0.3);
      color: #dc2626;
      transform: translateY(-1px) scale(1.02);
      box-shadow:
        0 4px 16px rgba(239, 68, 68, 0.2),
        0 2px 4px rgba(239, 68, 68, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.9);

      &::before {
        width: 100px;
        height: 100px;
      }
    }

    &:active {
      transform: translateY(0) scale(0.95);
      transition: all 0.1s ease;
    }
  }
}

/* Animations */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1);
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .hotelHeader {
    padding: 0.5rem 0.75rem;
    border-radius: 0;

    h1 {
      font-size: 1.25rem;
    }

    .dashboardIcon {
      width: 1.75rem;
      height: 1.75rem;
      font-size: 0.875rem;
    }

    .actionButton,
    .closeButton {
      padding: 0.375rem;
      font-size: 0.875rem;
    }

    .headerActions {
      gap: 0.375rem;
    }
  }
}

/* Smooth scroll behavior for sticky header */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar for modern look */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #cbd5e1 0%, #94a3b8 100%);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
}
.hotelDetailsLoading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  gap: 1.5rem;
  .loader {
    width: 3rem;
    height: 3rem;
    border: 3px solid var(--gray-200);
    border-top: 3px solid var(--primary-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  p {
    color: var(--gray-600);
    font-size: 1.125rem;
    font-weight: 500;
    text-align: center;
    margin: 0;
  }
}
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.hotelContent {
  padding: 0 1rem 1rem;
  flex: 1;
  overflow-y: auto;
  scroll-behavior: smooth;
  &::-webkit-scrollbar {
    width: 0.5rem;
  }
  &::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: var(--radius-lg);
  }
  &::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: var(--radius-lg);
    &:hover {
      background: var(--gray-400);
    }
  }
}

.hotelCompactHeader {
  margin: 1rem 0;
  padding: 1rem;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.8),
      transparent
    );
  }
}

.hotelBasicInfo {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
  gap: 1rem;
  h2 {
    margin: 0 0 0.5rem 0;
    font-size: 2rem;
    font-weight: 800;
    background: linear-gradient(
      135deg,
      var(--gray-900) 0%,
      var(--primary-600) 100%
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.025em;
    line-height: 1.2;
  }
}
.hotelImageAndInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}
.hotelLogo {
  border-radius: var(--radius-lg);
  object-fit: cover;
  box-shadow: var(--shadow-md);
  border: 2px solid rgba(255, 255, 255, 0.8);
  transition: transform 0.3s ease;
  &:hover {
    transform: scale(1.05);
  }
}
.hotelLogoPlaceholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: var(--gray-100);
  border-radius: var(--radius-lg);
  color: var(--gray-500);
}

.chainAndRating {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
  span {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
    padding: 0.25rem 0.75rem;
    background: var(--gray-100);
    border-radius: var(--radius-md);
  }
}
.ratingStars {
  display: flex;
  gap: 0.25rem;
  .starFilled,
  .star {
    font-size: 1.125rem;
    transition: transform 0.2s ease;
    &:hover {
      transform: scale(1.2);
    }
  }
}
.starFilled {
  color: var(--warning-500);
  filter: drop-shadow(0 0 4px rgba(245, 158, 11, 0.4));
}
.star {
  color: var(--gray-300);
}
.userRatingInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-top: 0.5rem;
}
.userRating {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--primary-600);
  padding: 0.25rem 0.75rem;
  background: var(--primary-50);
  border-radius: var(--radius-md);
  border: 1px solid var(--primary-200);
}
.userRatingCategory {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--primary-700);
}
.reviewCount {
  font-size: 0.75rem;
  color: var(--gray-500);
  font-weight: 500;
}

.statusAndIds {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.75rem;
}
.statusActive,
.statusUnavailable {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-lg);
  border: 1px solid;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }
}
.statusActive {
  background: rgba(34, 197, 94, 0.1);
  color: var(--success-600);
  border-color: var(--success-200);
  &:hover {
    background: rgba(34, 197, 94, 0.15);
  }
}
.statusUnavailable {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-600);
  border-color: var(--error-200);
  &:hover {
    background: rgba(239, 68, 68, 0.15);
  }
}
.hotelIds {
  font-size: 0.75rem;
  color: var(--gray-500);
  text-align: right;
  font-family: "Monaco", "Menlo", monospace;
  background: var(--gray-50);
  padding: 0.375rem 0.75rem;
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
}

.hotelQuickInfo {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}
.infoItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: var(--gray-600);
  font-weight: 500;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.6);
  border-radius: var(--radius-lg);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  &:hover {
    background: rgba(255, 255, 255, 0.8);
    transform: translateY(-2px);
    box-shadow: var(--shadow-sm);
  }
  svg {
    color: var(--primary-500);
  }
}

.tabNavigation {
  display: flex;
  gap: 0.25rem;
  margin: 1rem 0;
  padding: 0.25rem;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow-x: auto;
  &::-webkit-scrollbar {
    height: 0.25rem;
  }
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  &::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: var(--radius-lg);
  }
}
.tabButton {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  padding: 0.5rem 1rem;
  border: none;
  background: transparent;
  color: var(--gray-600);
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  position: relative;
  overflow: hidden;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(59, 130, 246, 0.1),
      transparent
    );
    transition: left 0.5s ease;
  }
  &:hover {
    color: var(--primary-600);
    background: rgba(59, 130, 246, 0.05);
    transform: translateY(-2px);
    &::before {
      left: 100%;
    }
    svg {
      transform: scale(1.1);
    }
  }
  &.active {
    background: linear-gradient(
      135deg,
      var(--primary-500) 0%,
      var(--primary-600) 100%
    );
    color: white;
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      width: 0.5rem;
      height: 0.125rem;
      background: rgba(255, 255, 255, 0.8);
      border-radius: var(--radius-sm);
    }
    &:hover {
      background: linear-gradient(
        135deg,
        var(--primary-600) 0%,
        var(--primary-700) 100%
      );
      transform: translateY(-3px);
      box-shadow: var(--shadow-lg);
    }
  }
  svg {
    transition: transform 0.3s ease;
    flex-shrink: 0;
  }
}
.tabContent {
  margin-top: 1rem;
  animation: fadeInUp 0.4s ease;
}
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.detailsGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin: 1rem 0;
}
.detailsLeftColumn,
.detailsRightColumn {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
.infoCard,
.amenitiesCard {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(
      90deg,
      var(--primary-500),
      var(--primary-600),
      var(--primary-500)
    );
  }
  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: rgba(59, 130, 246, 0.3);
  }
}

.infoCard h3,
.amenitiesCard h3,
.roomTypesSection h3,
.imagesSection h3 {
  margin: 0;
  padding: 0.75rem 1rem;
  background: linear-gradient(
    135deg,
    var(--gray-50) 0%,
    rgba(255, 255, 255, 0.8) 100%
  );
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  font-size: 1rem;
  font-weight: 700;
  color: var(--gray-800);
  letter-spacing: -0.025em;
  position: relative;
  &::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 2rem;
    right: 2rem;
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent,
      var(--primary-200),
      transparent
    );
  }
}
.infoCardContent {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}
.descriptionBox p {
  margin: 0;
  font-size: 1rem;
  line-height: 1.7;
  color: var(--gray-700);
  font-weight: 400;
}
.contactDetails,
.addressBox {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
.contactDetails h4,
.addressBox h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 700;
  color: var(--gray-800);
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--primary-100);
  position: relative;
  &::after {
    content: "";
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 2rem;
    height: 2px;
    background: var(--primary-500);
  }
}

.contactItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  color: var(--gray-600);
  padding: 0.75rem;
  background: var(--gray-50);
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
  transition: all 0.3s ease;
  &:hover {
    background: var(--primary-50);
    border-color: var(--primary-200);
    transform: translateX(4px);
  }
  svg {
    color: var(--primary-500);
    flex-shrink: 0;
  }
}
.addressBox p {
  margin: 0;
  font-size: 0.875rem;
  line-height: 1.6;
  color: var(--gray-600);
  padding: 1rem;
  background: var(--gray-50);
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
}
.amenitiesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 1rem;
  padding: 2rem;
}
.amenityTag {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.875rem 1rem;
  background: linear-gradient(
    135deg,
    var(--gray-50) 0%,
    rgba(255, 255, 255, 0.8) 100%
  );
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-700);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(59, 130, 246, 0.1),
      transparent
    );
    transition: left 0.5s ease;
  }
  &:hover {
    background: var(--primary-50);
    border-color: var(--primary-300);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    &::before {
      left: 100%;
    }
    svg {
      color: var(--primary-600);
      transform: scale(1.1);
    }
  }
  svg {
    color: var(--primary-500);
    transition: all 0.3s ease;
    flex-shrink: 0;
  }
}

.spotlightCard {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(
      90deg,
      var(--warning-500),
      var(--primary-500),
      var(--warning-500)
    );
  }
  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: rgba(245, 158, 11, 0.3);
  }
  h3 {
    margin: 0;
    padding: 1.5rem 2rem;
    background: linear-gradient(
      135deg,
      var(--warning-50) 0%,
      rgba(255, 255, 255, 0.8) 100%
    );
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--gray-800);
    letter-spacing: -0.025em;
    position: relative;
    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 2rem;
      right: 2rem;
      height: 1px;
      background: linear-gradient(
        90deg,
        transparent,
        var(--warning-200),
        transparent
      );
    }
  }
}
.spotlightItems {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}
.spotlightItem {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.25rem;
  background: linear-gradient(
    135deg,
    var(--warning-50) 0%,
    rgba(255, 255, 255, 0.6) 100%
  );
  border: 1px solid var(--warning-200);
  border-radius: var(--radius-lg);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(245, 158, 11, 0.1),
      transparent
    );
    transition: left 0.5s ease;
  }
  &:hover {
    background: linear-gradient(
      135deg,
      var(--warning-100) 0%,
      rgba(255, 255, 255, 0.8) 100%
    );
    border-color: var(--warning-300);
    transform: translateX(4px);
    box-shadow: var(--shadow-md);
    &::before {
      left: 100%;
    }
    .spotlightIcon {
      transform: scale(1.2) rotate(5deg);
    }
  }
  h4 {
    margin: 0 0 0.5rem 0;
    font-size: 1rem;
    font-weight: 700;
    color: var(--gray-800);
    line-height: 1.3;
  }
  p {
    margin: 0;
    font-size: 0.875rem;
    line-height: 1.5;
    color: var(--gray-600);
  }
}
.spotlightIcon {
  font-size: 2rem;
  line-height: 1;
  flex-shrink: 0;
  transition: transform 0.3s ease;
  filter: drop-shadow(0 2px 4px rgba(245, 158, 11, 0.3));
  margin-top: 0.125rem;
}

.roomTypesSection {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(
      90deg,
      var(--primary-500),
      var(--primary-600),
      var(--primary-500)
    );
  }
  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: rgba(59, 130, 246, 0.3);
  }
}
.room-type-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  background: linear-gradient(
    135deg,
    var(--gray-50) 0%,
    rgba(255, 255, 255, 0.8) 100%
  );
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
  h3 {
    margin: 0;
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--gray-800);
    letter-spacing: -0.025em;
  }
}
.add-room-type-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--primary-600);
  border: 1px solid var(--primary-200);
  border-radius: var(--radius-lg);
  background: var(--primary-50);
  cursor: pointer;
  transition: all 0.3s ease;
  &:hover {
    background: var(--primary-100);
    border-color: var(--primary-300);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }
  &:active {
    transform: translateY(0);
  }
}

.imagesSection {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(
      90deg,
      var(--primary-500),
      var(--success-500),
      var(--primary-500)
    );
  }
  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: rgba(34, 197, 94, 0.3);
  }
  h3 {
    margin: 0;
    padding: 1.5rem 2rem;
    background: linear-gradient(
      135deg,
      var(--success-50) 0%,
      rgba(255, 255, 255, 0.8) 100%
    );
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--gray-800);
    letter-spacing: -0.025em;
    position: relative;
    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 2rem;
      right: 2rem;
      height: 1px;
      background: linear-gradient(
        90deg,
        transparent,
        var(--success-200),
        transparent
      );
    }
  }
}
.roomTypesList {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}
.roomTypeCard {
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  overflow: hidden;
}
.roomTypeHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  h4 {
    margin: 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: #2d3748;
  }
}
.priceTag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.875rem;
  font-weight: 600;
  color: #3b82f6;
}
.roomTypeBody {
  padding: 1rem;
  p {
    margin: 0 0 0.75rem 0;
    font-size: 0.813rem;
    line-height: 1.5;
    color: #4b5563;
  }
}
.roomTypeMetrics {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}
.metric {
  display: flex;
  align-items: center;
  gap: 0.375rem;
  font-size: 0.75rem;
  color: #64748b;
}
.available {
  color: #15803d;
  font-weight: 500;
}
.unavailable {
  color: #b91c1c;
  font-weight: 500;
}
.roomAmenities {
  font-size: 0.75rem;
  color: #64748b;
  line-height: 1.5;
  padding-top: 0.5rem;
  border-top: 1px dashed #e2e8f0;
}
.roomTypeActions {
  position: absolute;
  bottom: 1rem;
  right: 1rem;
  display: flex;
  gap: 0.5rem;
}
.action-btn {
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
  border: none;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all 0.2s ease;
}
.view-btn {
  background: var(--primary-50);
  color: var(--primary-600);
  &:hover {
    background: var(--primary-100);
  }
}
.edit-btn {
  background: var(--gray-50);
  color: var(--gray-600);
  &:hover {
    background: var(--gray-100);
  }
}
.no-room-types {
  text-align: center;
  padding: 2rem;
  color: var(--gray-500);
}
.noImages {
  text-align: center;
  padding: 2rem;
  color: var(--gray-500);
}

.imagesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 1rem;
  padding: 2rem;
  @media screen and (max-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 0.75rem;
    padding: 1.5rem;
  }
}
.imageItem {
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.8);
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  position: relative;
  aspect-ratio: 4/3;
  background: var(--gray-100);
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      135deg,
      transparent 0%,
      rgba(34, 197, 94, 0.1) 100%
    );
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 1;
  }
  &:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow: var(--shadow-lg);
    border-color: var(--success-300);
    &::before {
      opacity: 1;
    }
    .hotelImage {
      transform: scale(1.05);
    }
  }
  &:nth-child(1) {
    grid-column: span 2;
    grid-row: span 2;
    @media screen and (max-width: 768px) {
      grid-column: span 1;
      grid-row: span 1;
    }
  }
}
.hotelImage {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.actionsBar {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 2rem 0 0;
  margin-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
      90deg,
      transparent,
      var(--gray-200),
      transparent
    );
  }
}
.actionButton {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border-radius: var(--radius-lg);
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid;
  position: relative;
  overflow: hidden;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.5s ease;
  }
  &:hover::before {
    left: 100%;
  }
  &:active {
    transform: scale(0.98);
  }
}
.actionButton.primary {
  background: linear-gradient(
    135deg,
    var(--primary-500) 0%,
    var(--primary-600) 100%
  );
  color: white;
  border-color: var(--primary-600);
  box-shadow: var(--shadow-md);
  &:hover {
    background: linear-gradient(
      135deg,
      var(--primary-600) 0%,
      var(--primary-700) 100%
    );
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
  }
}
.actionButton.secondary {
  background: var(--gray-50);
  color: var(--gray-700);
  border-color: var(--gray-300);
  &:hover {
    background: var(--gray-100);
    border-color: var(--gray-400);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }
}
.actionButton.delete {
  background: linear-gradient(135deg, var(--error-50) 0%, #fef2f2 100%);
  color: var(--error-600);
  border-color: var(--error-200);
  &:hover {
    background: linear-gradient(
      135deg,
      var(--error-100) 0%,
      var(--error-50) 100%
    );
    border-color: var(--error-300);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
  }
}
.noDataFound {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  gap: 1.5rem;
  text-align: center;
  p {
    color: var(--gray-600);
    font-size: 1.125rem;
    font-weight: 500;
    margin: 0;
  }
}

/* Modern Responsive Design */
@media screen and (max-width: 1024px) {
  .hotelContent {
    padding: 0 1.5rem 1.5rem;
  }

  .hotelHeader {
    padding: 1.5rem 2rem;
  }

  .hotelCompactHeader {
    padding: 1.5rem;
  }
}

@media screen and (max-width: 768px) {
  .hotelHeader {
    padding: 1rem 1.5rem;

    h1 {
      font-size: 1.5rem;
    }
  }

  .hotelContent {
    padding: 0 1rem 1rem;
  }

  .tabNavigation {
    margin: 1.5rem 0;
    padding: 0.375rem;
    gap: 0.25rem;

    .tabButton {
      padding: 0.75rem 1rem;
      font-size: 0.75rem;

      svg {
        width: 12px;
        height: 12px;
      }
    }
  }

  .detailsGrid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .hotelBasicInfo {
    flex-direction: column;
    gap: 1.5rem;
    align-items: flex-start;
  }

  .statusAndIds {
    align-items: flex-start;
  }

  .hotelQuickInfo {
    flex-direction: column;
    gap: 0.75rem;
  }

  .actionsBar {
    flex-direction: column;
    gap: 1rem;
  }

  .actionButton {
    width: 100%;
    justify-content: center;
  }

  .room-type-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .amenitiesGrid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 0.75rem;
    padding: 1.5rem;
  }

  .hotelCompactHeader {
    margin: 1rem 0;
    padding: 1.5rem;
  }

  .hotelBasicInfo h2 {
    font-size: 1.5rem;
  }

  .ratingRow {
    flex-direction: column;
    gap: 0.75rem;
    align-items: stretch;

    .featureName {
      min-width: auto;
      text-align: center;
    }

    .ratingValue {
      align-self: center;
    }
  }

  .spotlightItem {
    flex-direction: column;
    text-align: center;
    gap: 0.75rem;

    .spotlightIcon {
      align-self: center;
    }
  }
}

@media screen and (max-width: 480px) {
  .hotelHeader {
    padding: 1rem;

    h1 {
      font-size: 1.25rem;
    }
  }

  .hotelContent {
    padding: 0 0.75rem 0.75rem;
  }

  .hotelCompactHeader {
    padding: 1rem;
  }

  .hotelImageAndInfo {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .amenitiesGrid {
    grid-template-columns: 1fr;
  }

  .infoCardContent {
    padding: 1.5rem;
  }
}

.ratingsContent {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 2rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
}
.ratingsOverview {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 2rem;
  @media screen and (max-width: 768px) {
    grid-template-columns: 1fr;
  }
}
.overallRating {
  background: linear-gradient(
    135deg,
    var(--primary-50) 0%,
    rgba(255, 255, 255, 0.8) 100%
  );
  border: 1px solid var(--primary-200);
  border-radius: var(--radius-xl);
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  position: relative;
  overflow: hidden;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(
      90deg,
      var(--primary-500),
      var(--primary-600),
      var(--primary-500)
    );
  }
}
.ratingScore {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
}
.bigRating {
  font-size: 4rem;
  font-weight: 900;
  background: linear-gradient(
    135deg,
    var(--primary-600) 0%,
    var(--primary-500) 100%
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  line-height: 1;
  text-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}
.ratingCategory {
  font-weight: 700;
  font-size: 1.125rem;
  color: var(--primary-700);
  margin-bottom: 0.5rem;
  letter-spacing: -0.025em;
}
.detailedRatings {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  h4 {
    margin: 0 0 1rem 0;
    font-size: 1.125rem;
    font-weight: 700;
    color: var(--gray-800);
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--primary-100);
    position: relative;
    &::after {
      content: "";
      position: absolute;
      bottom: -2px;
      left: 0;
      width: 3rem;
      height: 2px;
      background: var(--primary-500);
    }
  }
}
.ratingRow {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1rem 1.25rem;
  background: linear-gradient(
    135deg,
    var(--gray-50) 0%,
    rgba(255, 255, 255, 0.8) 100%
  );
  border-radius: var(--radius-xl);
  border: 1px solid var(--gray-200);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(59, 130, 246, 0.1),
      transparent
    );
    transition: left 0.5s ease;
  }
  &:hover {
    background: linear-gradient(
      135deg,
      var(--primary-50) 0%,
      rgba(255, 255, 255, 0.9) 100%
    );
    border-color: var(--primary-300);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    &::before {
      left: 100%;
    }
    .ratingBar {
      box-shadow: 0 0 0 2px var(--primary-200);
    }
  }
}
.featureName {
  min-width: 140px;
  font-size: 0.875rem;
  font-weight: 700;
  color: var(--gray-800);
  text-transform: capitalize;
}
.ratingBar {
  flex: 1;
  height: 1rem;
  background: var(--gray-200);
  border-radius: var(--radius-xl);
  overflow: hidden;
  position: relative;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.4),
      transparent
    );
    animation: ratingShimmer 3s infinite;
  }
}
@keyframes ratingShimmer {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(100%);
  }
}
.ratingFill {
  height: 100%;
  background: linear-gradient(
    135deg,
    var(--primary-400) 0%,
    var(--primary-600) 50%,
    var(--primary-500) 100%
  );
  border-radius: var(--radius-xl);
  transition: width 1s ease;
  position: relative;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 50%;
    background: linear-gradient(
      90deg,
      rgba(255, 255, 255, 0.3),
      rgba(255, 255, 255, 0.1)
    );
    border-radius: var(--radius-xl) var(--radius-xl) 0 0;
  }
  &::after {
    content: "";
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    width: 0.25rem;
    height: 0.25rem;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    box-shadow: 0 0 4px rgba(255, 255, 255, 0.8);
    animation: ratingPulse 2s ease-in-out infinite;
  }
}
@keyframes ratingPulse {
  0%,
  100% {
    opacity: 1;
    transform: translateY(-50%) scale(1);
  }
  50% {
    opacity: 0.7;
    transform: translateY(-50%) scale(1.2);
  }
}
.ratingValue {
  font-size: 0.875rem;
  font-weight: 800;
  color: var(--primary-700);
  min-width: 3rem;
  text-align: center;
  padding: 0.375rem 0.75rem;
  background: linear-gradient(
    135deg,
    var(--primary-50) 0%,
    rgba(255, 255, 255, 0.9) 100%
  );
  border-radius: var(--radius-lg);
  border: 2px solid var(--primary-200);
  box-shadow: var(--shadow-sm);
  position: relative;
  &::before {
    content: "";
    position: absolute;
    top: -1px;
    left: -1px;
    right: -1px;
    bottom: -1px;
    background: linear-gradient(135deg, var(--primary-300), var(--primary-400));
    border-radius: var(--radius-lg);
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
  }
  &:hover::before {
    opacity: 1;
  }
}

.guestImpressions {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
}
.impressionSection {
  background-color: #f8fafc;
  border-radius: 0.5rem;
  padding: 1.25rem;
  h4 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
  }
}
.impressionItems {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}
.impressionItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #4b5563;
  background-color: #fff;
  padding: 0.5rem 0.75rem;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}
.impressionIcon {
  color: #3b82f6;
}
.locationContent {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 1.5rem;
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.coordinatesInfo {
  background-color: #f8fafc;
  border-radius: 0.5rem;
  padding: 1.25rem;
  h4 {
    margin: 0 0 0.75rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
  }
  p {
    margin: 0.25rem 0;
    font-size: 0.875rem;
    color: #4b5563;
  }
}
.landmarksSection {
  background-color: #f8fafc;
  border-radius: 0.5rem;
  padding: 1.25rem;
  h4 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
  }
}
.landmarksList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 0.75rem;
}
.landmarkItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #fff;
  padding: 0.75rem;
  border-radius: 0.375rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}
.landmarkName {
  flex: 1;
  font-size: 0.875rem;
  color: #4b5563;
}
.landmarkDistance {
  font-size: 0.75rem;
  font-weight: 500;
  color: #3b82f6;
}

.policiesContent {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  padding: 1.5rem;
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.checkInOutTimes {
  display: flex;
  gap: 2rem;
  background-color: #f8fafc;
  border-radius: 0.5rem;
  padding: 1.25rem;
  @media screen and (max-width: 768px) {
    flex-direction: column;
    gap: 1rem;
  }
}
.timeInfo {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #4b5563;
  strong {
    color: #2d3748;
  }
}
.rulesSection {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}
.ruleCategory {
  background-color: #f8fafc;
  border-radius: 0.5rem;
  padding: 1.25rem;
  h4 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e2e8f0;
  }
}
.subRule {
  margin-bottom: 1rem;
  h5 {
    margin: 0 0 0.5rem 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: #4b5563;
  }
  ul {
    margin: 0;
    padding-left: 1.5rem;
  }
  li {
    font-size: 0.875rem;
    color: #4b5563;
    margin-bottom: 0.25rem;
  }
}
.pricingContent {
  padding: 1.5rem;
  background-color: #fff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
.fareBreakdown {
  background-color: #f8fafc;
  border-radius: 0.5rem;
  padding: 1.25rem;
  max-width: 500px;
  h4 {
    margin: 0 0 1rem 0;
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e2e8f0;
  }
}
.fareItem {
  display: flex;
  justify-content: space-between;
  padding: 0.75rem 0;
  border-bottom: 1px dashed #e2e8f0;
  font-size: 0.875rem;
  color: #4b5563;
  &.discount {
    color: #16a34a;
  }
  &.instant {
    color: #16a34a;
  }
  &.total {
    border-bottom: none;
    padding-top: 1rem;
    font-size: 1rem;
    color: #1e40af;
  }
}

// Actions Bar
.actionsBar {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1.5rem 0;
  margin-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.3);
}

.actionButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;

  &.secondary {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    box-shadow: 0 4px 14px rgba(245, 158, 11, 0.25);

    &:hover {
      background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(245, 158, 11, 0.35);
    }
  }

  &.delete {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
    box-shadow: 0 4px 14px rgba(239, 68, 68, 0.25);

    &:hover {
      background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(239, 68, 68, 0.35);
    }
  }

  &:active {
    transform: translateY(0);
  }
}

// Spotlight Card
.spotlightCard {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(
      90deg,
      var(--primary-500),
      var(--primary-600),
      var(--primary-500)
    );
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
    border-color: rgba(59, 130, 246, 0.3);
  }

  h3 {
    margin: 0;
    padding: 0.75rem 1rem;
    background: linear-gradient(
      135deg,
      var(--gray-50) 0%,
      rgba(255, 255, 255, 0.8) 100%
    );
    border-bottom: 1px solid rgba(255, 255, 255, 0.3);
    font-size: 1rem;
    font-weight: 700;
    color: var(--gray-800);
    letter-spacing: -0.025em;
    position: relative;

    &::after {
      content: "";
      position: absolute;
      bottom: 0;
      left: 1rem;
      right: 1rem;
      height: 1px;
      background: linear-gradient(
        90deg,
        transparent,
        var(--primary-200),
        transparent
      );
    }
  }
}

.spotlightItems {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.spotlightItem {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 0.5rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;

  &:hover {
    background: rgba(255, 255, 255, 0.8);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }

  .spotlightIcon {
    font-size: 1.5rem;
    flex-shrink: 0;
  }

  h4 {
    margin: 0 0 0.25rem 0;
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--gray-800);
  }

  p {
    margin: 0;
    font-size: 0.75rem;
    color: var(--gray-600);
    line-height: 1.4;
  }
}

// No Data Found
.noDataFound {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;

  p {
    font-size: 1.125rem;
    color: var(--gray-600);
    margin: 0;
  }
}
