@import '/styles/variables.scss';

// .overall-overview-container{
//     width: 100%;
//     display: flex;
//     flex-wrap: wrap;
//     flex-direction: row;
//     justify-content: space-between;
//     gap: 15px;
//     background-color: $white_color1;
//     //padding: 15px 0 0 0 ;

    

//     .overall-overview{
//         width: calc((100% - 60px) / 4);
//         min-width: 250px;
//         //height: 100px;
//         display: flex;
//         flex-direction: column;
//         background-color: $white_color;
//         border: 1px solid $black_color4;
//         border-radius: 5px;

//         @media (max-width: $breakpoint-md) {
//             width: -webkit-fill-available;
//          }

//         .header{
//             display: flex;
//             flex-direction: row;
//             justify-content: space-between;
//             align-items: center;
//             padding: 10px 15px;
//             border-bottom: 1px solid $black_color4 ;
//             //flex: 0 0 auto;
//             h3{
//                 display: flex;
//                 align-items: center;
//                 font-weight: 500;
//                 font-size: 14px;
                

//                 span{
//                     padding: 2px 3px;
//                     border-radius: 6px;
//                     margin-right: 8px;
//                     font-size: 18px;
                   
//                 }
                
//                 .groups{
//                     background-color: $green_color3;
//                     color: $green_color2;
//                     border: 1px solid $green_color;
                   
//                 }

//                 .person{
//                     background-color: $red_color3;
//                     color: $red_color;
//                     border: 1px solid $red_color2;
//                     padding: 2px 3px;
//                     border-radius: 6px;

//                 }

//                 .group{
//                     background-color: #9286DD1A;
//                     color: #9286DD;
//                     border: 1px solid #9286DD66;
//                     padding: 2px 3px;
//                     border-radius: 6px;
//                 }

//                 .group2{
//                     background-color: #D65CE01A;
//                     color:#D65CE0;
//                     border: 1px solid #D65CE066;
//                     padding: 2px 3px;
//                     border-radius: 6px;
//                 }

            

             
//             }

//             .morehorizIcon{
//                 cursor: pointer;
//             }

//             @media (max-width: $breakpoint-md) {
//                 padding: 8px;
        
//                 h3 {
//                     font-size: 12px;
        
//                     span {
//                         font-size: 16px;
//                         margin-right: 6px;
//                     }
//                 }
        
//                 .morehorizIcon {
//                     font-size: 20px; 
//                 }
//             }
        
//             @media (max-width: $breakpoint-sm) {
//                 flex-direction: column;
//                 align-items: flex-start;
//                 gap: 8px;
        
//                 h3 {
//                     font-size: 12px;
        
//                     span {
//                         font-size: 14px;
//                         //margin-right: 4px;
//                     }
//                 }
        
//                 .morehorizIcon {
//                     font-size: 18px;
//                 }
//             }
//         }
//         .content{
//             display: flex;
//             justify-content: start;
//             align-items: center;
//             //flex: 1;
//             padding: 20px 15px;

//             h2{
//                 font-weight: 500;
//                 padding-right: 10px;
//                 font-size: 30px;
              
//             }

         

//                .raise{
//                 color: $green_color2;
//                 background-color: $green_color3;
//                 border: 1px solid $green_color;
//                 border-radius: 3px;
//                 display: flex;
//                 align-items: center;
//                 font-size: 11px;
//                 padding: .8px 2px;

//                 .arrow-upward{
//                     padding-left: 2px;
//                     font-size: 10px;
//                 }
           

          
//             }


//             p{
//                 padding-left: 5px;
//                 font-size: 12px;
//                 color: $black_color2;
//             }

//             .fall{
//                 color: $red_color;
//                 background-color: $red_color3;
//                 border: 1px solid $red_color2;
//                 border-radius: 3px;
//                 display: flex;
//                 align-items: center;
//                 font-size: 11px;
//                 padding: .8px 2px;

//                 .arrow-upward{
//                     padding-left: 2px;
//                     font-size: 10px;
//                 }
           

          
//             }

    

    
//             @media (max-width: $breakpoint-md) {
//                 flex-direction: column;
//                 align-items: flex-start;
//                 padding: 15px 10px;
        
//                 h2 {
//                     font-size: 24px;
//                     padding-right: 0;
//                     margin-bottom: 8px; // Adds spacing when stacked
//                 }
        
//                 .raise,
//                 .fall {
//                     font-size: 10px;
//                     padding: 0.6px 2px;
        
//                     .arrow-upward {
//                         font-size: 8px;
//                     }
//                 }
        
//                 p {
//                     font-size: 10px;
//                     padding-left: 0;
//                     padding-top: 5px;
//                 }
//             }
        
//             @media (max-width: $breakpoint-sm) {
//                 h2 {
//                     font-size: 20px;
//                 }
        
//                 .raise,
//                 .fall {
//                     font-size: 9px;
//                     padding: 0.4px 2px;
        
//                     .arrow-upward {
//                         font-size: 7px;
//                     }
//                 }
        
//                 p {
//                     font-size: 9px;
//                     padding-top: 5px;
//                 }
//             }
        
//         }
//     }


// }

// .overall-overview-container {
//     width: 100%;
//     display: flex;
//     flex-wrap: wrap;
//     flex-direction: row;
//     justify-content: space-between;
//     gap: 15px;
//     background-color: $white_color1;

//     .overall-overview {
//         width: calc((100% - 60px) / 4);
//         min-width: 250px;
//         display: flex;
//         flex-direction: column;
//         background-color: $white_color;
//         border: 1px solid $black_color4;
//         border-radius: 5px;
//         opacity: 0;
//         transform: translateY(20px);
//         transition: opacity 0.5s ease, transform 0.5s ease;
        
//         // Animation to make the cards slide and fade in
//         &.visible {
//             opacity: 1;
//             transform: translateY(0);
//         }

//         @media (max-width: $breakpoint-md) {
//             width: -webkit-fill-available;
//         }

//         .header {
//             display: flex;
//             flex-direction: row;
//             justify-content: space-between;
//             align-items: center;
//             padding: 10px 15px;
//             border-bottom: 1px solid $black_color4;
//             transition: padding 0.3s ease;

//             h3 {
//                 display: flex;
//                 align-items: center;
//                 font-weight: 500;
//                 font-size: 14px;
//                 transition: font-size 0.3s ease;

//                 span {
//                     padding: 2px 3px;
//                     border-radius: 6px;
//                     margin-right: 8px;
//                     font-size: 18px;
//                 }

//                 .groups, .person, .group, .group2 {
//                     padding: 2px 3px;
//                     border-radius: 6px;
//                 }

//                 .groups {
//                     background-color: $green_color3;
//                     color: $green_color2;
//                     border: 1px solid $green_color;
//                 }

//                 .person {
//                     background-color: $red_color3;
//                     color: $red_color;
//                     border: 1px solid $red_color2;
//                 }

//                 .group {
//                     background-color: #9286DD1A;
//                     color: #9286DD;
//                     border: 1px solid #9286DD66;
//                 }

//                 .group2 {
//                     background-color: #D65CE01A;
//                     color: #D65CE0;
//                     border: 1px solid #D65CE066;
//                 }
//             }

//             .morehorizIcon {
//                 cursor: pointer;
//                 transition: transform 0.2s ease;
//             }

//             @media (max-width: $breakpoint-md) {
//                 padding: 8px;
//                 h3 {
//                     font-size: 12px;
//                     span {
//                         font-size: 16px;
//                         margin-right: 6px;
//                     }
//                 }

//                 .morehorizIcon {
//                     font-size: 20px;
//                     &:hover {
//                         transform: rotate(90deg); // Rotate icon on hover
//                     }
//                 }
//             }

//             @media (max-width: $breakpoint-sm) {
//                 flex-direction: column;
//                 align-items: flex-start;
//                 gap: 8px;

//                 h3 {
//                     font-size: 12px;
//                 }

//                 .morehorizIcon {
//                     font-size: 18px;
//                 }
//             }
//         }

//         .content {
//             display: flex;
//             justify-content: start;
//             align-items: center;
//             padding: 20px 15px;
//             transition: padding 0.3s ease;

//             h2 {
//                 font-weight: 500;
//                 padding-right: 10px;
//                 font-size: 30px;
//             }

//             .raise, .fall {
//                 color: $green_color2;
//                 background-color: $green_color3;
//                 border: 1px solid $green_color;
//                 border-radius: 3px;
//                 display: flex;
//                 align-items: center;
//                 font-size: 11px;
//                 padding: .8px 2px;
//                 transition: transform 0.2s ease;

//                 .arrow-upward {
//                     padding-left: 2px;
//                     font-size: 10px;
//                 }
//             }

//             p {
//                 padding-left: 5px;
//                 font-size: 12px;
//                 color: $black_color2;
//             }

//             @media (max-width: $breakpoint-md) {
//                 flex-direction: column;
//                 align-items: flex-start;
//                 padding: 15px 10px;

//                 h2 {
//                     font-size: 24px;
//                     margin-bottom: 8px;
//                 }

//                 .raise,
//                 .fall {
//                     font-size: 10px;
//                     padding: 0.6px 2px;
//                     .arrow-upward {
//                         font-size: 8px;
//                     }
//                 }

//                 p {
//                     font-size: 10px;
//                     padding-left: 0;
//                     padding-top: 5px;
//                 }
//             }

//             @media (max-width: $breakpoint-sm) {
//                 h2 {
//                     font-size: 20px;
//                 }

//                 .raise,
//                 .fall {
//                     font-size: 9px;
//                     padding: 0.4px 2px;
//                     .arrow-upward {
//                         font-size: 7px;
//                     }
//                 }

//                 p {
//                     font-size: 9px;
//                     padding-top: 5px;
//                 }
//             }
//         }
//     }
// }




// Variables - moved to top for better management
$transition-standard: 0.3s ease;
$card-border-radius: 8px;
$card-spacing: 16px;

// Mixins for reusable styles
@mixin flex-layout($direction: row, $justify: flex-start, $align: center, $wrap: nowrap) {
  display: flex;
  flex-direction: $direction;
  justify-content: $justify;
  align-items: $align;
  flex-wrap: $wrap;
}

@mixin badge($bg-color, $text-color, $border-color) {
  background-color: $bg-color;
  color: $text-color;
  border: 1px solid $border-color;
  border-radius: 6px;
  padding: 4px 6px;
  font-size: 0.875rem;
}

@mixin responsive-text($desktop-size, $tablet-size: null, $mobile-size: null) {
  font-size: $desktop-size;
  
  @if $tablet-size {
    @media (max-width: $breakpoint-md) {
      font-size: $tablet-size;
    }
  }
  
  @if $mobile-size {
    @media (max-width: $breakpoint-sm) {
      font-size: $mobile-size;
    }
  }
}

// Main container styles
.overall-overview-container {
  width: 100%;
  @include flex-layout(row, space-between, flex-start, wrap);
  gap: $card-spacing;
  background-color: $white_color1;
  padding: $card-spacing;
  
  // Individual card styles
  .overall-overview {
    width: calc((100% - #{$card-spacing * 3}) / 4);
    min-width: 250px;
    @include flex-layout(column);
    background-color: $white_color;
    border: 1px solid $black_color4;
    border-radius: $card-border-radius;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    transition: transform $transition-standard, box-shadow $transition-standard, opacity $transition-standard;
    opacity: 0;
    transform: translateY(20px);
    
    // Animation class
    &.visible {
      opacity: 1;
      transform: translateY(0);
      
      &:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
    }
    
    // Card header section
    .header {
      @include flex-layout(row, space-between, center);
      padding: 12px 16px;
      border-bottom: 1px solid $black_color4;
      
      h3 {
        @include flex-layout;
        @include responsive-text(0.9rem, 0.8rem, 0.75rem);
        font-weight: 600;
        margin: 0;
        
        span {
          margin-right: 8px;
          @include responsive-text(1.1rem, 1rem, 0.9rem);
          display: flex;
          align-items: center;
        }
        
        // Badge styles
        .groups { @include badge($green_color3, $green_color2, $green_color); }
        .person { @include badge($red_color3, $red_color, $red_color2); }
        .group { @include badge(#9286DD1A, #9286DD, #9286DD66); }
        .group2 { @include badge(#D65CE01A, #D65CE0, #D65CE066); }
      }
      
      .morehorizIcon {
        cursor: pointer;
        transition: transform $transition-standard;
        
        &:hover {
          transform: rotate(90deg);
        }
      }
    }
    
    // Card content section
    .content {
      @include flex-layout;
      padding: 16px;
      
      h2 {
        font-weight: 600;
        padding-right: 10px;
        @include responsive-text(1.875rem, 1.5rem, 1.25rem);
        margin: 0;
      }
      
      // Trend indicators
      .trend-indicator {
        @include flex-layout;
        padding: 2px 4px;
        border-radius: 4px;
        font-weight: 500;
        @include responsive-text(0.75rem, 0.7rem, 0.65rem);
      }
      
      .raise {
        @extend .trend-indicator;
        color: $green_color2;
        background-color: $green_color3;
        border: 1px solid $green_color;
        
        .arrow-upward {
          margin-left: 2px;
          font-size: 0.7em;
        }
      }
      
      .fall {
        @extend .trend-indicator;
        color: $red_color;
        background-color: $red_color3;
        border: 1px solid $red_color2;
        
        .arrow-downward {
          margin-left: 2px;
          font-size: 0.7em;
        }
      }
      
      p {
        margin: 0;
        padding-left: 5px;
        @include responsive-text(0.75rem, 0.7rem, 0.65rem);
        color: $black_color2;
      }
    }
    
    // Responsive styles
    @media (max-width: $breakpoint-md) {
      width: calc(50% - #{$card-spacing / 2});
      
      .header {
        padding: 10px 12px;
      }
      
      .content {
        @include flex-layout(column, center, flex-start);
        padding: 12px;
        
        h2 {
          margin-bottom: 8px;
        }
        
        p {
          padding-left: 0;
          padding-top: 5px;
        }
      }
    }
    
    @media (max-width: $breakpoint-sm) {
      width: 100%;
      
      .header {
        @include flex-layout(column, center, flex-start);
        gap: 8px;
      }
      
      .content {
        padding: 10px 12px;
      }
    }
  }
}