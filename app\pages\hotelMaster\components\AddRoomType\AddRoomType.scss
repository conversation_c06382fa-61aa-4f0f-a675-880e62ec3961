@import '/styles/variables.scss';

.agent-create-container {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(3px);
    z-index: -1;
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease, z-index 0s linear 0.3s;
    overflow-y: auto;
    padding: 15px;

    &.show {
        opacity: 1;
        z-index: 1000;
        transition: opacity 0.3s ease;
    }

    .agent-create-form {
        background-color: $white_color;
        padding: 25px;
        position: relative;
        border-radius: 8px;
        box-shadow: 0 15px 45px rgba(0, 0, 0, 0.12);
        max-height: 90vh;
        overflow-y: auto;

        // Full width styling
        &.full-width {
            width: 100%;
            max-width: 900px;
            min-width: 600px;

            @media (max-width: 1024px) {
                min-width: 85vw;
                max-width: 90vw;
            }

            @media (max-width: 768px) {
                min-width: 90vw;
                max-width: 95vw;
                padding: 15px;
            }
        }

        h3 {
            text-align: center;
            margin-bottom: 20px;
            color: darken($black_color2, 15%);
            font-weight: 600;
            font-size: 20px;

            @media (max-width: 768px) {
                font-size: 18px;
                margin-bottom: 15px;
            }
        }

        .closeIcon {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 16px;
            cursor: pointer;
            font-weight: 600;
            color: $black_color3;
            padding: 6px;
            border-radius: 50%;
            transition: all 0.2s ease;

            &:hover {
                background-color: rgba(0, 0, 0, 0.1);
                color: $black_color;
            }
        }

        .form-content {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .form-row {
            display: flex;
            gap: 15px;
            width: 100%;

            &.double {
                .input-field-container {
                    flex: 1;
                }
            }

            @media (max-width: 768px) {
                flex-direction: column;
                gap: 0;
            }
        }

        .input-field-container {
            flex: 1;
            display: flex;
            flex-direction: column;

            .input-field {
                display: flex;
                flex-direction: column;
                width: 100%;

                label {
                    font-size: 12px;
                    color: $black_color2;
                    font-weight: 600;
                    margin-bottom: 6px;
                    display: block;
                }

                input, textarea, select {
                    padding: 8px 12px;
                    border: 2px solid $black_color4;
                    border-radius: 6px;
                    font-size: 13px;
                    font-family: inherit;
                    transition: border-color 0.2s ease, box-shadow 0.2s ease;
                    width: 100%;
                    box-sizing: border-box;

                    &:focus {
                        outline: none;
                        border-color: $primary_color;
                        box-shadow: 0 0 0 2px rgba($primary_color, 0.1);
                    }

                    &::placeholder {
                        color: $black_color4;
                        font-size: 12px;
                    }

                    &.error {
                        border-color: #dc3545;
                        box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.1);
                    }
                }

                select {
                    cursor: pointer;
                    background-color: white;

                    option {
                        padding: 8px;
                    }
                }

                textarea {
                    resize: vertical;
                    min-height: 60px;
                    line-height: 1.4;
                }

                input[type="time"] {
                    cursor: pointer;
                }

                // Checkbox grid styles
                .checkbox-grid {
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 12px;
                    margin-top: 8px;

                    @media (max-width: 768px) {
                        grid-template-columns: 1fr;
                        gap: 8px;
                    }
                }

                .checkbox-item {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    padding: 8px 12px;
                    border: 1px solid $black_color4;
                    border-radius: 6px;
                    background-color: $white_color1;
                    transition: all 0.2s ease;

                    &:hover {
                        border-color: $primary_color;
                        background-color: rgba($primary_color, 0.02);
                    }

                    input[type="checkbox"] {
                        width: auto;
                        margin: 0;
                        cursor: pointer;
                        accent-color: $primary_color;
                    }

                    label {
                        margin: 0;
                        font-size: 13px;
                        font-weight: 500;
                        cursor: pointer;
                        color: $black_color2;
                    }
                }

                // Error message styles
                .error-message {
                    color: #dc3545;
                    font-size: 11px;
                    margin-top: 4px;
                    font-weight: 500;
                }
            }
        }

        .amenities-list {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .amenity-row {
            display: flex;
            align-items: center;
            gap: 8px;

            input {
                flex: 1;
                padding: 8px 12px;
                border: 2px solid $black_color4;
                border-radius: 6px;
                font-size: 13px;
                transition: border-color 0.2s ease;

                &:focus {
                    outline: none;
                    border-color: $primary_color;
                    box-shadow: 0 0 0 2px rgba($primary_color, 0.1);
                }

                &::placeholder {
                    color: $black_color4;
                    font-size: 12px;
                }
            }

            .remove-amenity-btn {
                background: none;
                border: none;
                color: $black_color4;
                cursor: pointer;
                padding: 6px;
                border-radius: 4px;
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                justify-content: center;

                &:hover {
                    color: #dc3545;
                    background-color: rgba(220, 53, 69, 0.1);
                }
            }
        }

        .addMoreField {
            display: flex;
            justify-content: flex-start;
            margin-bottom: 8px;

            p {
                font-size: 12px;
                text-decoration: underline;
                text-underline-offset: 2px;
                color: $primary_color;
                font-weight: 600;
                transition: color 0.2s ease;
                cursor: pointer;
                margin: 0;

                &:hover {
                    color: darken($primary_color, 10%);
                }
            }
        }

        .fileUpload {
            background-color: $white_color1;
            border: 2px dotted $black_color4;
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            flex-direction: column;
            padding: 25px 15px;
            margin: 15px 0;
            transition: all 0.2s ease;
            cursor: pointer;

            &:hover {
                border-color: $primary_color;
                background-color: rgba($primary_color, 0.02);
            }

            .uploadIcon {
                color: $black_color2;
                margin-bottom: 8px;

                span {
                    font-size: 28px;
                    display: block;
                }
            }

            .desc {
                font-size: 13px;
                color: $black_color;
                font-weight: 600;
                margin-bottom: 4px;

                span {
                    color: $primary_color;
                    text-decoration: underline;
                    text-underline-offset: 2px;

                    &:hover {
                        color: darken($primary_color, 10%);
                    }
                }
            }

            .fileFormat {
                font-size: 11px;
                color: $black_color3;
                font-weight: 500;
            }
        }

        .SubmitBtn {
            width: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 20px;

            .submit-button {
                background-color: $primary_color;
                color: white;
                border: none;
                padding: 12px 30px;
                border-radius: 6px;
                cursor: pointer;
                font-weight: 600;
                font-size: 14px;
                transition: all 0.2s ease;
                min-width: 160px;

                &:hover {
                    background-color: darken($primary_color, 8%);
                    transform: translateY(-1px);
                    box-shadow: 0 3px 10px rgba($primary_color, 0.3);
                }

                &:active {
                    transform: translateY(0);
                }

                @media (max-width: 768px) {
                    width: 100%;
                    min-width: unset;
                }
            }
        }
    }

    // Responsive adjustments
    @media (max-width: 768px) {
        padding: 8px;
        align-items: flex-start;
        padding-top: 15px;
    }
}


.fileUpload {
  cursor: pointer;
  transition: all 0.3s ease;

  &.drag-active {
    border-color: #007bff;
    background-color: #f8f9ff;
  }

  &:hover {
    border-color: #007bff;
  }
}

.selected-images {
  margin-top: 20px;

  .images-preview {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 10px;
  }

  .image-preview-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    background-color: #f9f9f9;

    .image-info {
      display: flex;
      align-items: center;
      gap: 10px;

      .image-name {
        font-weight: 500;
      }

      .image-size {
        color: #666;
        font-size: 0.9em;
      }
    }

    .remove-image-btn {
      background: #ff4757;
      color: white;
      border: none;
      border-radius: 50%;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;

      &:hover {
        background: #ff3838;
      }
    }
  }
}