@use "sass:color";
@use '/styles/variables' as *;

.navbar{
    width: 100%;
    height: 80px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    background-color: $white_color1;
    
    
    // @media(max-width: 560px){
    //     flex-direction: column-reverse;
    //     margin-top: 10px;
    //     padding-left: 10px;
    // }

    .navbar-item1, .navbar-item2{
        display: flex;
        flex-direction: row;
        align-items: center;

       .userType{
        
        color: $primary_color;
        padding: 8px 0 8px 35px;
        font-size: 18px;
        font-weight: 500;

        
     
       }

        
    }

    .navbar-item2{

            transform: translateX(100px);
            opacity: 0;
            transition: opacity 0.5s ease, transform 0.5s ease;

            &.visible{
                transform: translateY(0);
                opacity: 1;
                z-index: 500;
            }
        

        .searchIcon{
            width: 38px;
            height: 38px;
            background-color: #F9F9F9;
            justify-content: center;
            align-items: center;
            display: none;
            border-radius: 50%;
            cursor: pointer;
            position: relative;
            margin-right: 10px;

            .fa-magnifying-glass{
                color: $primary_color;
             }

            @media(max-width: 650px){
                display: flex;
            }


            .search-box-dropdown{
                padding: 4px 10px;
                width: 250px;
                height: auto;
                background-color: $white_color;
                display: flex;
                align-items: center;
                gap: 8px;
                border: 1px solid $black_color4;
                border-radius: 20px;
                position: absolute;
                top: 46px;
                left: 0px;
                z-index: 1000;



                .search-icon{
                    color: #9A9A9A;
                }


                input{
                    //padding: 9px 10px;
                    width: 100%;
                    height: 30px;
                    background-color: transparent;
                    border: 0;
                    outline: none;
                }


                input::placeholder{
                    color: #9A9A9A;
                    font-weight: 500;
                }

            
                
            }
 
        }

        .search-box-overlay{
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            z-index: 999;

        }

        
        .searchBar{
            display: flex;
            gap: 15px;
            align-items: center;
            border-right: 1px solid $black_color4;
            padding-right: 15px;

            

            // @media(max-width: 560px){
            //    width: 100%;
            // }
            
            .search-box{
                padding: 6px 12px;
                width: 320px;
                height: auto;
                background-color: $white_color;
                display: flex;
                align-items: center;
                gap: 8px;
                border: 1px solid $black_color4;
                border-radius: 20px;

                @media(max-width: 650px){
                    display: none;
                }

              



                .search-icon{
                    color: #9A9A9A;
                }



                input{
                    //padding: 9px 10px;
                    width: 100%;
                    height: 30px;
                    background-color: transparent;
                    border: 0;
                    outline: none;
                }


                input::placeholder{
                    color: #9A9A9A;
                    font-weight: 500;
                }

            
                
            }

       

            .notificationIcon {
                width: 38px;
                height: 38px;
                background-color: $white_color;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 50%;
                cursor: pointer;
                position: relative; 

                span {
                  font-size: 18px;
                }
              
                .notificationCount {
                  position: absolute;
                  top: -4px;
                  right: -4px; 
                  width: 18px;
                  height: 18px;
                  background-color: $red_color; 
                  color: $white_color;
                  font-size: 12px;
                  border-radius: 50%;
                  display: flex;
                  justify-content: center;
                  align-items: center;
                  font-weight: bold;
                }
              }
              

            @media(max-width: $breakpoint-md) {
                .search-box {
                    width: 100%; 
                    padding: 4px 8px; 
                }
        
                .notificationIcon {
                    width: 35px; 
                    height: 35px;
                  
                }
            }

            @media(max-width: $breakpoint-xs) {
                .search-box {
                    width: 100%;  
                }
        
                .notificationIcon {
                    width: 30px; 
                    height: 30px;
                    span {
                        font-size: 16px;
                    }
                }
            }
        }

        .userProfile{
            padding: 0 15px 0 15px;
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 12px;

            // transform: translateX(100px);
            // opacity: 0;
            // transition: opacity 0.5s ease, transform 0.5s ease;

            // &.visible{
            //     transform: translateY(0);
            //     opacity: 1;
            //     z-index: 500;
            // }

            
            @media(max-width : 880px){
                padding: 0 0 0 15px;
            }

            .dropdown {
                display: flex;
                flex-direction: column;
                justify-content: center;
                background-color: $white_color;
                border: 1px solid #ccc;
                box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
                padding: 10px;
                border-radius: 5px;
                width: 150px;
                z-index: 10;
                gap: 10px;
              }

              

              .dropdown-item {
                background-color: $white_color;
                padding: 5px 10px;
                width: 130px;
                height: 25px;               
                display: flex;
                align-items: center;
                cursor: pointer;
                font-size: 10px;
                font-weight: 600;
                border-radius: 15px;
                color: $black_color;
                transition: background-color 0.2s ease, color 0.2s ease ;

                .logoutIcon{
                    font-size: 12px;
                    margin-right: 5px;
                }

                &:hover{
                    background-color: $primary_color;
                    color: $white_color;
                    
                }

                .logout{
                    text-decoration: none;
                    
                    

                   
                }
                
              }
              
           
              
              .dropdown-list {
                //padding: 10px;
         

              
                .dropdown-username {
                  font-weight: 600;
                  margin-bottom: 5px;
                  font-size: 14px;
                }
              
                .dropdown-email {
                  font-size: 11px;
                  color: $black_color2;
                  margin-bottom: 8px;
                }

                .dropdown-logout{
               
                    display: flex;
                    align-items: center;
                    justify-content: start;

                    .logout{
                        cursor: pointer;
                        text-decoration: none;
                        font-size: 13px;
                        margin-right: 5px;

                        &:hover{
                            text-decoration: underline;
                        }
                    }

                    .logoutIcon{
                        font-size: 20px;
                        color: $red_color;
                        cursor: pointer;
                    }
                }
              }

              
              
              @media (min-width: 881px) {
                .dropdown-list {
                  display: none;
                }
              }
              

            .userIcon{
                width: 38px;
                height: 38px;
                background-color: $white_color;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 50%;
                cursor: pointer;

                &:hover{
                    background-color: color.adjust(#f9f9f9, $lightness: -10%);
        
                }
             

                span{
                    font-size: 20px;
                    color: $primary_color;
                    cursor: pointer;
                  
                }
            }

            .userDetails{
                display: flex;
                flex-direction: column;
                justify-content: center;
                align-items: start;

                .username{
                    font-size: 14px;
                    font-weight: 600;
                    margin-bottom: 2px;

                }
                .email{
                    font-size: 12px;
                    font-weight: 400;
                    color: $black_color2;

                }
            }

            .arrow-down-icon{
                span{
                    cursor: pointer;

                    
            @media(max-width : 880px){
                display: none;
            }
                }
            }

        }
    }

    .menuIcon{
        width: 38px;
        height: 38px;
        background-color: #F9F9F9;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 50%;
        cursor: pointer;
        transition: color 0.3s ease;

        &:hover{
            background-color: color.adjust(#f9f9f9, $lightness: -10%);
        }

        .fa-bars{
            color: $primary_color;
            cursor: pointer;
        }
    }

 
}

//media queries

@media(max-width: 500px){
    .navbar{
        flex-direction: column-reverse;
        height: auto;
        padding: 10px;

        .navbar-item1, .navbar-item2{
            .userType{
                padding: 8px 0 8px 20px;
                font-size: 16px;
            }
    
        }

        .navbar-item2{
            justify-content: end;

            .userProfile .userDetails .username {
                font-size: 12px;   
        }

        .userProfile .userDetails .email {
            font-size: 9px;   
    }

    }
}

    

    
}