// Variables
$primary-color: #0ea5e9;
$primary-dark: #0284c7;
$secondary-color: #64748b;
$success-color: #10b981;
$warning-color: #f59e0b;
$danger-color: #ef4444;
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;
$white: #ffffff;
$black: #000000;

// Mixins
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin card-shadow {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

@mixin hover-scale {
  transition: all 0.2s ease;
  &:hover {
    transform: scale(1.01);
  }
}

// Room Details Full Overlay - Maximum Priority to Hide Navbar
.room-details-full-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  width: 100vw !important;
  height: 100vh !important;
  background-color: rgba(0, 0, 0, 0.95) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  z-index: 999999 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 1rem !important;

  &.hidden {
    display: none !important;
  }

  &.block {
    display: flex !important;
  }
}

// Ensure modal content is above everything
.room-details-full-overlay > div {
  z-index: 1000000 !important;
  position: relative !important;
}

// Ensure close button is always clickable
.room-details-full-overlay button {
  z-index: 1000001 !important;
  position: relative !important;
}

// Override any potential navbar or header z-index
.room-details-full-overlay {
  // Force overlay above all possible elements
  z-index: 2147483647 !important; // Maximum safe z-index value

  // Ensure it covers everything including potential fixed headers
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.95);
    z-index: -1;
  }
}

// Main overlay
.room-details-overlay {
  position: fixed;
  inset: 0;
  background: linear-gradient(135deg, rgba(17, 24, 39, 0.9), rgba(0, 0, 0, 0.7));
  backdrop-filter: blur(8px);
  z-index: 50;
  @include flex-center;
  padding: 1rem;

  &.hidden {
    display: none;
  }
}

// Main container
.room-details-container {
  width: 100%;
  max-width: 80rem;
  max-height: 90vh;
  background: $white;
  border-radius: 0.75rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  border: 1px solid $gray-200;

  // Close button
  .close-button {
    position: absolute;
    top: 1.5rem;
    right: 1.5rem;
    z-index: 20;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    border-radius: 9999px;
    @include card-shadow;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;

    &:hover {
      background: $white;
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .close-icon {
      color: $gray-600;
      transition: color 0.2s ease;
    }

    &:hover .close-icon {
      color: $gray-800;
    }
  }
}

// Scrollable content
.scrollable-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;

  .content-wrapper {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
}

// Hero header
.hero-header {
  position: relative;
  background: linear-gradient(135deg, #38bdf8, #0ea5e9);
  border-radius: 0.75rem;
  padding: 1.5rem;
  color: $white;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(135deg, rgba(56, 189, 248, 0.2), transparent);
  }

  .hero-content {
    position: relative;
    z-index: 10;

    .hero-main {
      display: flex;
      flex-direction: column;
      gap: 1rem;

      @media (min-width: 1024px) {
        flex-direction: row;
        justify-content: space-between;
        align-items: flex-start;
      }
    }

    .hero-left {
      display: flex;
      align-items: flex-start;
      gap: 1rem;

      .hero-icon {
        background: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(4px);
        padding: 0.75rem;
        border-radius: 0.5rem;
        flex-shrink: 0;
      }

      .hero-info {
        h1 {
          font-size: 1.5rem;
          font-weight: 700;
          margin-bottom: 0.5rem;
        }

        .location-info {
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          gap: 0.5rem;
          color: rgb(186, 230, 253);

          .location-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
          }
        }

        .room-id {
          margin-top: 0.25rem;
          font-size: 0.875rem;
          color: rgb(186, 230, 253);
        }
      }
    }

    .availability-badge {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      background: rgba(16, 185, 129, 0.2);
      backdrop-filter: blur(4px);
      color: rgb(167, 243, 208);
      padding: 0.25rem 0.75rem;
      border-radius: 9999px;
      font-size: 0.875rem;
      flex-shrink: 0;
      border: 1px solid rgba(52, 211, 153, 0.3);
      font-weight: 500;
    }

    .hero-stats {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      margin-top: 1rem;

      .stat-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(4px);
        padding: 0.5rem 0.75rem;
        border-radius: 0.5rem;

        .stat-price {
          font-weight: 700;
        }

        .stat-suffix {
          color: rgb(186, 230, 253);
        }
      }
    }
  }
}

// Room options
.room-options {
  .section-title {
    font-size: 1.25rem;
    font-weight: 700;
    color: $gray-900;
    margin-bottom: 1rem;
  }

  .options-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0.75rem;

    @media (min-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  .option-card {
    position: relative;
    padding: 1rem;
    border-radius: 0.5rem;
    border: 2px solid $gray-200;
    text-align: left;
    transition: all 0.2s ease;
    cursor: pointer;
    background: $white;

    &:hover {
      border-color: rgb(147, 197, 253);
      background: $gray-50;
      @include card-shadow;
    }

    &.selected {
      border-color: $primary-color;
      background: rgb(240, 249, 255);
      @include card-shadow;
      transform: scale(1.01);
    }

    .option-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 0.5rem;

      h3 {
        font-weight: 600;
        color: $gray-900;
      }

      .recommended-badge {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        background: rgb(254, 243, 199);
        color: rgb(180, 83, 9);
        padding: 0.25rem 0.5rem;
        border-radius: 9999px;
        font-size: 0.75rem;
        font-weight: 500;
      }
    }

    .option-price {
      font-size: 1.25rem;
      font-weight: 700;
      color: $primary-color;
      margin-bottom: 0.5rem;
    }

    .payment-type {
      font-size: 0.875rem;
      color: $gray-600;
      margin-bottom: 0.5rem;
    }

    .fomo-tag {
      display: inline-block;
      font-size: 0.75rem;
      background: #f97316;
      color: $white;
      padding: 0.25rem 0.5rem;
      border-radius: 9999px;
      font-weight: 500;
    }
  }
}

// Main content grid
.main-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: 1.5rem;

  @media (min-width: 1024px) {
    grid-template-columns: repeat(3, 1fr);
  }
}

// Gallery section
.gallery-section {
  @media (min-width: 1024px) {
    grid-column: span 1;
  }

  .gallery-card {
    background: $white;
    border-radius: 0.75rem;
    @include card-shadow;
    border: 1px solid $gray-100;
    overflow: hidden;

    .gallery-header {
      padding: 1rem;
      border-bottom: 1px solid $gray-100;

      h3 {
        font-size: 1.125rem;
        font-weight: 600;
        color: $gray-900;
        display: flex;
        align-items: center;
        gap: 0.5rem;

        .gallery-icon {
          color: $primary-color;
        }
      }
    }

    .gallery-main {
      position: relative;

      .main-image {
        width: 100%;
        height: 16rem;
        object-fit: cover;
      }

      .nav-button {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: rgba(0, 0, 0, 0.5);
        color: $white;
        padding: 0.5rem;
        border-radius: 9999px;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: rgba(0, 0, 0, 0.7);
        }

        &.prev {
          left: 0.75rem;
        }

        &.next {
          right: 0.75rem;
        }
      }

      .image-counter {
        position: absolute;
        bottom: 0.75rem;
        right: 0.75rem;
        background: rgba(0, 0, 0, 0.5);
        color: $white;
        padding: 0.25rem 0.5rem;
        border-radius: 9999px;
        font-size: 0.75rem;
      }
    }

    .gallery-caption {
      padding: 0.75rem;
      text-align: center;

      p {
        color: $gray-600;
        font-size: 0.875rem;
      }
    }

    .gallery-thumbnails {
      display: flex;
      gap: 0.5rem;
      padding: 0.75rem;
      padding-top: 0;

      .thumbnail {
        flex: 1;
        height: 3rem;
        border-radius: 0.375rem;
        overflow: hidden;
        border: 2px solid $gray-200;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          border-color: $gray-300;
        }

        &.active {
          border-color: $primary-color;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }
}

// Room details section
.room-details-section {
  @media (min-width: 1024px) {
    grid-column: span 1;
  }

  .section-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .details-card {
    background: $white;
    border-radius: 0.75rem;
    @include card-shadow;
    border: 1px solid $gray-100;
    padding: 1rem;

    h3 {
      font-size: 1.125rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: $gray-900;
    }

    .properties-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 0.75rem;

      .property-item {
        background: $gray-50;
        padding: 0.75rem;
        border-radius: 0.5rem;

        .property-label {
          font-size: 0.75rem;
          color: $gray-600;
          margin-bottom: 0.25rem;
        }

        .property-value {
          font-weight: 600;
          color: $gray-900;
          font-size: 0.875rem;
        }
      }
    }

    .extra-bed-info {
      background: rgb(240, 249, 255);
      border: 1px solid rgb(186, 230, 253);
      padding: 0.75rem;
      border-radius: 0.5rem;

      .extra-bed-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: rgb(3, 105, 161);

        .extra-bed-title {
          font-weight: 500;
          font-size: 0.875rem;
        }
      }

      .extra-bed-text {
        color: rgb(7, 89, 133);
        font-size: 0.75rem;
        margin-top: 0.25rem;
      }
    }

    .availability-info {
      background: rgb(236, 253, 245);
      border: 1px solid rgb(167, 243, 208);
      padding: 0.75rem;
      border-radius: 0.5rem;

      .availability-header {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        color: rgb(4, 120, 87);
        margin-bottom: 0.25rem;

        .availability-title {
          font-weight: 500;
          font-size: 0.875rem;
        }
      }

      .availability-count {
        color: rgb(5, 150, 105);
        font-weight: 600;
        font-size: 0.875rem;
      }
    }
  }

  .amenities-card {
    .amenities-grid {
      display: grid;
      grid-template-columns: repeat(1, 1fr);
      gap: 0.5rem;

      .amenity-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem;
        background: $gray-50;
        border-radius: 0.5rem;
        transition: background-color 0.2s ease;

        &:hover {
          background: $gray-100;
        }

        .amenity-text {
          color: $gray-900;
          font-size: 0.875rem;
        }
      }
    }
  }

  .benefits-card {
    .benefits-content {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;

      .benefit-section {
        h4 {
          font-weight: 500;
          color: $gray-900;
          margin-bottom: 0.5rem;
          display: flex;
          align-items: center;
          gap: 0.5rem;
          font-size: 0.875rem;
        }

        .benefit-list {
          display: flex;
          flex-direction: column;
          gap: 0.25rem;

          .benefit-item {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem;
            border-radius: 0.5rem;
            font-size: 0.875rem;

            &.meal-benefit {
              color: rgb(180, 83, 9);
              background: rgb(254, 243, 199);
            }

            &.other-benefit {
              color: rgb(3, 105, 161);
              background: rgb(240, 249, 255);
            }
          }
        }
      }
    }
  }
}

// Pricing section
.pricing-section {
  @media (min-width: 1280px) {
    grid-column: span 1;
  }

  .section-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .pricing-card {
    background: $white;
    border-radius: 1rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border: 1px solid $gray-100;
    overflow: hidden;

    .pricing-header {
      background: linear-gradient(135deg, #2563eb, #1d4ed8);
      padding: 1.5rem;
      color: $white;

      h3 {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1rem;
      }

      .price-display {
        text-align: center;

        .main-price {
          font-size: 2.25rem;
          font-weight: 700;
          margin-bottom: 0.5rem;
        }

        .price-suffix {
          color: rgb(191, 219, 254);
        }
      }
    }

    .pricing-breakdown {
      padding: 1.5rem;

      .breakdown-list {
        display: flex;
        flex-direction: column;
        gap: 0.75rem;

        .breakdown-item {
          display: flex;
          justify-content: space-between;
          padding: 0.5rem 0;

          .item-label {
            color: $gray-600;
          }

          .item-value {
            font-weight: 500;
          }

          &.savings {
            color: $success-color;
          }

          &.total {
            border-top: 1px solid $gray-200;
            padding-top: 0.75rem;
            font-size: 1.125rem;
            font-weight: 700;

            .item-value {
              color: #2563eb;
            }
          }
        }
      }

      .discounts-section {
        background: rgb(236, 253, 245);
        border: 1px solid rgb(167, 243, 208);
        padding: 1rem;
        border-radius: 0.5rem;
        margin-top: 1rem;

        h4 {
          font-weight: 500;
          color: rgb(4, 120, 87);
          margin-bottom: 0.5rem;
        }

        .discount-list {
          display: flex;
          flex-direction: column;
          gap: 0.25rem;

          .discount-item {
            display: flex;
            justify-content: space-between;
            color: rgb(5, 150, 105);
            font-size: 0.875rem;
          }
        }
      }
    }
  }

  .cancellation-card {
    background: $white;
    border-radius: 1rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border: 1px solid $gray-100;
    padding: 1.5rem;

    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: $gray-900;
    }

    .policy-summary {
      background: $gray-50;
      padding: 1rem;
      border-radius: 0.5rem;
      margin-bottom: 1rem;

      p {
        color: $gray-700;
        font-weight: 500;
      }
    }

    .policy-details {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;

      .policy-item {
        border: 1px solid $gray-200;
        padding: 1rem;
        border-radius: 0.5rem;

        .policy-title {
          font-weight: 500;
          color: $gray-900;
          margin-bottom: 0.5rem;
        }

        .policy-subtitle {
          font-size: 0.875rem;
          color: $gray-600;
          margin-bottom: 0.5rem;
        }

        .policy-dates {
          font-size: 0.75rem;
          // color: $gray-500;
          margin-bottom: 0.5rem;
        }

        .policy-fee {
          font-size: 0.875rem;
          font-weight: 500;
          color: $danger-color;
        }
      }
    }
  }

  .actions-card {
    background: $white;
    border-radius: 1rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border: 1px solid $gray-100;
    padding: 1.5rem;

    h3 {
      font-size: 1.25rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: $gray-900;
    }

    .actions-list {
      display: flex;
      flex-direction: column;
      gap: 0.75rem;

      .action-button {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.5rem;
        padding: 0.75rem 1rem;
        border-radius: 0.75rem;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        font-weight: 500;

        &:hover {
          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        &.disable {
          background: $gray-600;
          color: $white;

          &:hover {
            background: $gray-700;
          }
        }

        &.delete {
          background: $danger-color;
          color: $white;

          &:hover {
            background: #dc2626;
          }
        }
      }
    }
  }
}

// Confirmation dialog
.confirmation-dialog {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  @include flex-center;
  z-index: 50;
  padding: 1rem;

  .dialog-content {
    background: $white;
    padding: 2rem;
    border-radius: 1rem;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    max-width: 28rem;
    width: 100%;
    border: 1px solid $gray-200;

    h3 {
      font-size: 1.5rem;
      font-weight: 600;
      margin-bottom: 1rem;
      color: $gray-900;
    }

    p {
      color: $gray-600;
      margin-bottom: 2rem;
      font-size: 1.125rem;
    }

    .dialog-actions {
      display: flex;
      gap: 1rem;

      .dialog-button {
        flex: 1;
        padding: 0.75rem 1.5rem;
        border-radius: 0.75rem;
        border: none;
        cursor: pointer;
        transition: all 0.2s ease;
        font-weight: 500;

        &.cancel {
          border: 2px solid $gray-300;
          color: $gray-700;
          background: $white;

          &:hover {
            background: $gray-50;
            border-color: $gray-400;
          }
        }

        &.confirm {
          background: $danger-color;
          color: $white;

          &:hover {
            background: #dc2626;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
          }
        }
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .room-details-container {
    margin: 0.5rem;
    max-height: 95vh;
  }

  .scrollable-content {
    padding: 1rem;
  }

  .hero-header {
    padding: 1rem;

    .hero-content .hero-main {
      flex-direction: column;
      gap: 1rem;
    }

    .hero-stats {
      gap: 1rem;

      .stat-item {
        font-size: 0.875rem;
      }
    }
  }

  .main-content {
    grid-template-columns: 1fr;
  }

  .room-options .options-grid {
    grid-template-columns: 1fr;
  }
}