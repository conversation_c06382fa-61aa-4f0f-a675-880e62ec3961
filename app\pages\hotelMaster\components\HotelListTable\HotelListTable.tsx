import React from "react";

import Image from "next/image";
import Link from "next/link";
import TableWithShimmer from "@/app/components/TableWithShimmer/TableWithShimmer";
import Pagination from "@/app/utilities/Pagination/Pagination";
import TableMenuTwo from "@/app/components/TableMenuTwo/TableMenuTwo";
import SearchBox from "@/app/components/SearchBox/SearchBox";
import profilePic from "@/public/images/profile.png";
import { DropdownItem, Hotel } from "./Page";
import page from "@/app/pages/page";

interface HotelListTableProps {
  hotels: Hotel[];
  filteredHotels: Hotel[];
  paginatedHotels: Hotel[];
  isTableLoading: boolean;
  searchValue: string;
  handleSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  status: string;
  handleFilterSelect: (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
    item: string
  ) => void;
  selectedDate: string;
  isFilterOpen: boolean;
  handleToggleFilter: () => void;
  handleMenuItemClick: (item: DropdownItem, id: number) => void;
  itemsPerPage: number;
  currentPage: number;
  totalPages: number;
  handlePageChange: (pageNo: number) => void;
  handleItemsPerPageChange: (value: number) => void;
   pageName?: string;
  showFilter?: boolean;
}

const HotelListTable: React.FC<HotelListTableProps> = ({
  paginatedHotels,
  isTableLoading,
  searchValue,
  handleSearchChange,
  status,
  handleFilterSelect,
  selectedDate,
  isFilterOpen,
  handleToggleFilter,
  handleMenuItemClick,
  itemsPerPage,
  currentPage,
  totalPages,
  handlePageChange,
  handleItemsPerPageChange,
  pageName,
  showFilter,
}) => {
  return (
    <div className="user-list-table-container">
      <div className="tableBorder">
        <div className="table-header">
          <h5>Hotel List</h5>

          {showFilter && (
             <div className="filter-search-container">
            <div className="filterButton" onClick={handleToggleFilter}>
              <button>
                <i className="fa-solid fa-filter"></i>
              </button>
            </div>

            <div
              className={`filter-options-select-box ${
                isFilterOpen ? "show" : ""
              }`}
            >
              <div className="filterOption">
                <span>Status {status ? `(${status})` : ""}</span>
                <select
                  className="dropdown"
                  value={status}
                  onChange={(e) => handleFilterSelect(e, "status")}
                >
                  <option value="">None</option>
                  <option value="in_progress">In Progress</option>
                  <option value="failed">Failed</option>
                  <option value="success">Success</option>
                </select>
                <span className="material-icons">keyboard_arrow_down</span>
              </div>
              <input
                className="date-picker"
                type="date"
                value={selectedDate}
                onChange={(e) => handleFilterSelect(e, "date")}
              />
            </div>

            <SearchBox
              value={searchValue}
              onChange={handleSearchChange}
              placeholders={["Search by hotel name"]}
            />
          </div>
          )
          
          }

          
        </div>

        {isTableLoading ? (
          <TableWithShimmer
            no_of_rows={itemsPerPage < 8 ? itemsPerPage : 8}
            no_of_cols={6}
            colWidths={[1.5, 1.5, 1, 1]}
          />
        ) : (
          <div className="table-style table-vertical-scroll">
            <table>
              <thead>
                <tr>
                  {pageName === "hotelMaster" && (
                    <>
                  <th>Hotel Name</th>
                  <th>Current Status</th>
                  <th>Hotel ID</th>
                  <th>Location</th>
                  <th>Chain Name</th>
                  <th>Details</th>
                  <th>Actions</th>
                  </>
                  )}

                  {pageName === "homepage" && (
                    <>
                       <th>Hotel Name</th>
                  <th>Current Status</th>
                  <th>Hotel ID</th>
                  <th>Location</th>
              

                    </>
                    )}
                
                </tr>
              </thead>

              <tbody>
                {paginatedHotels.length > 0 ? (
                  paginatedHotels.map((hotel) => (


                    <tr key={hotel.id}>

                      {pageName === "hotelMaster" && (
                        <>
                              <td>
                        <div className="name-profilePic">
                          <div className="profile-pic">
                            <Image
                              src={profilePic}
                              alt="Hotel Logo"
                              fill
                              className="profileImage"
                              style={{ objectFit: "cover" }}
                              sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                              loading="lazy"
                            />
                          </div>
                          <div className="name">{hotel.name}</div>
                        </div>
                      </td>

                      <td>
                        <div className={hotel.status === "Available" ? "available" : "offline"}>
                          <span>{hotel.status}</span>
                        </div>
                      </td>

                      <td>
                        <span className="empId">{hotel.id}</span>
                      </td>
                      
                      <td>
                        <span className="walletAmount">
                          {hotel.location}
                        </span>
                      </td>
                      
                      <td>
                        <span className="contact">{hotel.chainName}</span>
                      </td>
                      
                      <td>
                        <div className="viewDetails">
                          <Link
                            href=""
                            onClick={(e) => {
                              e.preventDefault();
                              // handleShowDetails(hotel.id);
                            }}
                          >
                            View Details
                          </Link>
                        </div>
                      </td>
                      
                      <td onClick={(e) => e.stopPropagation()}>
                        <TableMenuTwo
                          items={[
                            { id: "edit-hotel", label: "Edit Hotel" },
                            {
                              id: hotel.status === "Available" ? "disable-hotel" : "enable-hotel",
                              label: hotel.status === "Available" ? "Disable Hotel" : "Enable Hotel",
                            },
                            { id: "delete-hotel", label: "Delete Hotel" },
                          ]}
                          onClick={(item) => handleMenuItemClick(item, hotel.id)}
                          id={hotel.id}
                        />
                      </td>
                        </>)}


                      {pageName === "homepage" && (
                        <>
                            <td>
                        <div className="name-profilePic">
                          <div className="profile-pic">
                            <Image
                              src={profilePic}
                              alt="Hotel Logo"
                              fill
                              className="profileImage"
                              style={{ objectFit: "cover" }}
                              sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                              loading="lazy"
                            />
                          </div>
                          <div className="name">{hotel.name}</div>
                        </div>
                      </td>

                      <td>
                        <div className={hotel.status === "Available" ? "available" : "offline"}>
                          <span>{hotel.status}</span>
                        </div>
                      </td>

                      <td>
                        <span className="empId">{hotel.id}</span>
                      </td>
                      
                      <td>
                        <span className="walletAmount">
                          {hotel.location}
                        </span>
                      </td>
                      
                     
                      
                   
                        </>
                      )}

                  
                      
                    
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={7} className="no-data2">
                      <h5>
                        {!searchValue && !status &&
                          `It looks like you don't have a hotel yet.`}
                        {(searchValue || status) &&
                          `No hotel matches your search for "${searchValue}".`}
                      </h5>
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        )}

        <Pagination
          totalPages={totalPages}
          handlePage={handlePageChange}
          itemsPerPage={itemsPerPage}
          page={currentPage}
          handleItemsPerPageChange={handleItemsPerPageChange}
        />
      </div>
    </div>
  );
};

export default HotelListTable;


