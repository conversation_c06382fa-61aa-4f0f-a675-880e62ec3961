// BookingsList Component (components/BookingsList/BookingsList.tsx)
import React from 'react';

const BookingsList: React.FC = () => {
  return (
    <div className="table-card">
      <h2 className="table-title">Bookings List</h2>
      <div className="table-container">
        <table className="data-table">
          <thead>
            <tr>
              <th>Booking ID</th>
              <th>Guest Name</th>
              <th>Payment Status</th>
              <th>Booking Status</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>BK001</td>
              <td>
                <div className="user-cell">
                  <div className="user-avatar">
                    <i className="fas fa-user"></i>
                  </div>
                  <div className="user-name">John <PERSON></div>
                </div>
              </td>
              <td>
                <span className="status-badge paid">Paid</span>
              </td>
              <td className="status-confirmed">Confirmed</td>
            </tr>
            <tr>
              <td>BK002</td>
              <td>
                <div className="user-cell">
                  <div className="user-avatar">
                    <i className="fas fa-user"></i>
                  </div>
                  <div className="user-name">Sarah Johnson</div>
                </div>
              </td>
              <td>
                <span className="status-badge pending">Pending</span>
              </td>
              <td className="status-awaiting">Awaiting Payment</td>
            </tr>
            <tr>
              <td>BK003</td>
              <td>
                <div className="user-cell">
                  <div className="user-avatar">
                    <i className="fas fa-user"></i>
                  </div>
                  <div className="user-name">Michael Smith</div>
                </div>
              </td>
              <td>
                <span className="status-badge paid">Paid</span>
              </td>
              <td className="status-confirmed">Confirmed</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default BookingsList;