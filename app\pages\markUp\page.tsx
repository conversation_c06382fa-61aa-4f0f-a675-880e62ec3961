"use client";
import React, { useEffect, useState, useMemo } from "react";
import { useRouter } from "next/navigation";
import "./style.scss";
import { useAlert } from "@/app/utilities/Alert/Alert";
import AddMarkup from "./AddMarkup/AddMarkup";
import MarkupTable from "./components/MarkupTable/MarkupTable";
import { Markup } from "./components/MarkupTable/MarkupTable";

export interface DropdownItem {
  id: string;
  label: string;
}

function Page() {
  const router = useRouter();
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [showDetails, setShowDetails] = useState<boolean>(false);
  const [visible, setVisible] = useState<boolean>(false);
  const [isTableLoading, setIsTableLoading] = useState<boolean>(false);
  const [status, setStatus] = useState<string>("");
  const [selectedDate, setSelectedDate] = useState<string>("");
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState<string>("");
  const [showCreate, setShowCreate] = useState<boolean>(false);
  const [editMarkup, setEditMarkup] = useState<Markup | null>(null);
  const [markupList, setMarkupList] = useState<Markup[]>([]);

  const { fire } = useAlert();
  
  // Fetch markup data - in a real app this would come from an API
  useEffect(() => {
    // Simulate loading state
    setIsTableLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      const sampleMarkups: Markup[] = [
        {
          id: 1,
          name: "Hotel ABC",
          type: "Percentage",
          value: "15%",
          status: "Active",
          markup_code: "ABC123",
          remarks: "Standard markup for premium hotels"
        },
        {
          id: 2,
          name: "Resort XYZ",
          type: "Flat",
          value: "₹500",
          status: "Active",
          markup_code: "XYZ456",
          remarks: "Resort markup for peak season"
        },
        {
          id: 3,
          name: "Hotel Grand",
          type: "Percentage",
          value: "10%",
          status: "Inactive",
          markup_code: "GRD789",
          remarks: "Standard markup for budget hotels"
        }
      ];
      
      setMarkupList(sampleMarkups);
      setTotalPages(Math.ceil(sampleMarkups.length / itemsPerPage));
      setIsTableLoading(false);
    }, 1000);
  }, [itemsPerPage]);

  // Filter markups based on search and filters
  const filteredMarkups = useMemo(() => {
    return markupList.filter(markup => {
      // Filter by search value
      const matchesSearch = searchValue ? 
        markup.name.toLowerCase().includes(searchValue.toLowerCase()) || 
        (markup.markup_code && markup.markup_code.toLowerCase().includes(searchValue.toLowerCase())) : 
        true;
      
      // Filter by status
      const matchesStatus = status ? markup.status.toLowerCase() === status.toLowerCase() : true;
      
      // Filter by date (placeholder - in a real app you'd have created_at or updated_at fields)
      const matchesDate = selectedDate ? true : true; // Simplified for this example
      
      return matchesSearch && matchesStatus && matchesDate;
    });
  }, [markupList, searchValue, status, selectedDate]);

  // Update total pages when filtered results change
  useEffect(() => {
    setTotalPages(Math.ceil(filteredMarkups.length / itemsPerPage));
    // Reset to first page if current page is greater than new total pages
    if (currentPage > Math.ceil(filteredMarkups.length / itemsPerPage)) {
      setCurrentPage(1);
    }
  }, [filteredMarkups, itemsPerPage, currentPage]);

  // Get current items based on pagination
  const currentItems = useMemo(() => {
    const indexOfLastItem = currentPage * itemsPerPage;
    const indexOfFirstItem = indexOfLastItem - itemsPerPage;
    return filteredMarkups.slice(indexOfFirstItem, indexOfLastItem);
  }, [filteredMarkups, currentPage, itemsPerPage]);

  const handleToggleFilter = () => {
    setIsFilterOpen((prev) => !prev);
  };

  const handleFilterSelect = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
    item: string
  ) => {
    const value = event.target.value;
    if (item === "status") setStatus(value);
    else if (item === "date") setSelectedDate(value);
  };

  const handleCreate = () => {
    // Reset edit markup when creating a new one
    setEditMarkup(null);
    setShowCreate(true);
  };

  const handleEdit = (id: number) => {
    // Find the markup by id
    const markupToEdit = markupList.find(markup => markup.id === id);
    if (markupToEdit) {
      setEditMarkup(markupToEdit);
      setShowCreate(true);
    }
  };

  const handleShowDetails = (id: number) => {
    // In a real app, you might navigate to a details page
    console.log("Show details for markup id:", id);
    setShowDetails(true);
    // Or you could redirect:
    // router.push(`/markups/${id}`);
  };

  const handlePageChange = (pageNo: number) => {
    setCurrentPage(pageNo);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  const handleCloseModal = () => {
    setShowCreate(false);
    setEditMarkup(null);
  };

  const handleSaveMarkup = (newMarkup: Markup) => {
    if (editMarkup) {
      // Update existing markup
      setMarkupList(prevList => 
        prevList.map(item => item.id === editMarkup.id ? { ...newMarkup, id: editMarkup.id } : item)
      );
    } else {
      // Add new markup with a generated id
      const maxId = markupList.length > 0 ? Math.max(...markupList.map(item => item.id)) : 0;
      const markupWithId = { ...newMarkup, id: maxId + 1 };
      setMarkupList(prevList => [...prevList, markupWithId]);
    }
    
    // Close modal after saving
    handleCloseModal();
  };

  const handleDeleteMarkup = (id: number) => {
    fire({
      position: "center",
      icon: "info",
      title: `Are you sure you want to delete this markup?`,
      text: "This action cannot be undone!",
      confirmButtonText: "Yes",
      cancelButtonText: "No",
      onConfirm: async () => {
        // Delete the markup
        setMarkupList(prevList => prevList.filter(item => item.id !== id));
      },
    });
  };

  const handleToggleStatus = (item: DropdownItem, id: number) => {
    fire({
      position: "center",
      icon: "info",
      title: `Are you sure?`,
      text: `To ${item.id === "disable-agent" ? "disable" : "enable"} this markup.`,
      confirmButtonText: "Yes",
      cancelButtonText: "No",
      onConfirm: async () => {
        // Toggle the status of the markup
        setMarkupList(prevList => prevList.map(markup => {
          if (markup.id === id) {
            const newStatus = markup.status === "Active" ? "Inactive" : "Active";
            return { ...markup, status: newStatus };
          }
          return markup;
        }));
      },
    });
  };

  const handleMenuItemClick = (item: DropdownItem, id: number) => {
    switch (item.id) {
     
      case "edit-agent":
        handleEdit(id);
        break;
      case "disable-agent":
      case "enable-agent":
        handleToggleStatus(item, id);
        break;
      case "delete-agent":
        handleDeleteMarkup(id);
        break;
    }
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(true);
    }, 500);
    return () => clearTimeout(timer);
  }, []);

  return (
    <>
      <div className={`agentList overall-list-padding ${visible ? "visible" : ""}`}>
          <div className="addUser">
          <div className="addButton" onClick={handleCreate}>
            Add Markup +
          </div>
        </div>
        <MarkupTable
          isTableLoading={isTableLoading}
          markupList={markupList}
          searchValue={searchValue}
          handleSearchChange={handleSearchChange}
          status={status}
          selectedDate={selectedDate}
          handleFilterSelect={handleFilterSelect}
          isFilterOpen={isFilterOpen}
          handleToggleFilter={handleToggleFilter}
          currentPage={currentPage}
          itemsPerPage={itemsPerPage}
          currentItems={currentItems}
          handleShowDetails={handleShowDetails}
          handleMenuItemClick={handleMenuItemClick}
          totalPages={totalPages}
          handlePageChange={handlePageChange}
          handleItemsPerPageChange={handleItemsPerPageChange}
        />
      </div>
      {showCreate && (
        <AddMarkup 
          showCreate={showCreate} 
          handleCloseCreate={handleCloseModal} 
          editData={editMarkup}
          isEditMode={!!editMarkup}
          onSave={handleSaveMarkup}
        />
      )}
    </>
  );
}

export default Page;