// import React, { useState, useEffect } from "react";
// import { FileRejection, useDropzone } from "react-dropzone";
// import Image from "next/image";
// import './image-uploader.scss'

// interface ImageUploaderProps {
//   uploadedImage: string | File | null;
//   isOpen?: boolean;
//   onModalClose?: () => void;
//   allowedTypes: string[];
//   maxSize: number;
//   onImageUpload: (file: File | null) => void;
//   label: string;
//   requiredMessage: string;
//   maxFileSizeMessage: string;
//   invalidTypeMessage: string;
// }

// const ImageUploader: React.FC<ImageUploaderProps> = ({
//   uploadedImage,
//   isOpen = true,
//   onModalClose,
//   allowedTypes,
//   maxSize,
//   onImageUpload,
//   label,
//   maxFileSizeMessage,
//   invalidTypeMessage,
// }) => {
//   const [error, setError] = useState<string | null>(null);
//   const [preview, setPreview] = useState<string | null>(null);
//   const [modalOpen, setModalOpen] = useState<boolean>(isOpen);
//   const media_base_url = process.env.NEXT_PUBLIC_MEDIA_PATH;

//   const onDrop = (acceptedFiles: File[], fileRejections: FileRejection[]) => {
//     if (fileRejections.length) {
//       const rejectionErrors = fileRejections
//         .map(({ file, errors }) => {
//           return errors
//             .map((error) => {
//               if (error.code === "file-too-large") {
//                 return `${maxFileSizeMessage}: ${file.name}`;
//               }
//               if (error.code === "file-invalid-type") {
//                 return `${invalidTypeMessage}: ${file.name}`;
//               }
//               return null;
//             })
//             .filter(Boolean)
//             .join(", ");
//         })
//         .join(", ");
//       setError(rejectionErrors);
//       onImageUpload(null);
//       return;
//     }

//     const errors = acceptedFiles.map((file) => {
//       if (!allowedTypes.includes(file.type)) {
//         return `${invalidTypeMessage}: ${file.name}`;
//       }
//       if (file.size > maxSize) {
//         return `${maxFileSizeMessage}: ${file.name}`;
//       }
//       return null;
//     }).filter(Boolean);

//     if (errors.length) {
//       setError(errors.join(", "));
//       onImageUpload(null);
//       return;
//     }

//     setError(null);
//     onImageUpload(acceptedFiles[0]);
//   };

//   const { getRootProps, getInputProps, open } = useDropzone({
//     onDrop,
//     accept: allowedTypes.reduce((acc, type) => {
//       acc[type] = []; // Populate each type as an object key with an empty array
//       return acc;
//     }, {} as Record<string, string[]>),
//     maxSize,
//     multiple: false,
//     noClick: true,
//     noKeyboard: true,
//   });

//   const handleRemoveImage = (e: React.MouseEvent) => {
//     e.stopPropagation();
//     onImageUpload(null);
//     setPreview(null);
//   };

//   const handleCloseModal = () => {
//     setModalOpen(false);
//     if (onModalClose) {
//       onModalClose();
//     }
//     onImageUpload(null); // Reset when modal is closed
//   };

//   const handleOpenModal = () => {
//     setModalOpen(true);
//   };

//   // Sync with external isOpen prop
//   useEffect(() => {
//     setModalOpen(isOpen);
//   }, [isOpen]);

//   useEffect(() => {
//     if (!modalOpen) {
//       onImageUpload(null); // Reset when modal is closed
//     }
//   }, [modalOpen, onImageUpload]);

//   useEffect(() => {
//     if (uploadedImage) {
//       if (typeof uploadedImage === "string") {
//         // If the uploadedImage is a string (URL), ensure the base URL is attached correctly
//         const imageURL = uploadedImage.startsWith("http")
//           ? uploadedImage
//           : media_base_url + uploadedImage;
//         setPreview(imageURL);
//       } else if (uploadedImage instanceof File) {
//         if (uploadedImage.type.startsWith("image/")) {
//           // For File type, create a URL for preview
//           setPreview(URL.createObjectURL(uploadedImage));
//         } else {
//           setPreview(null); // If not an image, clear preview
//         }
//       }
//     } else {
//       setPreview(null); // Reset preview when file is cleared
//     }
//   }, [uploadedImage, media_base_url]);

//   if (!modalOpen) {
//     return (
//       <div className="imageUploader-trigger">
//         <button type="button" onClick={handleOpenModal} className="openModalButton">
//           {label}
//         </button>
//       </div>
//     );
//   }

//   return (
//     <div className="imageUploader-modal">
//       <div className="modal-overlay" onClick={handleCloseModal}></div>
//       <div className="modal-content">
//         <div className="modal-header">
//           <h3>{label}</h3>
//           <button type="button" className="closeModalButton" onClick={handleCloseModal}>
//             <span className="material-icons">close</span>
//           </button>
//         </div>
//         <div className="modal-body">
//           <div className="input-field w-100">
//             <div id="image" {...getRootProps()} className="imageUploader w-100">
//               <input {...getInputProps()} />
//               {!uploadedImage ? (
//                 <>
//                   <div className="uploadIcon" onClick={open}>
//                     <span className="material-icons">upload</span>
//                   </div>
//                   <div className="desc">
//                     Drag & Drop or <span onClick={open}>Choose Image</span> to upload
//                   </div>
//                   <div className="fileFormat">
//                     JPG, or PNG (Max: {maxSize / 1024 / 1024}MB)
//                   </div>
//                 </>
//               ) : (
//                 <div className="imagePreview">
//                   {preview && (
//                     <>
//                       <div className="removeIcon" onClick={handleRemoveImage}>
//                         <span className="material-icons">close</span>
//                       </div>
//                       <Image 
//                         src={preview} 
//                         alt="Preview" 
//                         className="imagePreviewImg" 
//                         width={150} 
//                         height={150} 
//                       />
//                     </>
//                   )}
//                   <p className="imagePreviewName">
//                     {uploadedImage instanceof File ? uploadedImage.name : String(uploadedImage)}
//                   </p>
//                   <div className="imageActions">
//                     <button type="button" className="replaceButton" onClick={open}>
//                       <i className="fa-solid fa-rotate"></i> Change Image
//                     </button>
//                   </div>
//                 </div>
//               )}
//             </div>
//             {error && <p className="error-message">{error}</p>}
//           </div>
//         </div>
//         <div className="modal-footer">
//           <button type="button" className="cancelButton" onClick={handleCloseModal}>
//             Cancel
//           </button>
//           <button 
//             type="button" 
//             className="confirmButton" 
//             disabled={!uploadedImage}
//             onClick={handleCloseModal}
//           >
//             Confirm
//           </button>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default ImageUploader;
'use client'
import React, { useState, useEffect } from "react";
import { FileRejection, useDropzone } from "react-dropzone";
import Image from "next/image";
import './image-uploader.scss'

interface ImageUploaderProps {
  uploadedImage: string | File | null;
  isOpen: boolean;
  allowedTypes: string[];
  maxSize: number;
  onImageUpload: (file: File | null) => void;
  label: string;
  requiredMessage: string;
  maxFileSizeMessage: string;
  invalidTypeMessage: string;
}

const ImageUploader: React.FC<ImageUploaderProps> = ({
  uploadedImage,
  isOpen,
  allowedTypes,
  maxSize,
  onImageUpload,
  label,
  maxFileSizeMessage,
  invalidTypeMessage,
}) => {
  const [error, setError] = useState<string | null>(null);
  const [preview, setPreview] = useState<string | null>(null);
  const media_base_url = process.env.NEXT_PUBLIC_MEDIA_PATH;

  const onDrop = (acceptedFiles: File[], fileRejections: FileRejection[]) => {
    if (fileRejections.length) {
      const rejectionErrors = fileRejections
        .map(({ file, errors }) => {
          return errors
            .map((error) => {
              if (error.code === "file-too-large") {
                return `${maxFileSizeMessage}: ${file.name}`;
              }
              if (error.code === "file-invalid-type") {
                return `${invalidTypeMessage}: ${file.name}`;
              }
              return null;
            })
            .filter(Boolean)
            .join(", ");
        })
        .join(", ");
      setError(rejectionErrors);
      onImageUpload(null);
      return;
    }

    const errors = acceptedFiles.map((file) => {
      if (!allowedTypes.includes(file.type)) {
        return `${invalidTypeMessage}: ${file.name}`;
      }
      if (file.size > maxSize) {
        return `${maxFileSizeMessage}: ${file.name}`;
      }
      return null;
    }).filter(Boolean);

    if (errors.length) {
      setError(errors.join(", "));
      onImageUpload(null);
      return;
    }

    setError(null);
    onImageUpload(acceptedFiles[0]);
  };

  const { getRootProps, getInputProps, open } = useDropzone({
    onDrop,
    accept: allowedTypes.reduce((acc, type) => {
      acc[type] = []; // Populate each type as an object key with an empty array
      return acc;
    }, {} as Record<string, string[]>),
    maxSize,
    multiple: false,
    noClick: true,
    noKeyboard: true,
  });

  const handleRemoveImage = (e: React.MouseEvent) => {
    e.stopPropagation();
    onImageUpload(null);
    setPreview(null);
  };

  useEffect(() => {
    if (!isOpen) {
      onImageUpload(null); // Reset when component is closed
    }
  }, [isOpen, onImageUpload]);

  useEffect(() => {
    if (uploadedImage) {
      if (typeof uploadedImage === "string") {
        // If the uploadedImage is a string (URL), ensure the base URL is attached correctly
        const imageURL = uploadedImage.startsWith("http")
          ? uploadedImage
          : media_base_url + uploadedImage;
        setPreview(imageURL);
      } else if (uploadedImage instanceof File) {
        if (uploadedImage.type.startsWith("image/")) {
          // For File type, create a URL for preview
          setPreview(URL.createObjectURL(uploadedImage));
        } else {
          setPreview(null); // If not an image, clear preview
        }
      }
    } else {
      setPreview(null); // Reset preview when file is cleared
    }
  }, [uploadedImage, media_base_url]);

  return (
    <div className="input-field w-100">
      <label htmlFor="image">{label}</label>
      <div id="image" {...getRootProps()} className="imageUploader w-100">
        <input {...getInputProps()} />
        {!uploadedImage ? (
          <>
            <div className="uploadIcon" onClick={open}>
              <span className="material-icons">upload</span>
            </div>
            <div className="desc">
              Drag & Drop or <span onClick={open}>Choose Image</span> to upload
            </div>
            <div className="fileFormat">
              JPG, or PNG (Max: {maxSize / 1024 / 1024}MB)
            </div>
          </>
        ) : (
          <div className="imagePreview">
            {preview && (
              <>
                <div className="removeIcon" onClick={handleRemoveImage}>
                  <span className="material-icons">close</span>
                </div>
                <Image 
                  src={preview} 
                  alt="Preview" 
                  className="imagePreviewImg" 
                  width={150} 
                  height={150} 
                />
              </>
            )}
            <p className="imagePreviewName">
              {uploadedImage instanceof File ? uploadedImage.name : String(uploadedImage)}
            </p>
            <div className="imageActions">
              <button type="button" className="replaceButton" onClick={open}>
                <i className="fa-solid fa-rotate"></i> Change Image
              </button>
            </div>
          </div>
        )}
      </div>
      {error && <p className="error-message">{error}</p>}
    </div>
  );
};

export default ImageUploader;