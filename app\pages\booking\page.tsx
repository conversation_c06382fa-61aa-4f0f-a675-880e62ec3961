

"use client";
import React, { useEffect, useState } from "react";
import Link from "next/link";
import "./style.scss";
import { useAlert } from "@/app/utilities/Alert/Alert";
import BookingDetails from "./components/BookingDetails/BookingDetails";
import EditBooking from "./components/EditBooking/EditBooking";
import AddByStep from "@/app/components/AddByStep/AddByStep";
import BookingsTable from "./components/BookingTable/BookingTable";
import SearchBox from "@/app/components/SearchBox/SearchBox"; // Added missing import

// Adding missing Booking type definition
interface Booking {
  id: string;
  guestName: string;
  hotelName: string;
  roomType: string;
  checkIn: string;
  checkOut: string;
  guests: number;
  bookingDate: string;
  paymentStatus: string;
  bookingStatus: string;
  amountPaid: string;
  paymentMethod: string;
  is_active: boolean;
}

function BookingsPage() {
  const [isTableLoading, setIsTableLoading] = useState<boolean>(true);
  const [showDetails, setShowDetails] = useState<boolean>(false);
  const [selectedBookingId, setSelectedBookingId] = useState<number | null>(null);
  const [showStepBooking, setShowStepBooking] = useState<boolean>(false);
  const [showEdit, setShowEdit] = useState<boolean>(false);
  const [selectedBookingData, setSelectedBookingData] = useState<Booking | null>(null);
  const [visible, setVisible] = useState<boolean>(false);
  
  // Added missing states for filter and search
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);
  const [status, setStatus] = useState<string>("");
  const [selectedDate, setSelectedDate] = useState<string>("");
  const [searchValue, setSearchValue] = useState<string>("");

  const { fire } = useAlert();
  
  // Sample booking data
  const bookings: Booking[] = [
    {
      id: "BK1001",
      guestName: "John Doe",
      hotelName: "Seaside Resort & Spa",
      roomType: "Deluxe Ocean View",
      checkIn: "2025-05-10",
      checkOut: "2025-05-15",
      guests: 2,
      bookingDate: "2025-04-20",
      paymentStatus: "Paid",
      bookingStatus: "Confirmed",
      amountPaid: "$745.00",
      paymentMethod: "Credit Card",
      is_active: true
    },
    {
      id: "BK1002",
      guestName: "Sarah Johnson",
      hotelName: "Mountain View Lodge",
      roomType: "Standard Twin",
      checkIn: "2025-05-08",
      checkOut: "2025-05-12",
      guests: 2,
      bookingDate: "2025-04-15",
      paymentStatus: "Pending",
      bookingStatus: "Awaiting Payment",
      amountPaid: "$0.00",
      paymentMethod: "Bank Transfer",
      is_active: true
    },
    {
      id: "BK1003",
      guestName: "Michael Smith",
      hotelName: "City Center Hotel",
      roomType: "Executive Suite",
      checkIn: "2025-05-20",
      checkOut: "2025-05-23",
      guests: 1,
      bookingDate: "2025-05-01",
      paymentStatus: "Paid",
      bookingStatus: "Confirmed",
      amountPaid: "$520.00",
      paymentMethod: "PayPal",
      is_active: true
    },

    {
      id: "BK1005",
      guestName: "Emily Davis",
      hotelName: "Lakeside Inn",
      roomType: "Family Suite",
      checkIn: "2025-05-25",
      checkOut: "2025-05-30",
      guests: 4,
      bookingDate: "2025-05-01",
      paymentStatus: "Paid",
      bookingStatus: "Confirmed",
      amountPaid: "$520.00",
      paymentMethod: "PayPal",
      is_active: true
    },
        {
      id: "BK1003",
      guestName: "Michael Smith",
      hotelName: "City Center Hotel",
      roomType: "Executive Suite",
      checkIn: "2025-05-20",
      checkOut: "2025-05-23",
      guests: 1,
      bookingDate: "2025-05-01",
      paymentStatus: "Paid",
      bookingStatus: "Confirmed",
      amountPaid: "$520.00",
      paymentMethod: "PayPal",
      is_active: true
    }
  ];

  // Simulate data loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(true);
      setIsTableLoading(false);
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  const handleShowDetails = (id: number) => {
    setSelectedBookingId(id);
    setShowDetails(true);
  };

  const handleCloseDetails = () => {
    setShowDetails(false);
    setSelectedBookingId(null);
  };

  const handleCreateByStep = () => {
    setShowStepBooking(true);
  };

  const handleCloseCreateByStep = () => {
    setShowStepBooking(false);
  };

  const handleEditBooking = (id: number) => {
    if (id < 0 || id >= bookings.length) return;
    
    // Set the selected booking data
    setSelectedBookingData(bookings[id]);
    setSelectedBookingId(id);
    setShowEdit(true);
  };

  const handleCloseEdit = () => {
    setShowEdit(false);
    setSelectedBookingId(null);
    setSelectedBookingData(null);
  };

  const handleBookingUpdated = () => {
    // In a real app, you might want to refresh the data from the server
    // For this example, we'll just close the edit modal
    fire({
      position: "center",
      icon: "success",
      title: "Success",
      text: "Booking updated successfully",
      confirmButtonText: "Ok"
    });
    handleCloseEdit();
  };

  // Added missing handlers for filter functionality
  const handleToggleFilter = () => {
    setIsFilterOpen(!isFilterOpen);
  };

  const handleFilterSelect = (
    e: React.ChangeEvent<HTMLSelectElement | HTMLInputElement>,
    type: string
  ) => {
    if (type === "status") {
      setStatus(e.target.value);
    } else if (type === "date") {
      setSelectedDate(e.target.value);
    }
  };

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
  };

  return (
    <>
      <div className={`agentList overall-list-padding ${visible ? "visible" : ""}`}>
       

        <div className="user-list-table-container">
       

              <div className="filter-search-container">
                <div className="filterButton" onClick={handleToggleFilter}>
                  <button>
                    <i className="fa-solid fa-filter"></i>
                  </button>
                </div>

         
              </div>
            </div>
           
        <div className="addUser">
          <div className="addButton">
            Add Booking By Step +
          </div>
        </div>
    
            <BookingsTable
              bookings={bookings}
              isLoading={isTableLoading}
              onShowDetails={handleShowDetails}
              onEditBooking={handleEditBooking}
              onAddBooking={handleCreateByStep}
              className="booking-table"
              pageName="booking-page"
            />
          </div>
       

      {/* Booking Details Modal */}
      {selectedBookingId !== null && (
        <BookingDetails
          showDetails={showDetails}
          bookingId={selectedBookingId}
          handleClose={handleCloseDetails}
        />
      )}
      
      {/* Add By Step Modal */}
      <AddByStep 
        id={null} 
        showCreate={showStepBooking}
        handleCloseCreate={handleCloseCreateByStep} 
      />
      
      {/* Edit Booking Modal */}
      {selectedBookingData && (
        <EditBooking 
          showCreate={showEdit} 
          handleCloseCreate={handleCloseEdit} 
          bookingId={selectedBookingData.id}
          bookingData={selectedBookingData}
          onUpdateSuccess={handleBookingUpdated}
        />
      )}
    </>
  );
}

export default BookingsPage;