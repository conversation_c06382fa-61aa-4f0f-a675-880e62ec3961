@use 'sass:color';
@use '/styles/variables' as *;


.side-menu-bar-wrapper{
   position: relative;
   height: 100vh;
   overflow: hidden;
   background-color: #F2F2F2;
}
.sidebar-mainContainer,
.sidebar2-mainContainer {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  transition: all 0.3s ease-in-out;
}
.sidebar-mainContainer{
    z-index: 1;
    width: 70px; 
    &.show{
        visibility: visible;
        transform: translateX(0);
        transition: all 0.5s ease;
    }
    &.hide{
        transform: translateX(-70px);
        visibility: hidden;
    }
    .headerPart{
        background-color: $white_color;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 80px;
        width: auto;
        

        
        img{
            max-width: 100%;
            height: auto;
            margin-bottom: 8px;
        }
        }

    .sidebar-container{
        //width: 70px;
        height: calc(100dvh - 80px) ;
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: $white_color;

        

        
       
    
       
    
        .chevron-right{
            height: 15px;
            width: 15px;
            border: 1.5px solid $black_color;
            display: flex;
            justify-content: center;
            align-items: center;
            border-radius: 50%;
            margin-bottom: 20px;
            cursor: pointer;
            transition: color 0.3s ease;
            margin-top: 25px;
    
            span{
                font-size: 12px;
                font-weight: 600;
            }
    
            &:hover{
                background-color: darken($white_color1, 10%);
            }
        }
    
        ul{
            display: flex;
            flex-direction: column;
            gap: 10px;
            list-style: none;
            padding-bottom: 20px;
            border-bottom: 1px solid $black_color4;
    
            li{
                a{
                    text-decoration: none;
                    .icon{
                        width: 40px;
                        height: 40px; 
                        display: flex;
                        justify-content: center;
                        align-items: center;  
                        border-radius: 50%;
                        border: .5px solid $black_color;
                        cursor: pointer;
                        color: $black_color;
                        transition: background-color 0.5s ease, color 0.2s ease, border 0.2s ease;



                        @media(max-width: $breakpoint-md){
                            width: 30px;
                        height: 30px; 
                        }
                     
    
                        
    
                        &:hover{
                          background-color: $primary_color;
                          color: $white_color;
                          border: .5px solid $primary_color;
                        }

                        
    
                        span{
                            font-size: 21px;

                            @media(max-width: $breakpoint-md){
                                font-size: 16px;
                            }
                        }
                    }

                    .active{
                        width: 40px;
                        height: 40px; 
                        display: flex;
                        justify-content: center;
                        align-items: center;  
                        border-radius: 50%;
                        border: .5px solid $primary_color;
                        cursor: pointer;
                        color: $white_color;
                        background-color: $primary_color;
                      



                        @media(max-width: $breakpoint-md){
                            width: 30px;
                        height: 30px; 
                        }

                        
    
                        span{
                            font-size: 21px;

                            @media(max-width: $breakpoint-md){
                                font-size: 16px;
                            }
                        }
                    }

                    
                }
            }
        }
    
        .secondUL{
            margin-top: 50px;
        }
        
    }
}


.sidebar2-mainContainer{
    z-index: 1000;
    width: 288px;
    &.show{
        transform: translateX(0); 
        visibility: visible;
        transition: all 0.5s ease;
    }
    &.hide{
        transform: translateX(-288px);
        visibility: hidden;
    }
    .headerPart{
        width: 100%;
        background-color: $white_color1;
        display: flex;
        align-items: center;
        height: 80px;
        padding: 0 0px 0 16px;

        @media(max-width : $breakpoint-sm){
            height: 65px;
          }
        
        .image{
            padding-right: 131px;
            border-right: 1px solid $black_color4;

           

            @media(max-width : $breakpoint-sm){
                padding-right: 12px;
              }

              @media(max-width : 360px){
                padding-right: 6px;
              }
    
          img{
              width: 120px;
              height: 35px;

              @media(max-width : $breakpoint-sm){
                width: 85px;
                height: 25px;
              }
          }
      }
        
    
    }
    
    .sidebar-container2{
        // position: fixed;
        // top: 0;
        // left: 0;
        z-index: 100;
        width: 288px;
        height: calc(100dvh - 90px) ;
        display: flex;
        flex-direction: column;
        align-items: center;
        background-color: $white_color;
        border-radius: 20px;
     
        .sidebarHeader{
            width: 100%;
            display: flex;
            justify-content: space-between;
            padding: 0 20px;
            margin-top: 25px;
            
        
    
            .header{
                color: $primary_color;
                font-size: 14px;
            }
    
            .chevron-left{
                height: 15px;
                width: 15px;
                border: 1.5px solid $black_color;
                display: flex;
                justify-content: center;
                align-items: center;
                border-radius: 50%;
                margin-bottom: 20px;
                cursor: pointer;
                transition: color 0.3s ease;
                
                span{
                    font-size: 12px;
                    font-weight: 600;
                }
    
                &:hover{
                    //background-color: darken($white_color1, 10%);
                    background-color: color.scale($white_color1, $lightness: -10.5371900826%);

                }
            }
        }
    
        ul{
            width: 100%;
            display: flex;
            flex-direction: column;
            gap: 10px;
            list-style: none;
            padding: 0 5px 15px 5px;
            border-bottom: 1px solid $black_color4;
          
    
            li{
                width: 100%;
                padding: 0 10px;
    
                a{
                    text-decoration: none;
                    display: flex;
                    align-items: center;
                    font-size: 14px;
                color: $black_color;
                font-weight: 600;
                padding: 9px 17px;
                border-radius: 18px;
                transition: background-color 0.3s ease, color 0.3s ease;
                
             

                span{
                    font-size: 23px;
                    color: $black_color;
                    margin-right: 5px;
                    transition: color 0.3s ease;
                    
                }
    
                &:hover{
                   background-color: $primary_color;
                   color: $white_color;
    
                   span {
                    color: $white_color; 
                  }
                }
                    
                    
    
    
                 
                }
            }

            .active{
                width: 100%;
                padding: 0 10px;
    
                a{
                    text-decoration: none;
                    display: flex;
                    align-items: center;
                    font-size: 14px;
                color: $white_color;
                font-weight: 600;
                padding: 9px 17px;
                border-radius: 18px;
                transition: background-color 0.3s ease, color 0.3s ease;
                background-color: $primary_color;
                
             

                span{
                    font-size: 23px;
                    color: $white_color;
                    margin-right: 5px;
                    transition: color 0.3s ease;
                    
                }
    
                &:hover{
                   background-color: $primary_color;
                   color: $white_color;
    
                   span {
                    color: $white_color; 
                  }
                }
                    
                    
    
    
                 
                }
            }
        }
    
        .secondUL{
            margin-top: 20px;
    
            .header2{
                color: $primary_color;
                font-size: 14px;
                padding: 0 20px;
            }
        }
        
    }
}

//media queries
      @media(max-width: $breakpoint-md){
        .sidebar-mainContainer {
        width: 55px;
        }
    }

    @media (max-width: 500px) {
        .sidebar-mainContainer {
          display: none;
        }
      }
      
      




