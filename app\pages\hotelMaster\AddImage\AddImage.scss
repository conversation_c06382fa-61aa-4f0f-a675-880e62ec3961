
@use '/styles/variables' as *;

.hotel-page-container {
  padding: 20px;
  background-color: $white_color;
  min-height: 100vh;

  .page-title {
    color: darken($black_color2, 15%);
    font-weight: 600;
    font-size: 18px;
    margin-bottom: 20px;
    padding-bottom: 8px;
    border-bottom: 1px solid $black_color4;
  }

  .hotel-form-wrapper {
    max-width: 100%;
    margin: 0 auto;
    background-color: $white_color;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    padding: 20px;
    position: relative;

    .closeButton1 {
      position: absolute;
      top: 15px;
      right: 15px;
      background: none;
      border: 0;
      color: $black_color2;
      font-size: 12px;
      cursor: pointer;

      &:hover {
        color: darken($black_color2, 20%);
      }
    }
  }

  .form-content {
    width: 100%;

    .form-fields-wrapper {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      gap: 15px;
      margin-bottom: 15px;
    }

    .form-field {
      &.half-width {
        width: calc(50% - 8px);

        @media (max-width: 768px) {
          width: 100%;
        }
      }

      &.full-width {
        width: 100%;

        h3 {
          font-size: 16px;
          color: darken($black_color2, 15%);
          font-weight: 600;
          margin-bottom: 15px;
          padding-bottom: 8px;
          border-bottom: 1px solid $black_color4;
          position: relative;

          &::after {
            content: "";
            position: absolute;
            bottom: -1px;
            left: 0;
            width: 60px;
            height: 3px;
            background-color: $primary_color;
            border-radius: 2px;
          }
        }
      }

      label {
        font-size: 13px;
        color: $black_color2;
        font-weight: 600;
        margin-bottom: 6px;
        display: block;
      }

      input, select.form-select {
        width: 100%;
        padding: 10px;
        border: 1px solid $black_color4;
        border-radius: 5px;
        font-size: 14px;
        transition: all 0.2s;

        &:focus {
          border-color: $primary_color;
          outline: none;
          box-shadow: 0 0 0 2px rgba($primary_color, 0.15);
        }
      }

      input::placeholder {
        color: $black_color4;
        font-size: 12px;
      }

      select.form-select {
        background-color: $white_color;
        appearance: none;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='6' fill='none'%3E%3Cpath d='M1 1l5 4 5-4' stroke='%23666' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
        background-repeat: no-repeat;
        background-position: right 10px center;
        cursor: pointer;

        &::-ms-expand {
          display: none;
        }

        option {
          padding: 6px;
          font-size: 14px;
        }
      }

      .field-error {
        color: #ff3333;
        font-size: 12px;
        margin-top: 4px;
      }

      .field-success {
        color: #4CAF50;
        font-size: 12px;
        margin-top: 4px;
      }

      // Textarea styling
      textarea {
        width: 100%;
        padding: 10px;
        border: 1px solid $black_color4;
        border-radius: 5px;
        font-size: 14px;
        font-family: inherit;
        resize: vertical;
        min-height: 80px;
        transition: all 0.2s;

        &:focus {
          border-color: $primary_color;
          outline: none;
          box-shadow: 0 0 0 2px rgba($primary_color, 0.15);
        }

        &::placeholder {
          color: $black_color4;
          font-size: 12px;
        }
      }
    }

    // Form section headers
    .form-section-header {
      width: 100%;
      margin: 25px 0 15px 0;

      h3 {
        font-size: 16px;
        color: darken($black_color2, 15%);
        font-weight: 600;
        margin: 0;
        padding-bottom: 8px;
        border-bottom: 2px solid $black_color4;
        position: relative;

        &::after {
          content: "";
          position: absolute;
          bottom: -2px;
          left: 0;
          width: 60px;
          height: 2px;
          background-color: $primary_color;
          border-radius: 2px;
        }
      }
    }

    // Amenities grid styling
    .amenities-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 12px;
      margin-top: 10px;

      @media (max-width: 768px) {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
        gap: 8px;
      }
    }

    .amenity-checkbox {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      border: 1px solid $black_color4;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.2s;
      font-size: 13px;
      background-color: $white_color;

      &:hover {
        border-color: $primary_color;
        background-color: rgba($primary_color, 0.05);
      }

      input[type="checkbox"] {
        width: auto;
        margin: 0;

        &:checked + .checkmark {
          background-color: $primary_color;
          border-color: $primary_color;

          &::after {
            opacity: 1;
            transform: rotate(45deg) scale(1);
          }
        }
      }

      .checkmark {
        position: relative;
        width: 16px;
        height: 16px;
        border: 2px solid $black_color4;
        border-radius: 3px;
        background-color: $white_color;
        transition: all 0.2s;
        flex-shrink: 0;

        &::after {
          content: "";
          position: absolute;
          left: 3px;
          top: 0px;
          width: 4px;
          height: 8px;
          border: solid $primary_color;
          border-width: 0 2px 2px 0;
          opacity: 0;
          transform: rotate(45deg) scale(0.8);
          transition: all 0.2s;
        }
      }

      // Hide default checkbox
      input[type="checkbox"] {
        position: absolute;
        opacity: 0;
        cursor: pointer;
        height: 0;
        width: 0;
      }
    }

    .form-submit-wrapper {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      margin-top: 20px;

      .submit-button {
        background-color: $primary_color;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 10px 25px;
        font-size: 14px;
        font-weight: 600;
        cursor: pointer;
        transition: background-color 0.2s;

        &:hover {
          background-color: darken($primary_color, 10%);
        }
      }
    }
  }

  // Hotel Images specific styles - UPDATED
  .hotel-images-container {
    margin-top: 15px;

    // New header section for hotel images
    .hotel-images-header {
      margin-bottom: 15px;

      h4 {
        font-size: 15px;
        color: $black_color2;
        font-weight: 600;
        padding-bottom: 8px;
        border-bottom: 1px solid $black_color4;
        display: flex;
        align-items: center;

        &::after {
          content: "";
          flex: 1;
          height: 1px;
          background: linear-gradient(to right, $black_color4, transparent);
          margin-left: 15px;
        }
      }
    }

    .add-image-button {
      background-color: $primary_color;
      color: white;
      border: none;
      border-radius: 8px;
      padding: 10px 18px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
      box-shadow: 0 2px 6px rgba($primary_color, 0.3);

      &::before {
        content: "+";
        font-size: 18px;
        line-height: 1;
      }

      &:hover {
        background-color: darken($primary_color, 8%);
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba($primary_color, 0.4);
      }

      &:active {
        transform: translateY(0);
      }
    }

    .hotel-images-list {
      margin-top: 25px;

      h4 {
        font-size: 15px;
        color: $black_color2;
        margin-bottom: 15px;
        display: flex;
        align-items: center;

        &::after {
          content: "";
          flex: 1;
          height: 1px;
          background: linear-gradient(to right, $black_color4, transparent);
          margin-left: 15px;
        }
      }

      .hotel-images-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 1.5rem;

        @media (max-width: 768px) {
          grid-template-columns: 1fr;
          gap: 1rem;
        }
      }

      .hotel-image-item {
        position: relative;
        border: 1px solid var(--gray-200);
        border-radius: var(--radius-xl);
        padding: 0;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(8px);
        cursor: grab;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        overflow: hidden;
        box-shadow: var(--shadow-md);

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          height: 2px;
          background: linear-gradient(90deg, var(--success-500), var(--primary-500), var(--success-500));
          border-radius: var(--radius-xl) var(--radius-xl) 0 0;
        }

        &:hover {
          box-shadow: var(--shadow-xl);
          transform: translateY(-4px);
          border-color: var(--primary-300);
        }

        &.dragging {
          opacity: 0.8;
          transform: scale(1.05) rotate(2deg);
          box-shadow: var(--shadow-xl);
          z-index: 10;
          border: 2px solid var(--primary-500);
        }

        &.drop-target {
          border: 2px dashed var(--primary-500);
          background: rgba(59, 130, 246, 0.1);
        }

        // Drag handle styling
        .drag-handle {
          position: absolute;
          top: 10px;
          right: 10px;
          background-color: rgba($white_color, 0.8);
          border-radius: 4px;
          padding: 4px;
          cursor: grab;
          z-index: 2;
          transition: all 0.2s;

          &:hover {
            background-color: $white_color;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
          }

          svg {
            display: block;
            color: $black_color3;
          }
        }

        .image-priority {
          position: absolute;
          top: 10px;
          left: 10px;
          background-color: rgba($primary_color, 0.9);
          color: white;
          width: 28px;
          height: 28px;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 13px;
          font-weight: bold;
          z-index: 2;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        }

        .image-preview {
          height: 180px;
          width: 100%;
          overflow: hidden;

          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
          }

          &:hover img {
            transform: scale(1.05);
          }
        }

        .image-details {
          padding: 15px;
          background: linear-gradient(to bottom, rgba($white_color1, 0.9), $white_color);

          .image-info {
            margin-bottom: 12px;

            p {
              font-size: 13px;
              color: $black_color2;
              margin: 5px 0;

              strong {
                color: darken($black_color2, 15%);
              }
            }

            .image-label {
              font-size: 15px;
              font-weight: 600;
              margin-bottom: 8px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .image-type {
              display: inline-block;
              background-color: rgba($primary_color, 0.1);
              color: $primary_color;
              padding: 4px 10px;
              border-radius: 100px;
              font-size: 12px;
              font-weight: 500;
              text-transform: capitalize;
              margin-bottom: 8px;
            }
          }

          // Remove button
          .remove-image-btn {
            background-color: rgba(#ff3333, 0.1);
            color: #ff3333;
            border: 1px solid rgba(#ff3333, 0.3);
            border-radius: 6px;
            padding: 6px 12px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s;

            &:hover {
              background-color: #ff3333;
              color: white;
            }
          }
        }
      }

      // Add image card styling
      .add-image-card {
        border: 2px dashed $black_color4;
        border-radius: 12px;
        padding: 20px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        background-color: $white_color1;
        min-height: 293px;

        &:hover {
          border-color: $primary_color;
          background-color: rgba($primary_color, 0.05);
          transform: translateY(-3px);
        }

        .add-icon {
          margin-bottom: 15px;

          svg {
            width: 50px;
            height: 50px;
          }
        }

        .add-text {
          text-align: center;

          span {
            font-size: 16px;
            font-weight: 600;
            color: $primary_color;
            display: block;
            margin-bottom: 5px;
          }

          p {
            font-size: 13px;
            color: $black_color3;
          }
        }
      }
    }

    // Modal styles - UPDATED
    .add-image-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
      background-color: rgba(0, 0, 0, 0.5);
      backdrop-filter: blur(3px);

      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }

      @keyframes slideUp {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .modal-content {
        position: relative;
        width: 550px;
        max-width: 90%;
        max-height: 90vh;
        overflow-y: auto;
        background-color: $white_color;
        border-radius: 12px;
        padding: 25px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
        z-index: 1001;
        animation: slideUp 0.3s ease;

        &::-webkit-scrollbar {
          width: 8px;
        }

        &::-webkit-scrollbar-track {
          background: $white_color1;
          border-radius: 10px;
        }

        &::-webkit-scrollbar-thumb {
          background: $black_color4;
          border-radius: 10px;

          &:hover {
            background: darken($black_color4, 10%);
          }
        }

        .modal-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 20px;
          padding-bottom: 12px;
          border-bottom: 1px solid $black_color4;

          h4 {
            font-size: 18px;
            color: darken($black_color2, 15%);
            margin: 0;
            padding: 0;

            &::before {
              content: "";
              display: inline-block;
              width: 4px;
              height: 18px;
              background-color: $primary_color;
              margin-right: 10px;
              border-radius: 2px;
              vertical-align: middle;
            }
          }

          .close-modal {
            background: none;
            border: none;
            font-size: 22px;
            line-height: 1;
            color: $black_color3;
            cursor: pointer;
            transition: all 0.2s;

            &:hover {
              color: #ff3333;
              transform: scale(1.2);
            }
          }
        }

        .modal-body {
          .form-field {
            margin-bottom: 20px;

            label {
              font-size: 14px;
              color: darken($black_color2, 10%);
              font-weight: 600;
              margin-bottom: 8px;
              display: block;
            }

            input, select {
              width: 100%;
              padding: 12px 15px;
              border: 1px solid $black_color4;
              border-radius: 8px;
              font-size: 14px;
              transition: all 0.2s ease;
              background-color: $white_color1;

              &:focus {
                border-color: $primary_color;
                outline: none;
                box-shadow: 0 0 0 3px rgba($primary_color, 0.15);
                background-color: white;
              }
            }

            select {
              appearance: none;
              background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='14' height='8' fill='none'%3E%3Cpath d='M1 1l6 6 6-6' stroke='%23666' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E");
              background-repeat: no-repeat;
              background-position: right 15px center;
              padding-right: 40px;
            }
          }
        }

        .modal-footer {
          display: flex;
          justify-content: flex-end;
          gap: 12px;
          margin-top: 25px;
          padding-top: 15px;
          border-top: 1px solid $black_color4;

          button {
            padding: 10px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;

            &.cancel-button {
              background-color: $white_color;
              border: 1px solid $black_color4;
              color: $black_color2;

              &:hover {
                background-color: $black_color4;
                color: darken($black_color2, 15%);
              }
            }

            &.add-button {
              background-color: $primary_color;
              color: white;
              border: none;
              box-shadow: 0 2px 6px rgba($primary_color, 0.3);
              display: flex;
              align-items: center;
              gap: 8px;

              &::before {
                content: "+";
                font-size: 18px;
                line-height: 0;
              }

              &:hover {
                background-color: darken($primary_color, 8%);
                transform: translateY(-2px);
                box-shadow: 0 4px 8px rgba($primary_color, 0.4);
              }

              &:active {
                transform: translateY(0);
              }

              &:disabled {
                background-color: rgba($primary_color, 0.5);
                cursor: not-allowed;
                transform: none;
                box-shadow: none;
              }
            }
          }
        }
      }
    }
  }
}