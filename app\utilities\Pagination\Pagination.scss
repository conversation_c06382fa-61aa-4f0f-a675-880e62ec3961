@use '/styles/variables' as *;


.pagination{
    width: 100%;
    //background-color: $white_color;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 30px;
    background-color: $white_color;
    border-top: 1px solid $black_color4;

    @media(max-width: 560px){
        flex-direction: column;
        height: 75px;
        gap: 5px;
        padding: 8px 0;
    }
 


    .result-per-page{
   
        display: flex;
        align-items: center;
        gap: 10px;
        

        // @media(max-width: $breakpoint-lg){
        //     padding-left: 35px;
        //   }
 
        //   @media(max-width: $breakpoint-md){
        //      padding-left: 15px;
        //    }

        //    @media(max-width: 560px){
        //     padding-left: 0px;
        // }

       
        
        

        label{
            color: #15171880;
            font-size: 11px;
            font-weight: 500;
            
            @media(max-width: $breakpoint-lg){
             font-size: 11px;
             }

             @media(max-width: $breakpoint-md){
                font-size: 10px;
                }
        }

        input{
            margin-left: 7px;
            width: 20px;
            height: 20px;
            font-size: 14px;
            //padding-left: 2px;

            @media(max-width: $breakpoint-md){
                width: 20px;
                height: 20px;
                font-size: 12px;
                // padding-left: 5px;
            }
        }

    }


    .pages{
        //padding-right: 53px;
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 7px;


         @media(max-width: $breakpoint-lg){
           padding-right: 35px;
         }

         @media(max-width: $breakpoint-md){
            padding-right: 15px;
          }

          @media(max-width: 560px){
            padding-right: 0px;
        }

        // @media(max-width: 795px){
        //     padding: 0;
        //     }
    }

    @media(max-width: $breakpoint-md){
        .pNo{
                  display: none;
        }

 
    }

    .pageOfPages{
        display: none; 

        @media (max-width:  $breakpoint-md) {
            display: flex; 
        }
    }


            

}