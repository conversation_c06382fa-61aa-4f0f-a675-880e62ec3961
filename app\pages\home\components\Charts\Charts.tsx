import React, { useEffect, useState, useRef } from 'react';
import * as echarts from 'echarts';

const DashboardCharts = () => {
  const [selectedBookingPeriod, setSelectedBookingPeriod] = useState('weekly');
  const [selectedRevenuePeriod, setSelectedRevenuePeriod] = useState('weekly');
  
  // Chart refs
  const bookingChartRef = useRef(null);
  const revenueChartRef = useRef(null);
  
  // Chart instances refs
  const bookingChartInstance = useRef(null);
  const revenueChartInstance = useRef(null);

  // Sample data for different periods
  const bookingData = {
    daily: {
      xAxis: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      data: [30, 40, 35, 50, 49, 60, 70]
    },
    weekly: {
      xAxis: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6', 'Week 7'],
      data: [120, 132, 101, 134, 90, 230, 210]
    },
    monthly: {
      xAxis: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
      data: [320, 332, 301, 334, 390, 330, 410]
    }
  };

  const revenueData = {
    daily: {
      xAxis: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      data: [3000, 4500, 3500, 5000, 4900, 6000, 7000]
    },
    weekly: {
      xAxis: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6', 'Week 7'],
      data: [10000, 15000, 12000, 18000, 14000, 22000, 20000]
    },
    monthly: {
      xAxis: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
      data: [45000, 52000, 49000, 60000, 55000, 70000, 65000]
    }
  };

  // Initialize charts
  useEffect(() => {
    // Initialize chart instances if they don't exist
    if (!bookingChartInstance.current && bookingChartRef.current) {
      bookingChartInstance.current = echarts.init(bookingChartRef.current);
    }
    
    if (!revenueChartInstance.current && revenueChartRef.current) {
      revenueChartInstance.current = echarts.init(revenueChartRef.current);
    }
    
    // Set initial chart options
    updateBookingChart();
    updateRevenueChart();

    // Resize handler
    const handleResize = () => {
      bookingChartInstance.current?.resize();
      revenueChartInstance.current?.resize();
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      bookingChartInstance.current?.dispose();
      revenueChartInstance.current?.dispose();
    };
  }, []);

  // Update booking chart when period changes
  useEffect(() => {
    updateBookingChart();
  }, [selectedBookingPeriod]);

  // Update revenue chart when period changes
  useEffect(() => {
    updateRevenueChart();
  }, [selectedRevenuePeriod]);

  const updateBookingChart = () => {
    if (!bookingChartInstance.current) return;

    const bookingOption = {
      animation: true,
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: bookingData[selectedBookingPeriod].xAxis
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: 'Bookings',
          type: 'line',
          smooth: true,
          lineStyle: {
            color: '#4F46E5',
            width: 3
          },
          areaStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(79, 70, 229, 0.3)' },
              { offset: 1, color: 'rgba(79, 70, 229, 0.1)' }
            ])
          },
          data: bookingData[selectedBookingPeriod].data
        }
      ]
    };

    bookingChartInstance.current.setOption(bookingOption);
  };

  const updateRevenueChart = () => {
    if (!revenueChartInstance.current) return;

    const revenueOption = {
      animation: true,
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: revenueData[selectedRevenuePeriod].xAxis
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: 'Revenue',
          type: 'bar',
          barWidth: '60%',
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#10B981' },
              { offset: 1, color: '#059669' }
            ])
          },
          data: revenueData[selectedRevenuePeriod].data
        }
      ]
    };

    revenueChartInstance.current.setOption(revenueOption);
  };

  return (
    <div className="grid grid-cols-1 xl:grid-cols-2 gap-6 mb-8">
      {/* Booking Trends Chart */}
      <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-800">Booking Analysis</h3>
          <div className="flex space-x-2">
            {['daily', 'weekly', 'monthly'].map(period => (
              <button
                key={period}
                onClick={() => setSelectedBookingPeriod(period)}
                className={`px-3 py-1 text-xs font-medium rounded-full ${
                  selectedBookingPeriod === period
                    ? 'bg-indigo-600 text-white'
                    : 'bg-indigo-100 text-indigo-800'
                }`}
              >
                {period.charAt(0).toUpperCase() + period.slice(1)}
              </button>
            ))}
          </div>
        </div>
        <div ref={bookingChartRef} className="h-64"></div>
      </div>

      {/* Revenue Analysis Chart */}
      <div className="bg-white rounded-lg shadow-sm p-6 border border-gray-100">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-800">Revenue Trends</h3>
          <div className="flex space-x-2">
            {['daily', 'weekly', 'monthly'].map(period => (
              <button
                key={period}
                onClick={() => setSelectedRevenuePeriod(period)}
                className={`px-3 py-1 text-xs font-medium rounded-full ${
                  selectedRevenuePeriod === period
                    ? 'bg-indigo-600 text-white'
                    : 'bg-indigo-100 text-indigo-800'
                }`}
              >
                {period.charAt(0).toUpperCase() + period.slice(1)}
              </button>
            ))}
          </div>
        </div>
        <div ref={revenueChartRef} className="h-64"></div>
      </div>
    </div>
  );
};

export default DashboardCharts;