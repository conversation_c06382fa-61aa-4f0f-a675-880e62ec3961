// HotelList Component (components/HotelList/HotelList.tsx)
import React from 'react';

const HotelList: React.FC = () => {
  return (
    <div className="table-card">
      <h2 className="table-title">Hotel List</h2>
      <div className="table-container">
        <table className="data-table">
          <thead>
            <tr>
              <th>Hotel Name</th>
              <th>Current Status</th>
              <th>Hotel ID</th>
              <th>Location</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>
                <div className="hotel-cell">
                  <div className="hotel-icon">
                    <i className="fas fa-hotel"></i>
                  </div>
                  <div className="hotel-name">Seaside Resort & Spa</div>
                </div>
              </td>
              <td>
                <span className="status-badge active">Active</span>
              </td>
              <td>H001</td>
              <td>Miami, FL</td>
            </tr>
            <tr>
              <td>
                <div className="hotel-cell">
                  <div className="hotel-icon">
                    <i className="fas fa-hotel"></i>
                  </div>
                  <div className="hotel-name">Mountain View Lodge</div>
                </div>
              </td>
              <td>
                <span className="status-badge active">Active</span>
              </td>
              <td>H002</td>
              <td>Denver, CO</td>
            </tr>
            <tr>
              <td>
                <div className="hotel-cell">
                  <div className="hotel-icon">
                    <i className="fas fa-hotel"></i>
                  </div>
                  <div className="hotel-name">City Center Hotel</div>
                </div>
              </td>
              <td>
                <span className="status-badge active">Active</span>
              </td>
              <td>H003</td>
              <td>New York, NY</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default HotelList;