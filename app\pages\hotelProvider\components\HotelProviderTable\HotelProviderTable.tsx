

"use client";
import React, { useState, useEffect, useMemo } from "react";
import Link from "next/link";
import Image from "next/image";
import TableWithShimmer from "@/app/components/TableWithShimmer/TableWithShimmer";
import Pagination from "@/app/utilities/Pagination/Pagination";
import TableMenuTwo from "@/app/components/TableMenuTwo/TableMenuTwo";
import { useAlert } from "@/app/utilities/Alert/Alert";
import profilePic from "@/public/images/profile.png";
import SearchBox from "@/app/components/SearchBox/SearchBox";

export interface DropdownItem {
  id: string;
  label: string;
}

interface HotelProvider {
  id: number;
  name: string;
  username: string;
  countryCode: string;
  status: "Active" | "Inactive";
  logo: any; // Using any for simplicity, but would be StaticImageData in practice
}

interface HotelProviderTableProps {
  onEdit: (id: number) => void;
  onShowDetails: (id: number) => void;
  pageName?: string;
  showFilter?: boolean;
}

const HotelProviderTable: React.FC<HotelProviderTableProps> = ({
  onEdit,
  onShowDetails,
  pageName,
  showFilter,
}) => {
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [totalPages, setTotalPages] = useState<number>(3); // Updated for multiple pages
  const [isTableLoading] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState<string>("");
  const [status, setStatus] = useState<string>("");
  const [selectedDate, setSelectedDate] = useState<string>("");
  const [isFilterOpen, setIsFilterOpen] = useState<boolean>(false);
  const [filteredProviders, setFilteredProviders] = useState<HotelProvider[]>([]);

  // Sample data array for hotel providers
  const hotelProviders: HotelProvider[] = useMemo(() => [
    {
      id: 1,
      name: "Hilton Hotels",
      username: "hilton_admin",
      countryCode: "US",
      status: "Active",
      logo: profilePic,
    },
    {
      id: 2,
      name: "Marriott International",
      username: "marriott_global",
      countryCode: "US",
      status: "Active",
      logo: profilePic,
    },
  ], [profilePic]);

  const { fire } = useAlert();

  // Filter popup toggle
  const handleToggleFilter = () => {
    setIsFilterOpen((prev) => !prev);
  };

  const handleFilterSelect = (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
    item: string
  ) => {
    const value = event.target.value;
    if (item === "status") {
      setStatus(value);
    } else if (item === "date") {
      setSelectedDate(value);
    }
  };

  const handlePageChange = (pageNo: number) => {
    setCurrentPage(pageNo);
  };

  const handleItemsPerPageChange = (value: number) => {
    setItemsPerPage(value);
    setCurrentPage(1);
    // Recalculate total pages
    setTotalPages(Math.ceil(hotelProviders.length / value));
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchValue(value);
  };

  const handleDeleteProvider = (id: number) => {
    fire({
      position: "center",
      icon: "info",
      title: `Are you sure you want to delete this hotel provider?`,
      text: "This action cannot be undone!",
      confirmButtonText: "yes",
      cancelButtonText: "No",
      onConfirm: async () => {
        console.log("delete id:", id);
        // Here you would add the API call to delete the provider
      },
    });
  };

  const handleStatusChange = (item: DropdownItem, id: number) => {
    fire({
      position: "center",
      icon: "info",
      title: `Are you sure?`,
      text: `To ${
        item.id === "disable-agent" ? "deactivate" : "activate"
      } the hotel provider.`,
      confirmButtonText: "yes",
      cancelButtonText: "No",
      ...(item.id === "disable-agent"
        ? { initialValue: "Currently Unavailable" }
        : {}),
      onConfirm: async (value: string) => {
        console.log(`status change value: ${value} and provider id: ${id}`);
        // Here you would add the API call to update the provider status
      },
    });
  };

  const handleMenuItemClick = (item: DropdownItem, id: number) => {
    if (item.id === "delete-agent") {
      handleDeleteProvider(id);
    } else if (item.id === "edit-agent") {
      onEdit(id);
    } else {
      handleStatusChange(item, id);
    }
  };

  // Apply filters and search to providers
  useEffect(() => {
    let filtered = [...hotelProviders];
    
    // Apply status filter
    if (status) {
      const statusFilter = status.charAt(0).toUpperCase() + status.slice(1);
      filtered = filtered.filter(provider => 
        provider.status === statusFilter
      );
    }
    
    // Apply search filter
    if (searchValue) {
      const searchLower = searchValue.toLowerCase();
      filtered = filtered.filter(provider =>
        provider.name.toLowerCase().includes(searchLower) ||
        provider.username.toLowerCase().includes(searchLower)
      );
    }
    
    setFilteredProviders(filtered);
    setTotalPages(Math.ceil(filtered.length / itemsPerPage));
  }, [status, searchValue, itemsPerPage, hotelProviders]);

  // Get current items
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredProviders.slice(indexOfFirstItem, indexOfLastItem);

  useEffect(() => {
    console.log("Hotel provider table component rendered");
    setFilteredProviders(hotelProviders);
  }, []);

  return (
    <div className="user-list-table-container">
      <div className="tableBorder">
        <div className="table-header">
          <h5>Hotel Provider List</h5>
          {showFilter && (
            <div className="filter-search-container">
              <div className="filterButton" onClick={handleToggleFilter}>
                <button>
                  <i className="fa-solid fa-filter"></i>
                </button>
              </div>

              <div
                className={`filter-options-select-box ${
                  isFilterOpen ? "show" : ""
                }`}
              >
                <div className="filterOption">
                  <span>Status {status ? `(${status})` : ""}</span>
                  <select
                    className="dropdown"
                    value={status}
                    onChange={(e) => handleFilterSelect(e, "status")}
                  >
                    <option value="">None</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                  <span className="material-icons">keyboard_arrow_down</span>
                </div>
          
              </div>

              <SearchBox
                value={searchValue}
                onChange={handleSearchChange}
                placeholders={["Search by provider name"]}
              />
            </div>
          )}
        </div>

        {isTableLoading ? (
          <TableWithShimmer
            no_of_rows={itemsPerPage < 8 ? itemsPerPage : 8}
            no_of_cols={5}
            colWidths={[1.5, 1, 1, 1]}
          />
        ) : (
          <div className="table-style table-vertical-scroll">
            <table>
              <thead>
                <tr>
                  {pageName === "hotelProvider" && (
                    <>
                      <th>Hotel Provider</th>
                      <th>Username</th>
                      <th>Country Code</th>
                      <th>Status</th>
                    
                      <th></th>
                    </>
                  )}
                  {pageName === "homepage" && (
                    <>
                      <th>Hotel Provider</th>
                      <th>Username</th>
                      <th>Country Code</th>
                      <th>Status</th>
                    </>
                  )}
                </tr>
              </thead>

              <tbody>
                {currentItems.map((provider) => (
                  <tr 
                    key={provider.id} 
                    onClick={() => onShowDetails(provider.id)}
                  >
                    {pageName === "hotelProvider" && (
                      <>
                        <td>
                          <div className="name-profilePic">
                            <div className="profile-pic">
                              <Image
                                src={provider.logo}
                                alt="Hotel Logo"
                                fill
                                className="profileImage"
                                style={{ objectFit: "cover" }}
                                sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                                loading="lazy"
                              />
                            </div>
                            <div className="name">{provider.name}</div>
                          </div>
                        </td>

                        <td>
                          <span className="empId">{provider.username}</span>
                        </td>

                        <td>
                          <span className="walletAmount">{provider.countryCode}</span>
                        </td>

                        <td>
                          <div className={provider.status === "Active" ? "available" : "unavailable"}>
                            <span>{provider.status}</span>
                          </div>
                        </td>

                   
                        <td onClick={(e) => e.stopPropagation()}>
                          <TableMenuTwo
                            items={[
                              { id: "edit-agent", label: "Edit Provider" },
                              { id: provider.status === "Active" ? "disable-agent" : "enable-agent", 
                                label: provider.status === "Active" ? "Deactivate Provider" : "Activate Provider" },
                              { id: "delete-agent", label: "Delete Provider" },
                            ]}
                            onClick={handleMenuItemClick}
                            id={provider.id}
                          />
                        </td>
                      </>
                    )}

                    {pageName === "homepage" && (
                      <>
                        <td>
                          <div className="name-profilePic">
                            <div className="profile-pic">
                              <Image
                                src={provider.logo}
                                alt="Hotel Logo"
                                fill
                                className="profileImage"
                                style={{ objectFit: "cover" }}
                                sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                                loading="lazy"
                              />
                            </div>
                            <div className="name">{provider.name}</div>
                          </div>
                        </td>

                        <td>
                          <span className="empId">{provider.username}</span>
                        </td>

                        <td>
                          <span className="walletAmount">{provider.countryCode}</span>
                        </td>

                        <td>
                          <div className={provider.status === "Active" ? "available" : "unavailable"}>
                            <span>{provider.status}</span>
                          </div>
                        </td>
                      </>
                    )}
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}

        <Pagination
          totalPages={totalPages}
          handlePage={handlePageChange}
          itemsPerPage={itemsPerPage}
          page={currentPage}
          handleItemsPerPageChange={handleItemsPerPageChange}
        />
      </div>
    </div>
  );
};

export default HotelProviderTable;