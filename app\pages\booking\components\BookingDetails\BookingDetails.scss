
@use '/styles/variables' as *;

.bookingDetailsOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.bookingDetails {
  width: 100%; /* Changed to 100% for full width */
  max-width: 100%; /* Removed max-width limitation */
  height: 100vh; /* Changed back to 100vh for full-screen view */
  background-color: #fff;
  border-radius: 0; /* Removed border radius for full-width look */
  box-shadow: none; /* Removed shadow for full-width design */
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.bookingDetailsContainer {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: hidden;
}

.bookingHeader {
  background-color: #f9fafc;
  padding: 16px 32px; /* Increased horizontal padding */
  border-bottom: 1px solid #eef1f5;
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  h1 {
    font-size: 20px; /* Slightly increased font size */
    font-weight: 600;
    margin: 0;
    color: #1a202c;
  }
  
  .closeButton {
    background: none;
    border: none;
    cursor: pointer;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #718096;
    border-radius: 4px;
    transition: all 0.2s;
    
    &:hover {
      background-color: #edf2f7;
    }
  }
}

.bookingContent {
  padding: 24px 32px; /* Increased padding for better spacing */
  overflow-y: auto;
  flex: 1;
}

/* Redesigned top section with more spacious layout */
.bookingTopSection {
  display: flex;
  flex-direction: column;
  gap: 24px; /* Increased gap */
  margin-bottom: 32px; /* Increased bottom margin */
  
  @media (min-width: 768px) {
    flex-direction: row;
    align-items: stretch;
  }
}

.bookingTopContent {
  display: flex;
  flex-direction: column;
  flex: 3; /* Increased ratio for better space utilization */
  gap: 24px; /* Increased gap */
  
  @media (min-width: 992px) {
    flex-direction: row;
  }
}

.bookingPaymentSection {
  flex: 1;
  display: flex;
  flex-direction: column;
  
  @media (min-width: 992px) {
    margin-left: 24px; /* Increased margin */
  }
}

.card, .guestInfoCard, .summaryCard, .detailsCard, .roomCard, .noDataFound {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  margin-bottom: 0;
  height: 100%;
}

/* Compact guest info with uniform styling */
.guestInfoCard {
  display: flex;
  align-items: center;
  padding: 24px;
  border-radius: 12px;
  background-color: #ffffff;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  margin-bottom: 20px;
  transition: all 0.3s ease;
}

.guestInfoCard:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.guestImageContainer {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f7fafc;
  border-radius: 50%;
  height: 56px;
  width: 56px;
  margin-right: 20px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
}

.guestInfo {
  flex: 1;
}

.guestInfo h2 {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 12px 0;
  line-height: 1.2;
}

.bookingId {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}

.idLabel {
  color: #718096;
  margin-right: 8px;
  font-weight: 500;
}

.idValue {
  color: #4a5568;
  font-family: monospace;
  background-color: #f7fafc;
  padding: 2px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.statusBadge {
  margin-top: 8px;
}

.statusActive,
.statusCancelled,
.statusPending {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
  line-height: 1;
}

.statusActive {
  background-color: #e6fffa;
  color: #047857;
  border: 1px solid #a7f3d0;
}

.statusCancelled {
  background-color: #fee2e2;
  color: #b91c1c;
  border: 1px solid #fecaca;
}

.statusPending {
  background-color: #fef3c7;
  color: #92400e;
  border: 1px solid #fde68a;
}

/* For responsive design */
@media (max-width: 640px) {
  .guestInfoCard {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .guestImageContainer {
    margin-bottom: 16px;
    margin-right: 0;
  }
}

.guestImageContainer {
  width: 64px; /* Increased size */
  height: 64px; /* Increased size */
  border-radius: 50%;
  overflow: hidden;
  border: 2px solid #edf2f7;
  margin-right: 20px; /* Increased margin */
  background-color: #f7fafc;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.guestInfo {
  flex: 1;
  
  h2 {
    font-size: 20px; /* Increased size */
    font-weight: 600;
    margin: 0 0 8px; /* Increased margin */
    color: #1a202c;
  }
}

.bookingSummary {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.summaryHeader, .cardHeader, .roomHeader {
  background-color: #f9fafc;
  padding: 14px 20px; /* Increased padding */
  border-bottom: 1px solid #eef1f5;
  display: flex;
  align-items: center;
  justify-content: space-between;
  
  h3, h4 {
    font-size: 16px; /* Increased size */
    font-weight: 600;
    margin: 0;
    color: #2d3748;
  }
}

.summaryContent, .cardContent, .roomContent {
  padding: 18px 20px; /* Increased padding */
}

/* Labels and values */
.idLabel, .dateLabel, .detailLabel {
  font-size: 13px; /* Slightly increased */
  color: #718096;
  margin-bottom: 3px; /* Increased */
  display: block;
  font-weight: 500;
}

.idValue, .dateValue, .detailValue {
  font-weight: 600;
  color: #2d3748;
  font-size: 15px; /* Increased */
  margin-bottom: 12px; /* Increased */
}

/* Status badges */
.statusActive, .statusPending, .statusCancelled, .paymentStatus {
  border-radius: 12px;
  font-size: 13px; /* Increased */
  font-weight: 500;
  padding: 5px 12px; /* Increased */
  display: inline-flex;
  align-items: center;
}

.statusActive, .paymentStatus.paid {
  background-color: #ebf8f0;
  color: #047857;
}

.statusPending, .paymentStatus.pending {
  background-color: #fff8ed;
  color: #b45309;
}

.statusCancelled {
  background-color: #fee2e2;
  color: #b91c1c;
}

/* Date display */
.dateInfo {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #f8fafc;
  border-radius: 6px;
  padding: 16px 20px; /* Increased */
  margin: 16px 0; /* Increased */
  
  @media (max-width: 576px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px; /* Increased */
  }
}

.checkDate {
  display: flex;
  flex-direction: column;
}

.dateDivider {
  display: flex;
  align-items: center;
  color: #a0aec0;
  padding: 0 16px; /* Increased */
  
  @media (max-width: 576px) {
    display: none;
  }
}

/* Amount display */
.amountPaid {
  display: flex;
  align-items: center;
  margin-top: 14px; /* Increased */
  background-color: #f0f9ff;
  padding: 10px 16px; /* Increased */
  border-radius: 6px;
}

.amountLabel {
  font-size: 14px; /* Increased */
  color: #4a5568;
  margin-right: 10px; /* Increased */
  font-weight: 500;
}

.amountValue {
  font-size: 18px; /* Increased */
  font-weight: 700;
  color: #2b6cb0;
}

/* Grid layout */
.bookingDetailsGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px; /* Increased */
  margin-bottom: 32px; /* Increased */
  
  @media (min-width: 768px) {
    grid-template-columns: 1fr 1fr;
  }

  /* Added support for larger screens */
  @media (min-width: 1400px) {
    grid-template-columns: 1fr 1fr 1fr;
  }
}

.detailItem, .roomDetail {
  margin-bottom: 14px; /* Increased */
  
  &:last-child {
    margin-bottom: 0;
  }
}

/* Rooms section */
.bookingRoomsSection {
  margin-bottom: 32px; /* Increased */
}

.sectionTitle {
  font-size: 18px; /* Increased */
  font-weight: 600;
  margin-bottom: 16px; /* Increased */
  color: #1a202c; 
  padding-bottom: 8px; /* Increased */
  border-bottom: 1px solid #edf2f7;
}

.roomsGrid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px; /* Increased */
  
  @media (min-width: 768px) {
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  }

  /* Added support for larger screens */
  @media (min-width: 1400px) {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }

  /* Added ultra-wide support */
  @media (min-width: 1800px) {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  }
}

.roomHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.roomPrice {
  font-size: 16px; /* Increased */
  font-weight: 700;
  color: #2b6cb0;
}

/* Guest profile */
.guestProfileContainer {
  display: flex;
  align-items: center;
  margin-bottom: 12px; /* Increased */
  padding-bottom: 10px; /* Increased */
  border-bottom: 1px solid #edf2f7;
}

.guestProfileImage {
  width: 40px; /* Increased */
  height: 40px; /* Increased */
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid #edf2f7;
  margin-right: 14px; /* Increased */
  background-color: #f7fafc;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

.guestName {
  font-size: 15px; /* Increased */
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 3px; /* Increased */
}

.guestCount {
  display: flex;
  align-items: center;
  font-size: 13px; /* Increased */
  color: #718096;
  gap: 5px; /* Increased */
}

/* Action buttons */
.actionsContainer {
  display: flex;
  gap: 16px; /* Increased */
  justify-content: flex-end;
  margin-top: 24px; /* Increased */
  
  @media (max-width: 576px) {
    flex-direction: column;
  }
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px; /* Increased */
  border-radius: 6px;
  font-size: 15px; /* Increased */
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  gap: 8px; /* Increased */
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.actionButton.primary {
  background-color: #3182ce;
  color: white;
  
  &:hover:not(:disabled) {
    background-color: #2c5282;
  }
}

.actionButton.secondary {
  background-color: #f7fafc;
  color: #4a5568;
  border: 1px solid #e2e8f0;
  
  &:hover:not(:disabled) {
    background-color: #edf2f7;
  }
}

.actionButton.delete {
  background-color: #fff5f5;
  color: #c53030;
  border: 1px solid #fed7d7;
  
  &:hover:not(:disabled) {
    background-color: #fed7d7;
  }
}

/* Loading state */
.bookingDetailsLoading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px; /* Increased */
  padding: 32px; /* Increased */
  gap: 20px; /* Increased */
  
  p {
    color: #718096;
    font-size: 15px; /* Increased */
  }
}

.loader {
  border: 4px solid #e2e8f0; /* Increased */
  border-top-color: #3182ce;
  border-radius: 50%;
  width: 40px; /* Increased */
  height: 40px; /* Increased */
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* No data state */
.noDataFound {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 160px; /* Increased */
  margin: 24px; /* Increased */
  
  p {
    font-size: 15px; /* Increased */
    color: #718096;
    text-align: center;
    max-width: 300px; /* Increased */
    line-height: 1.5; /* Increased */
  }
}