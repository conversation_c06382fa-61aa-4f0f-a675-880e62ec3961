@use "sass:color";
// Replace @import with @use
@use '/styles/variables' as *;

//@import '/app/styles/variables.scss';

.closeButton{
    background-color: $primary_color;
    color: $white_color1;
    font-size: 11px;
    padding: 5px 15px;
    border-radius: 14px;
    border: 0;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover{
        //background-color: darken($primary_color, 10%);  
        background-color: color.adjust($primary_color, $lightness: -10%);
   
    }

    @media( max-width: $breakpoint-md){
        font-size: 10px;
        padding: 2px 10px;
        border-radius: 14px;
        margin: 0;

    }
    @media( max-width: $breakpoint-sm){
        font-size: 9px;
        padding: 3px 13px;
        border-radius: 14px;
        margin: 0;

    }


    // @media (max-width: $breakpoint-lg) {
    //     //font-size: 13px; 
    //     padding: 5px 20px;
    // border-radius: 14px;
    // }

    // @media (max-width: $breakpoint-md) {
    //     //font-size: 12px;
    //     padding: 5px 20px;
    //     border-radius: 14px;
    // }
}
.closeButton1 {
    background-color: $primary_color;
    color: $white_color1;
    font-size: 11px;
    padding: 5px 15px;
    border-radius: 14px;
    border: 0;
    cursor: pointer;
    transition: background-color 0.3s ease;
    float: right; // Positions the button to the right
    margin-left: auto; // Alternative way to push to the right in flex containers
    
    &:hover {
        background-color: color.adjust($primary_color, $lightness: -10%);
    }

    @media (max-width: $breakpoint-md) {
        font-size: 10px;
        padding: 2px 10px;
        border-radius: 14px;
        margin: 0;
        margin-left: auto; // Maintains right alignment on medium screens
    }
    
    @media (max-width: $breakpoint-sm) {
        font-size: 9px;
        padding: 3px 13px;
        border-radius: 14px;
        margin: 0;
        margin-left: auto; // Maintains right alignment on small screens
    }
}

.submitButton{
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    background-color: $primary_color;
    color: $white_color1;
    font-size: 11px;
    padding: 6px 26px;
    border-radius: 3px;
    border: 0;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover{
        //background-color: darken($primary_color, 10%);   
        background-color: color.adjust($primary_color, $lightness: -10%);
  
    }
    .material-icons{
        font-size: 14px;
    }

  
}

.acceptButton{
    background-color: $primary_color;
    color: $white_color1;
    font-size: 12px;
    width: 75px;
    height: 25px;
    border-radius: 3px;
    border: 1px solid $primary_color;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover{
        //background-color: darken($primary_color, 10%);    
        background-color: color.adjust($primary_color, $lightness: -10%);
 
    }


  
}
.rejectButton{
    color: $primary_color;
    font-size: 12px;
    width: 75px;
    height: 25px;
    border: 1px solid $black_color3;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.3s ease;

    &:hover{
        //background-color: darken($white_color, 5%);    
        background-color: color.adjust($white_color1, $lightness: -5%);
 
    }


  
}

.acceptButton-inactive{
    background-color: $black_color4;
    color: $white_color1;
    font-size: 12px;
    padding: 5px 25px;
    border-radius: 3px;
    border: 1px solid $black_color4;
    cursor: not-allowed;
  
}
.rejectButton-inactive{
    background-color: $white_color;
    color: $black_color4;
    font-size: 12px;
    padding: 5px 25px;
    border: 1px solid $black_color4;
    border-radius: 3px;
    cursor: not-allowed;

 
}

.collectedButton{
    display: flex;
    align-items: center;
    flex-direction: row;
    gap: 3px;
    background-color: $primary_color;
    color: $white_color1;
    font-size: 11px;
    padding: 4px 6px;
    border-radius: 3px;
    border: 0;
    cursor: pointer;
    transition: background-color 0.3s ease;


    &:hover{
        //background-color: darken($primary_color, 10%);     
        background-color: color.adjust($primary_color, $lightness: -10%);
    }

    span{
        font-size: 11px;
        margin-top: 2px;

    }


  
}

.addButton{
    background-color: $primary_color;
    color: $white_color1;
    font-size: 14px;
    height: -webkit-fill-available;
    padding: 0 10px;
    border-radius: 6px;
    border: 0;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;

    &:hover{
        //background-color: darken($primary_color, 10%);     
        background-color: color.adjust($primary_color, $lightness: -10%);
    }


   

    @media (max-width: $breakpoint-md) {
        font-size: 10px;
        padding: 0 8px;
        border-radius: 4px;
    }
}

.downloadButton{
    background-color: $white_color1;
    color: $black_color;
    height: auto;
    width: 35px;
    border: 1px solid $black_color3;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 35px;

    input{
        border: 0;
        background-color: transparent;
    }

    .downloadIcon{
        font-size: 14px;
    }

    &:hover{
        //background-color: darken($white_color1, 10%);
        background-color: color.adjust($white_color1, $lightness: -10%);

     
    }

    @media (max-width: $breakpoint-lg) {
        padding: 5px 6px;
        border-radius: 5px;

        .downloadIcon {
            font-size: 13px;
        }
    }

    @media (max-width: $breakpoint-md) {
        padding: 4px 5px;
        border-radius: 4px;

        .downloadIcon {
            font-size: 12px;
        }
    }
}



.addButton2{
    background-color: $white_color1;
    color: $black_color;
    height: auto;
    padding: 0 10px;
    border: 1px solid $black_color3;
    border-radius: 6px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;

    input{
        border: 0;
        background-color: transparent;
    }

    .downloadIcon{
        font-size: 14px;
    }

    &:hover{
        //background-color: darken($white_color1, 10%);
        background-color: color.adjust($white_color1, $lightness: -10%);

     
    }

    @media (max-width: $breakpoint-lg) {
        padding: 5px 6px;
        border-radius: 5px;

        .downloadIcon {
            font-size: 13px;
        }
    }

    @media (max-width: $breakpoint-md) {
        padding: 4px 5px;
        border-radius: 4px;

        .downloadIcon {
            font-size: 12px;
        }
    }
}

.paginationButton{
    background-color: $white_color1;
    color: $black_color;
    padding: 2px 6px;
    font-size: 12px;
    border: 1px solid $black_color4;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;

    span{
        font-size: 14px;
    }

    .prev{
        margin-right: 5px;
    }

    .next{
        margin-left: 5px;
    }

    &.active {
        background-color: $primary_color;
        color: $white_color;
        border: 1px solid $black_color;
        box-shadow: 0 0 5px rgba(0, 0, 0, 0.2); 
    }


    &:hover{
        background-color: $primary_color;
        color: $white_color;
        border: 1px solid $black_color;
    }

    @media (max-width: $breakpoint-lg) {
        width: 28px;
        height: 28px;
        font-size: 12px;

    }

    @media (max-width: $breakpoint-md) {
        padding: 4px 5px;
        border-radius: 4px;


    }

}

.paginationButton2{
    background-color: $white_color1;
    color: $black_color;
    padding: 3px 8px;
    font-size: 12px;
    border: 1px solid $black_color4;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;

    span{
        font-size: 14px;
        @media (max-width: $breakpoint-lg) {
            font-size: 12px;
        }
    }

    .prev{
        margin-right: 5px;

      
    }

    .next{
        margin-left: 5px;
    }


    &:hover{
        background-color: $primary_color;
        color: $white_color;
        border: 1px solid $black_color;
    }

    @media (max-width: $breakpoint-lg) {
        padding: 3px 6px;
        font-size: 12px;
    }

    @media (max-width: $breakpoint-md) {
        padding: 2px 4px;
        border-radius: 4px;
        font-size: 10px;


    }
}

.paginationButtonWithoutHover{
    background-color: $white_color1;
    color: $black_color;
    padding: 2px 6px;
    font-size: 12px;
    border: 1px solid $black_color4;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;

    span{
        font-size: 14px;
    }

    .prev{
        margin-right: 5px;
    }

    .next{
        margin-left: 5px;
    }



    @media (max-width: $breakpoint-lg) {
        width: 28px;
        height: 28px;
        font-size: 12px;

    }

    @media (max-width: $breakpoint-md) {
        padding: 4px 5px;
        border-radius: 4px;


    }
}

.paginationButton2 {
    cursor: pointer;
  
    &.disabled {
      cursor: not-allowed;
      opacity: 0.6; 
    }
  }

  .addDocButton{
    background-color:$grayedDark_color ;
    color: $white_color;
    font-size: 11px;
    font-weight: 600;
    border-radius: 6px;
    border: 0;
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 12px;
    span{
        font-weight: 200;
        font-size: 26px;
        margin-right: 10px;
    }

    &:hover{
        //background-color: lighten($grayedDark_color, 10%);    
        background-color: color.adjust($grayedDark_color, $lightness: 10%);
 
        
    }


    @media (max-width: $breakpoint-lg) {
        font-size: 13px; 
    
    }

    @media (max-width: $breakpoint-md) {
        font-size: 12px;
       
    }
}
  
.table-remove-button{
        display: flex;
        justify-content: center;
        align-items: center;
        button{
            cursor: pointer;
            padding: 5px;
            border: 0;
            background: none;

            span{
                font-size: 15px;
                color: $red_color;
            
            }
        }
}
.isAccountedButton {
    display: flex;
    flex-direction: column;
    padding-bottom: 20px;
  
    .content{
        display: flex;
    align-items: center;
        input[type="checkbox"] {
            appearance: none;
            width: 15px;
            height: 15px;
            border: 1px solid $primary_color;
            background-color: white;
            cursor: pointer;
            position: relative;
            color: $primary_color;
            border-radius: 5px;
        
            &:checked {
              background-color: $white_color; /* Tick color */
              border-color: 1px solid $primary_color;
              color: $primary_color;
        
              /* Add a custom tick */
              &::after {
                content: "";
                position: absolute;
                width: 3px;
                height: 6px;
                border: solid $primary_color;
                border-width: 0 2px 2px 0;
                top: 3px;
                left: 5px;
                transform: rotate(45deg);
              }
            }
          }
        
          .label {
            font-size: 11px;
            font-weight: 500;
            color: $black_color2;
            margin-left: 8px; /* Add spacing between checkbox and label */
          }
    }

    
  }

.date-picker-container {
  display: flex;
  flex-direction: column;
  margin-right: 16px;

  label {
    font-size: 11px;
    margin-bottom: 1px;
    color: $black_color3; // Replace with your preferred label color.
  }
}
.date-picker {
    height: 35px;
    padding: 8px 12px;
    border: 1px solid $black_color4;
    border-radius: 6px;
    color: $black_color;
    cursor: pointer;
    font-size: 13px;
    background-color: transparent;

    @media(max-width: 660px){
        width: 100%;
        

    }
  }

  .date-picker2 {
    //height: auto;
    height: 35px;
    padding: 0px 12px;
    border: 1px solid $black_color4;
    border-radius: 6px;
    color: $black_color;
    cursor: pointer;
    font-size: 13px;
    background-color: transparent;

    @media(max-width: 660px){
        width: 100%;

    }
  }

  .delete-bttn{
    background-color: $red_color;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    .material-icons{
        font-size: 15px;
        color: $white_color;
    }
  }