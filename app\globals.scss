@use '../styles/variables' as *;
@use 'tailwindcss/utilities';
@use 'tailwindcss/components';
@use 'tailwindcss/base';



@use '/styles/table.scss' as table;
@use '/styles/buttons' as buttons;
@use '/styles/form' as form;

//@import './styles/variables.scss';
//@import 'tailwindcss/base';
//@import 'tailwindcss/components';
//@import 'tailwindcss/utilities';


/* :root {
  --background: #ffffff;g
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: Arial, Helvetica, sans-serif;
} */

@import url('https://fonts.googleapis.com/css2?family=Urbanist:ital,wght@0,100..900;1,100..900&display=swap');

//@import '/app/styles/table.scss';
//@import '/app/styles/buttons.scss';
//@import 'app/styles/form.scss';
*{
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  font-family: "Urbanist", sans-serif;
  // background-color: #F2F2F2;
}
.page-container{
  display: flex;
  width: 100dvw;
  height: 100dvh;
  overflow: hidden;
  background-color: #F2F2F2;

}
.side-bar-section{
  flex-basis: 70px; 
  flex-shrink: 0;
  background-color: #ffffff;
  overflow: hidden;
  height: 100%;
  transition: flex-basis 0.3s ease-in-out;
  &.expanded {
    flex-basis: 288px;
  }
  &.collapse{
    flex-basis: 70px;

  //   @media(max-width: $breakpoint-md){
  //     flex-basis: 55px;
  // }
  }
}



.content-header-section{
  width: calc(100dvw - 70px);
  
  &.menuExpanded{
    width: calc(100dvw - 288px);
    transition: width 0.3s ease-in-out;
  }

  .content-container{
    height: calc(100dvh - 50px);
    width: 100%;
    overflow-y: auto;
    //padding-right: 10px;
    padding: 0 10px 10px 10px;
  }

  
}

.w-100{
  width: 100%;
}

.w-50{
  width: 50%;
}
.wf-50{
  width: calc(50% - 10px);
}
.wf-100{
  width: calc(100% - 10px);
}
.primary-background{
  background-color: $primary_color;
  color: white;
}
.overall-list-padding{
  // padding: 0 30px 20px 30px;

  // @media(max-width: $breakpoint-md){
  //   padding: 0 20px 20px 20px;

  // }

  // @media(max-width: $breakpoint-sm){
  //   padding: 0 10px 20px 10px;

  // }
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 0.5s ease, transform 0.5s ease;

  &.visible{
    opacity: 1;
    transform: translateY(0);
  }

}

.table-vertical-scroll{
  width: 100%;
  height: calc(100dvh - 273px);
  overflow-y: auto;

}

.table-vertical-scroll-customerList{
  width: 100%;
  height: calc(100dvh - 273px);
  overflow-y: auto;

}



.table-vertical-scroll2{
  width: 100%;
  height: calc(100dvh - 411px);
  overflow-y: auto;
}

.table-vertical-scroll-fullHeight{
  width: 100%;
  height: calc(100dvh - 223px);
  overflow-y: auto;
}

.table-vertical-scroll-fullHeight2{
  width: 100%;
  height: calc(100dvh - 178px);
  overflow-y: auto;
}

.popup-overlay {
  position: absolute;
  background: rgba(0, 0, 0, 0.3); /* Semi-transparent overlay */
  width: 200px; /* Set the width as needed */
  z-index: 1000;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 12px 12px 5px rgba(0, 0, 0, 0.3);

  .popup-content {
    background: #fff;
    border-radius: 5px;
    
  

    h5{
      font-size: 16px;
    }

    .popup-option {
  
      font-size: 12px;
      display: flex;
      align-items: center;
      color: $black_color;
      border-bottom: 1px solid $black_color4;
      padding-left: 3px;
      background-color: #fff;
      transition: background-color 0.2s ease;



      &:last-child {
        border-bottom: none;
        
      }

      &:hover{
        background-color: #D8D7DD;
      }

      &.active {
        background-color: #D8D7DD; 
      }
 
label{
  padding: 8px 10px;
  width: 100%;
}

      /* Remove the default border */
input[type="checkbox"] {
  border: none; /* Removes the border */
  outline: none; /* Removes the outline on focus */
  -webkit-appearance: none; /* Removes the default appearance in Webkit browsers (Chrome, Safari) */
  -moz-appearance: none; /* Removes the default appearance in Firefox */
  appearance: none; /* Standard property to remove default appearance */
  width: 20px; /* Set the size of the checkbox */
  height: 20px; /* Set the size of the checkbox */
  background-color: transparent; /* Ensure background is transparent */
  position: relative; /* Position relative for custom styling */
}

/* Custom styling for checked state */
input[type="checkbox"]:checked::before {
  content: "✔"; /* Unicode checkmark */
  position: absolute;
  top: 0px; /* Position it correctly within the checkbox */
  left: 5px;
  font-size: 16px; /* Size of the checkmark */
}


    }

    button{
      margin: 10px 0 0 3px ;
    }

    
    
  }

  .material-icons.closeIcon {
    cursor: pointer;
  }


}

.error-message{
  color: $red_color;
  font-size: 10px;
  font-weight: 600;
  margin-top: 3px;
}

.file-info {
  font-size: 13px;
  color: #6c757d;  // Light gray color
  margin-top: 5px;
}

.view-file-icon{
  width: 26px;
  cursor: pointer;
  transition:  transform ease-in-out 0.3s;
  &:hover{
      transform: scale(1.1);
  }
}

.scroll-bar-1{
  &::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  &::-webkit-scrollbar-track {
    background: #f0f0f0;
    border-radius: 10px; 
  }

  &::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, $black_color2, $black_color3);
    border-radius: 10px; 
    border: 2px solid #f0f0f0;
  }

  &::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, $black_color, $black_color2); 
  }
}



@media screen and (max-width: 600px) {
  .wf-50{
    width: calc(100% - 10px);
  }
}

.available{
  width: 70px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #E6FCF1;
  border: 1px solid #62D150;
  border-radius: 13px;
   
  span{
    color: $green_color2;
    font-size: 10px;
    font-weight: 600;
    
  }
 }

 .offline{
  width: 70px;
  height: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #FFF8F8;
    border: 1px solid #F3C6C6;
  border-radius: 13px;
  span{
    color: #E51818;
    font-size: 10px;
    font-weight: 600;
  }
 }



.no-data-found-div {
  padding: 20px;
  width: 100%;
  display: flex;
  flex-direction: column;
  place-content: center flex-start;
  align-items: center;
  justify-content: center;
  gap: 10px;

  h5 {
      font-weight: 600;
      margin-bottom: 0;
      font-size: 22px;
  }

  h6 {
      color: $gray_clr;
      margin-bottom: 0;
      font-size: 18px;
  }
}    

.status{
  &.success{
    color: $green_color2;
  }
  &.failed{
    color: $red_color;
  }
  &.inprogress{
    color: $rating_color;
  }
}

//media queries


@media(max-width : $breakpoint-md){

  .side-bar-section{

    &.collapse{
      flex-basis: 55px;

    }
  }

}

@media(max-width : 500px){

  .side-bar-section{

    &.collapse{
      flex-basis: 0;
    }
  }

  .content-header-section{
    width: 100%;

    .content-container{
      padding: 0 10px 0 10px ;
    }
  }

  .table-vertical-scroll{
    height: calc(100dvh - 310px);
  }

  .table-vertical-scroll-customerList{
    height: calc(100dvh - 340px);
  }

  .table-vertical-scroll-fullHeight{
    height: calc(100dvh - 270px);
  }

  .table-vertical-scroll-fullHeight2{
    height: calc(100dvh - 195px);
  }
}

.status-indicater{
  position: absolute;
  width: 15px;
  height: 15px;
  border-radius: 50%;
  bottom: 5px;
  right: 0px;
  background-color: $green_color;
}

.offline-status-message{
  max-width: 150px;
  padding: 8px 15px;
  margin-top: 20px;
  height: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #FFF8F8;
    border: 1px solid #F3C6C6;
  border-radius: 13px;
  span{
    color: #E51818;
    font-size: 11px;
    font-weight: 600;
  }
 }