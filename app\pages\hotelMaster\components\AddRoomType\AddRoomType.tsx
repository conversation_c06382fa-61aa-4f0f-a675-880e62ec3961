// import React, { useState, useRef, useEffect, useCallback } from "react";

// import { FiX, FiUpload, FiImage } from "react-icons/fi";
// import './AddRoomType.scss';

// interface RoomType {
//   id: string;
//   name: string;
//   description: string;
//   capacity: number;
//   pricePerNight: string;
//   amenities: string[];
//   availability: number;
//   images?: File[];
//   // Additional comprehensive fields
//   roomSize: string; // e.g., "45 sqm"
//   bedType: string; // e.g., "King Size", "Twin Beds", "Queen Size"
//   viewType: string; // e.g., "Ocean View", "City View", "Garden View"
//   floorRange: string; // e.g., "10-15", "Ground Floor"
//   smokingPolicy: 'smoking' | 'non-smoking' | 'both';
//   bathroomType: string; // e.g., "Private Bathroom", "Shared Bathroom"
//   balcony: boolean;
//   airConditioning: boolean;
//   wifi: boolean;
//   maxOccupancy: number;
//   extraBedAvailable: boolean;
//   extraBedPrice: string;
//   roomFeatures: string[]; // Additional room-specific features
//   cancellationPolicy: string;
//   checkInTime: string;
//   checkOutTime: string;
// }

// interface RoomTypeModalProps {
//   isOpen: boolean;
//   onClose: () => void;
//   onSubmit: (roomType: Omit<RoomType, 'id'>) => void;
//   roomType?: RoomType | null; // Optional room type for editing
//   mode?: 'add' | 'edit' | 'view'; // Modal mode
// }

// const RoomTypeModal: React.FC<RoomTypeModalProps> = ({
//   isOpen,
//   onClose,
//   onSubmit,
//   roomType = null,
//   mode = 'add'
// }) => {
//   const getInitialFormData = useCallback(() => ({
//     name: "",
//     description: "",
//     capacity: 1,
//     pricePerNight: "",
//     availability: 0,
//     amenities: [""],
//     images: [] as File[],
//     // Additional comprehensive fields
//     roomSize: "",
//     bedType: "",
//     viewType: "",
//     floorRange: "",
//     smokingPolicy: 'non-smoking' as 'smoking' | 'non-smoking' | 'both',
//     bathroomType: "",
//     balcony: false,
//     airConditioning: true,
//     wifi: true,
//     maxOccupancy: 1,
//     extraBedAvailable: false,
//     extraBedPrice: "",
//     roomFeatures: [""],
//     cancellationPolicy: "",
//     checkInTime: "15:00",
//     checkOutTime: "11:00"
//   }), []);

//   const [formData, setFormData] = useState(getInitialFormData);
//   const [errors, setErrors] = useState<{[key: string]: string}>({});
//   const [dragActive, setDragActive] = useState(false);
//   const fileInputRef = useRef<HTMLInputElement>(null);

//   // Populate form data when editing
//   useEffect(() => {
//     if (mode === 'edit' && roomType) {
//       setFormData({
//         name: roomType.name,
//         description: roomType.description,
//         capacity: roomType.capacity,
//         pricePerNight: roomType.pricePerNight,
//         availability: roomType.availability,
//         amenities: roomType.amenities.length > 0 ? roomType.amenities : [""],
//         images: roomType.images || [],
//         // Additional fields with fallback values
//         roomSize: roomType.roomSize || "",
//         bedType: roomType.bedType || "",
//         viewType: roomType.viewType || "",
//         floorRange: roomType.floorRange || "",
//         smokingPolicy: roomType.smokingPolicy || 'non-smoking',
//         bathroomType: roomType.bathroomType || "",
//         balcony: roomType.balcony || false,
//         airConditioning: roomType.airConditioning !== undefined ? roomType.airConditioning : true,
//         wifi: roomType.wifi !== undefined ? roomType.wifi : true,
//         maxOccupancy: roomType.maxOccupancy || roomType.capacity,
//         extraBedAvailable: roomType.extraBedAvailable || false,
//         extraBedPrice: roomType.extraBedPrice || "",
//         roomFeatures: roomType.roomFeatures && roomType.roomFeatures.length > 0 ? roomType.roomFeatures : [""],
//         cancellationPolicy: roomType.cancellationPolicy || "",
//         checkInTime: roomType.checkInTime || "15:00",
//         checkOutTime: roomType.checkOutTime || "11:00"
//       });
//     } else {
//       setFormData(getInitialFormData());
//     }
//     setErrors({});
//   }, [mode, roomType, isOpen, getInitialFormData]);

//   const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
//     const { name, value, type } = e.target;

//     // Clear error when user starts typing
//     if (errors[name]) {
//       setErrors(prev => ({
//         ...prev,
//         [name]: ""
//       }));
//     }

//     if (type === 'checkbox') {
//       const target = e.target as HTMLInputElement;
//       setFormData(prev => ({
//         ...prev,
//         [name]: target.checked
//       }));
//     } else {
//       setFormData(prev => ({
//         ...prev,
//         [name]: name === 'capacity' || name === 'availability' || name === 'maxOccupancy'
//           ? (value === '' ? 0 : parseInt(value) || 0)
//           : value
//       }));
//     }
//   };

//   const handleAmenityChange = (index: number, value: string) => {
//     const newAmenities = [...formData.amenities];
//     newAmenities[index] = value;
//     setFormData(prev => ({
//       ...prev,
//       amenities: newAmenities
//     }));
//   };

//   const addAmenityField = () => {
//     setFormData(prev => ({
//       ...prev,
//       amenities: [...prev.amenities, ""]
//     }));
//   };

//   const removeAmenityField = (index: number) => {
//     if (formData.amenities.length > 1) {
//       const newAmenities = formData.amenities.filter((_, i) => i !== index);
//       setFormData(prev => ({
//         ...prev,
//         amenities: newAmenities
//       }));
//     }
//   };

//   // Image handling functions
//   const validateImageFile = (file: File): boolean => {
//     const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif'];
//     const maxSize = 10 * 1024 * 1024; // 10MB in bytes

//     if (!allowedTypes.includes(file.type)) {
//       setErrors(prev => ({
//         ...prev,
//         images: 'Only PNG, JPG, JPEG, and GIF files are allowed'
//       }));
//       return false;
//     }

//     if (file.size > maxSize) {
//       setErrors(prev => ({
//         ...prev,
//         images: 'File size must be less than 10MB'
//       }));
//       return false;
//     }

//     return true;
//   };

//   const handleFileSelect = (files: FileList | null) => {
//     if (!files) return;

//     const validFiles: File[] = [];
//     const fileArray = Array.from(files);

//     // Clear previous image errors
//     setErrors(prev => {
//       const newErrors = { ...prev };
//       delete newErrors.images;
//       return newErrors;
//     });

//     for (const file of fileArray) {
//       if (validateImageFile(file)) {
//         validFiles.push(file);
//       }
//     }

//     if (validFiles.length > 0) {
//       setFormData(prev => ({
//         ...prev,
//         images: [...prev.images, ...validFiles]
//       }));
//     }
//   };

//   const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
//     handleFileSelect(e.target.files);
//     // Reset input value to allow selecting the same file again
//     if (fileInputRef.current) {
//       fileInputRef.current.value = '';
//     }
//   };

//   const handleDrag = (e: React.DragEvent) => {
//     e.preventDefault();
//     e.stopPropagation();
//     if (e.type === "dragenter" || e.type === "dragover") {
//       setDragActive(true);
//     } else if (e.type === "dragleave") {
//       setDragActive(false);
//     }
//   };

//   const handleDrop = (e: React.DragEvent) => {
//     e.preventDefault();
//     e.stopPropagation();
//     setDragActive(false);

//     if (e.dataTransfer.files && e.dataTransfer.files[0]) {
//       handleFileSelect(e.dataTransfer.files);
//     }
//   };

//   const removeImage = (index: number) => {
//     setFormData(prev => ({
//       ...prev,
//       images: prev.images.filter((_, i) => i !== index)
//     }));
//   };

//   const handleUploadClick = () => {
//     fileInputRef.current?.click();
//   };

//   const validateForm = () => {
//     const newErrors: {[key: string]: string} = {};

//     if (!formData.name.trim()) {
//       newErrors.name = "Room type name is required";
//     }

//     if (!formData.description.trim()) {
//       newErrors.description = "Description is required";
//     }

//     if (!formData.pricePerNight.trim()) {
//       newErrors.pricePerNight = "Price per night is required";
//     }

//     if (formData.capacity < 1) {
//       newErrors.capacity = "Capacity must be at least 1";
//     }

//     if (formData.availability < 0) {
//       newErrors.availability = "Availability cannot be negative";
//     }

//     if (formData.maxOccupancy < 1) {
//       newErrors.maxOccupancy = "Max occupancy must be at least 1";
//     }

//     if (formData.maxOccupancy < formData.capacity) {
//       newErrors.maxOccupancy = "Max occupancy cannot be less than capacity";
//     }

//     // Validate amenities - at least one non-empty amenity
//     const validAmenities = formData.amenities.filter(amenity => amenity.trim() !== "");
//     if (validAmenities.length === 0) {
//       newErrors.amenities = "At least one amenity is required";
//     }

//     // Validate extra bed price if extra bed is available
//     if (formData.extraBedAvailable && !formData.extraBedPrice.trim()) {
//       newErrors.extraBedPrice = "Extra bed price is required when extra bed is available";
//     }

//     // Validate check-in and check-out times
//     if (!formData.checkInTime) {
//       newErrors.checkInTime = "Check-in time is required";
//     }

//     if (!formData.checkOutTime) {
//       newErrors.checkOutTime = "Check-out time is required";
//     }

//     // Validate that check-out time is after check-in time
//     if (formData.checkInTime && formData.checkOutTime) {
//       const checkIn = new Date(`2000-01-01T${formData.checkInTime}`);
//       const checkOut = new Date(`2000-01-01T${formData.checkOutTime}`);

//       if (checkOut <= checkIn) {
//         newErrors.checkOutTime = "Check-out time must be after check-in time";
//       }
//     }

//     setErrors(newErrors);
//     return Object.keys(newErrors).length === 0;
//   };

//   const handleSubmit = (e: React.FormEvent) => {
//     e.preventDefault();

//     if (!validateForm()) {
//       return;
//     }

//     const filteredAmenities = formData.amenities.filter(amenity => amenity.trim() !== "");

//     onSubmit({
//       ...formData,
//       amenities: filteredAmenities,
//       images: formData.images
//     });

//     // Reset form only if adding (not editing)
//     if (mode === 'add') {
//       setFormData(getInitialFormData());
//     }

//     setErrors({});
//     onClose();
//   };

//   const handleClose = () => {
//     // Reset form and errors when closing
//     if (mode === 'add') {
//       setFormData(getInitialFormData());
//     }
//     setErrors({});
//     onClose();
//   };

//   const handleBackdropClick = (e: React.MouseEvent) => {
//     // Close modal when clicking on backdrop
//     if (e.target === e.currentTarget) {
//       handleClose();
//     }
//   };

//   if (!isOpen) return null;

//   const modalTitle = mode === 'edit' ? 'Edit Room Type' : mode === 'view' ? 'View Room Type' : 'Add New Room Type';
//   const submitButtonText = mode === 'edit' ? 'Update Room Type' : 'Add Room Type';
//   const isViewMode = mode === 'view';

//   return (
//     <div
//       className={`agent-create-container ${isOpen ? 'show' : ''}`}
//       onClick={handleBackdropClick}
//     >
//       <div className="agent-create-form full-width">
//         <div className="closeIcon" onClick={handleClose}>
//           <FiX />
//         </div>

//         <h3>{modalTitle}</h3>

//         <form onSubmit={handleSubmit} className="form-content">
//           <div className="form-row">
//             <div className="input-field-container">
//               <div className="input-field">
//                 <label>Room Type Name *</label>
//                 <input
//                   type="text"
//                   name="name"
//                   value={formData.name}
//                   onChange={handleInputChange}
//                   placeholder="e.g., Deluxe Suite"
//                   className={errors.name ? 'error' : ''}
//                   readOnly={isViewMode}
//                 />
//                 {errors.name && <span className="error-message">{errors.name}</span>}
//               </div>
//             </div>
//           </div>

//           <div className="form-row">
//             <div className="input-field-container">
//               <div className="input-field">
//                 <label>Description *</label>
//                 <textarea
//                   name="description"
//                   value={formData.description}
//                   onChange={handleInputChange}
//                   placeholder="Describe the room type..."
//                   rows={3}
//                   className={errors.description ? 'error' : ''}
//                 />
//                 {errors.description && <span className="error-message">{errors.description}</span>}
//               </div>
//             </div>
//           </div>

//           <div className="form-row double">
//             <div className="input-field-container">
//               <div className="input-field">
//                 <label>Capacity (Guests) *</label>
//                 <input
//                   type="number"
//                   name="capacity"
//                   value={formData.capacity}
//                   onChange={handleInputChange}
//                   min="1"
//                   placeholder="1"
//                   className={errors.capacity ? 'error' : ''}
//                 />
//                 {errors.capacity && <span className="error-message">{errors.capacity}</span>}
//               </div>
//             </div>
//             <div className="input-field-container">
//               <div className="input-field">
//                 <label>Price Per Night *</label>
//                 <input
//                   type="text"
//                   name="pricePerNight"
//                   value={formData.pricePerNight}
//                   onChange={handleInputChange}
//                   placeholder="$150"
//                   className={errors.pricePerNight ? 'error' : ''}
//                 />
//                 {errors.pricePerNight && <span className="error-message">{errors.pricePerNight}</span>}
//               </div>
//             </div>
//           </div>

//           <div className="form-row">
//             <div className="input-field-container">
//               <div className="input-field">
//                 <label>Available Rooms *</label>
//                 <input
//                   type="number"
//                   name="availability"
//                   value={formData.availability}
//                   onChange={handleInputChange}
//                   min="0"
//                   placeholder="5"
//                   className={errors.availability ? 'error' : ''}
//                 />
//                 {errors.availability && <span className="error-message">{errors.availability}</span>}
//               </div>
//             </div>
//           </div>

//           {/* Room Size and Bed Type */}
//           <div className="form-row double">
//             <div className="input-field-container">
//               <div className="input-field">
//                 <label>Room Size</label>
//                 <input
//                   type="text"
//                   name="roomSize"
//                   value={formData.roomSize}
//                   onChange={handleInputChange}
//                   placeholder="e.g., 45 sqm"
//                   className={errors.roomSize ? 'error' : ''}
//                 />
//                 {errors.roomSize && <span className="error-message">{errors.roomSize}</span>}
//               </div>
//             </div>
//             <div className="input-field-container">
//               <div className="input-field">
//                 <label>Bed Type</label>
//                 <select
//                   name="bedType"
//                   value={formData.bedType}
//                   onChange={handleInputChange}
//                   className={errors.bedType ? 'error' : ''}
//                 >
//                   <option value="">Select bed type</option>
//                   <option value="Single Bed">Single Bed</option>
//                   <option value="Twin Beds">Twin Beds</option>
//                   <option value="Double Bed">Double Bed</option>
//                   <option value="Queen Size">Queen Size</option>
//                   <option value="King Size">King Size</option>
//                   <option value="Sofa Bed">Sofa Bed</option>
//                   <option value="Bunk Bed">Bunk Bed</option>
//                 </select>
//                 {errors.bedType && <span className="error-message">{errors.bedType}</span>}
//               </div>
//             </div>
//           </div>

//           {/* View Type and Floor Range */}
//           <div className="form-row double">
//             <div className="input-field-container">
//               <div className="input-field">
//                 <label>View Type</label>
//                 <select
//                   name="viewType"
//                   value={formData.viewType}
//                   onChange={handleInputChange}
//                   className={errors.viewType ? 'error' : ''}
//                 >
//                   <option value="">Select view type</option>
//                   <option value="Ocean View">Ocean View</option>
//                   <option value="City View">City View</option>
//                   <option value="Garden View">Garden View</option>
//                   <option value="Mountain View">Mountain View</option>
//                   <option value="Pool View">Pool View</option>
//                   <option value="Courtyard View">Courtyard View</option>
//                   <option value="No View">No View</option>
//                 </select>
//                 {errors.viewType && <span className="error-message">{errors.viewType}</span>}
//               </div>
//             </div>
//             <div className="input-field-container">
//               <div className="input-field">
//                 <label>Floor Range</label>
//                 <input
//                   type="text"
//                   name="floorRange"
//                   value={formData.floorRange}
//                   onChange={handleInputChange}
//                   placeholder="e.g., 10-15, Ground Floor"
//                   className={errors.floorRange ? 'error' : ''}
//                 />
//                 {errors.floorRange && <span className="error-message">{errors.floorRange}</span>}
//               </div>
//             </div>
//           </div>

//           {/* Max Occupancy and Bathroom Type */}
//           <div className="form-row double">
//             <div className="input-field-container">
//               <div className="input-field">
//                 <label>Max Occupancy *</label>
//                 <input
//                   type="number"
//                   name="maxOccupancy"
//                   value={formData.maxOccupancy}
//                   onChange={handleInputChange}
//                   min="1"
//                   placeholder="4"
//                   className={errors.maxOccupancy ? 'error' : ''}
//                 />
//                 {errors.maxOccupancy && <span className="error-message">{errors.maxOccupancy}</span>}
//               </div>
//             </div>
//             <div className="input-field-container">
//               <div className="input-field">
//                 <label>Bathroom Type</label>
//                 <select
//                   name="bathroomType"
//                   value={formData.bathroomType}
//                   onChange={handleInputChange}
//                   className={errors.bathroomType ? 'error' : ''}
//                 >
//                   <option value="">Select bathroom type</option>
//                   <option value="Private Bathroom">Private Bathroom</option>
//                   <option value="Shared Bathroom">Shared Bathroom</option>
//                   <option value="Ensuite Bathroom">Ensuite Bathroom</option>
//                   <option value="Half Bathroom">Half Bathroom</option>
//                 </select>
//                 {errors.bathroomType && <span className="error-message">{errors.bathroomType}</span>}
//               </div>
//             </div>
//           </div>

//           {/* Smoking Policy */}
//           <div className="form-row">
//             <div className="input-field-container">
//               <div className="input-field">
//                 <label>Smoking Policy</label>
//                 <select
//                   name="smokingPolicy"
//                   value={formData.smokingPolicy}
//                   onChange={handleInputChange}
//                   className={errors.smokingPolicy ? 'error' : ''}
//                 >
//                   <option value="non-smoking">Non-Smoking</option>
//                   <option value="smoking">Smoking Allowed</option>
//                   <option value="both">Both Available</option>
//                 </select>
//                 {errors.smokingPolicy && <span className="error-message">{errors.smokingPolicy}</span>}
//               </div>
//             </div>
//           </div>

//           {/* Room Features Checkboxes */}
//           <div className="form-row">
//             <div className="input-field-container">
//               <div className="input-field">
//                 <label>Room Features</label>
//                 <div className="checkbox-grid">
//                   <div className="checkbox-item">
//                     <input
//                       type="checkbox"
//                       id="balcony"
//                       name="balcony"
//                       checked={formData.balcony}
//                       onChange={(e) => setFormData(prev => ({ ...prev, balcony: e.target.checked }))}
//                     />
//                     <label htmlFor="balcony">Balcony</label>
//                   </div>
//                   <div className="checkbox-item">
//                     <input
//                       type="checkbox"
//                       id="airConditioning"
//                       name="airConditioning"
//                       checked={formData.airConditioning}
//                       onChange={(e) => setFormData(prev => ({ ...prev, airConditioning: e.target.checked }))}
//                     />
//                     <label htmlFor="airConditioning">Air Conditioning</label>
//                   </div>
//                   <div className="checkbox-item">
//                     <input
//                       type="checkbox"
//                       id="wifi"
//                       name="wifi"
//                       checked={formData.wifi}
//                       onChange={(e) => setFormData(prev => ({ ...prev, wifi: e.target.checked }))}
//                     />
//                     <label htmlFor="wifi">Free WiFi</label>
//                   </div>
//                   <div className="checkbox-item">
//                     <input
//                       type="checkbox"
//                       id="extraBedAvailable"
//                       name="extraBedAvailable"
//                       checked={formData.extraBedAvailable}
//                       onChange={(e) => setFormData(prev => ({ ...prev, extraBedAvailable: e.target.checked }))}
//                     />
//                     <label htmlFor="extraBedAvailable">Extra Bed Available</label>
//                   </div>
//                 </div>
//               </div>
//             </div>
//           </div>

//           {/* Extra Bed Price - Show only if extra bed is available */}
//           {formData.extraBedAvailable && (
//             <div className="form-row">
//               <div className="input-field-container">
//                 <div className="input-field">
//                   <label>Extra Bed Price</label>
//                   <input
//                     type="text"
//                     name="extraBedPrice"
//                     value={formData.extraBedPrice}
//                     onChange={handleInputChange}
//                     placeholder="e.g., $50/night"
//                     className={errors.extraBedPrice ? 'error' : ''}
//                   />
//                   {errors.extraBedPrice && <span className="error-message">{errors.extraBedPrice}</span>}
//                 </div>
//               </div>
//             </div>
//           )}

//           {/* Check-in and Check-out Times */}
//           <div className="form-row double">
//             <div className="input-field-container">
//               <div className="input-field">
//                 <label>Check-in Time</label>
//                 <input
//                   type="time"
//                   name="checkInTime"
//                   value={formData.checkInTime}
//                   onChange={handleInputChange}
//                   className={errors.checkInTime ? 'error' : ''}
//                 />
//                 {errors.checkInTime && <span className="error-message">{errors.checkInTime}</span>}
//               </div>
//             </div>
//             <div className="input-field-container">
//               <div className="input-field">
//                 <label>Check-out Time</label>
//                 <input
//                   type="time"
//                   name="checkOutTime"
//                   value={formData.checkOutTime}
//                   onChange={handleInputChange}
//                   className={errors.checkOutTime ? 'error' : ''}
//                 />
//                 {errors.checkOutTime && <span className="error-message">{errors.checkOutTime}</span>}
//               </div>
//             </div>
//           </div>

//           {/* Cancellation Policy */}
//           <div className="form-row">
//             <div className="input-field-container">
//               <div className="input-field">
//                 <label>Cancellation Policy</label>
//                 <textarea
//                   name="cancellationPolicy"
//                   value={formData.cancellationPolicy}
//                   onChange={handleInputChange}
//                   placeholder="Describe the cancellation policy..."
//                   rows={3}
//                   className={errors.cancellationPolicy ? 'error' : ''}
//                 />
//                 {errors.cancellationPolicy && <span className="error-message">{errors.cancellationPolicy}</span>}
//               </div>
//             </div>
//           </div>

//           {/* Room Features List */}
//           <div className="form-row">
//             <div className="input-field-container">
//               <div className="input-field">
//                 <label>Additional Room Features</label>
//                 <div className="amenities-list">
//                   {formData.roomFeatures.map((feature, index) => (
//                     <div key={index} className="amenity-row">
//                       <input
//                         type="text"
//                         value={feature}
//                         onChange={(e) => {
//                           const newFeatures = [...formData.roomFeatures];
//                           newFeatures[index] = e.target.value;
//                           setFormData(prev => ({ ...prev, roomFeatures: newFeatures }));
//                         }}
//                         placeholder="e.g., Mini Bar, Safe, Coffee Machine"
//                       />
//                       {formData.roomFeatures.length > 1 && (
//                         <button
//                           type="button"
//                           onClick={() => {
//                             if (formData.roomFeatures.length > 1) {
//                               const newFeatures = formData.roomFeatures.filter((_, i) => i !== index);
//                               setFormData(prev => ({ ...prev, roomFeatures: newFeatures }));
//                             }
//                           }}
//                           className="remove-amenity-btn"
//                           aria-label="Remove feature"
//                         >
//                           <FiX />
//                         </button>
//                       )}
//                     </div>
//                   ))}
//                 </div>
//               </div>
//             </div>
//           </div>

//           <div className="addMoreField">
//             <p onClick={() => setFormData(prev => ({ ...prev, roomFeatures: [...prev.roomFeatures, ""] }))} role="button" tabIndex={0}>
//               + Add another room feature
//             </p>
//           </div>

//           <div className="form-row">
//             <div className="input-field-container">
//               <div className="input-field">
//                 <label>Amenities</label>
//                 <div className="amenities-list">
//                   {formData.amenities.map((amenity, index) => (
//                     <div key={index} className="amenity-row">
//                       <input
//                         type="text"
//                         value={amenity}
//                         onChange={(e) => handleAmenityChange(index, e.target.value)}
//                         placeholder="e.g., Free WiFi"
//                       />
//                       {formData.amenities.length > 1 && (
//                         <button
//                           type="button"
//                           onClick={() => removeAmenityField(index)}
//                           className="remove-amenity-btn"
//                           aria-label="Remove amenity"
//                         >
//                           <FiX />
//                         </button>
//                       )}
//                     </div>
//                   ))}
//                 </div>
//               </div>
//             </div>
//           </div>

//           <div className="addMoreField">
//             <p onClick={addAmenityField} role="button" tabIndex={0}>
//               + Add another amenity
//             </p>
//           </div>

//           {/* Enhanced File Upload Section */}
//           <div
//             className={`fileUpload ${dragActive ? 'drag-active' : ''}`}
//             onDragEnter={handleDrag}
//             onDragLeave={handleDrag}
//             onDragOver={handleDrag}
//             onDrop={handleDrop}
//             onClick={handleUploadClick}
//           >
//             <input
//               ref={fileInputRef}
//               type="file"
//               multiple
//               accept="image/png,image/jpeg,image/jpg,image/gif"
//               onChange={handleFileInputChange}
//               style={{ display: 'none' }}
//             />
//             <div className="uploadIcon">
//               <span>
//                 <FiUpload />
//               </span>
//             </div>
//             <div className="desc">
//               <span>Click to upload</span> room images or drag and drop
//             </div>
//             <div className="fileFormat">
//               PNG, JPG, GIF up to 10MB
//             </div>
//           </div>

//           {/* Display upload errors */}
//           {errors.images && (
//             <div className="error-message" style={{ textAlign: 'center', marginTop: '10px' }}>
//               {errors.images}
//             </div>
//           )}

//           {/* Display selected images */}
//           {formData.images.length > 0 && (
//             <div className="selected-images">
//               <label>Selected Images ({formData.images.length})</label>
//               <div className="images-preview">
//                 {formData.images.map((file, index) => (
//                   <div key={index} className="image-preview-item">
//                     <div className="image-info">
//                       <FiImage />
//                       <span className="image-name">{file.name}</span>
//                       <span className="image-size">
//                         ({(file.size / 1024 / 1024).toFixed(2)} MB)
//                       </span>
//                     </div>
//                     <button
//                       type="button"
//                       onClick={() => removeImage(index)}
//                       className="remove-image-btn"
//                       aria-label="Remove image"
//                     >
//                       <FiX />
//                     </button>
//                   </div>
//                 ))}
//               </div>
//             </div>
//           )}

//           {!isViewMode && (
//             <div className="SubmitBtn">
//               <button
//                 type="submit"
//                 className="submit-button"
//               >
//                 {submitButtonText}
//               </button>
//             </div>
//           )}
//         </form>
//       </div>
//     </div>
//   );
// };

// export default RoomTypeModal;



import React, { useState, useRef, useEffect, useCallback } from "react";
import { FiX, FiUpload, FiImage } from "react-icons/fi";
import './AddRoomType.scss';

interface RoomType {
  id: string;
  name: string;
  description: string;
  capacity: number;
  pricePerNight: string;
  amenities: string[];
  availability: number;
  images?: File[];
  // Additional comprehensive fields
  roomSize: string; // e.g., "45 sqm"
  bedType: string; // e.g., "King Size", "Twin Beds", "Queen Size"
  viewType: string; // e.g., "Ocean View", "City View", "Garden View"
  floorRange: string; // e.g., "10-15", "Ground Floor"
  smokingPolicy: 'smoking' | 'non-smoking' | 'both';
  bathroomType: string; // e.g., "Private Bathroom", "Shared Bathroom"
  balcony: boolean;
  airConditioning: boolean;
  wifi: boolean;
  maxOccupancy: number;
  extraBedAvailable: boolean;
  extraBedPrice: string;
  roomFeatures: string[]; // Additional room-specific features
  cancellationPolicy: string;
  checkInTime: string;
  checkOutTime: string;
}

interface RoomTypeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (roomType: Omit<RoomType, 'id'>) => void;
  roomType?: RoomType | null; // Optional room type for editing
  mode?: 'add' | 'edit' | 'view'; // Modal mode
}

const RoomTypeModal: React.FC<RoomTypeModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  roomType = null,
  mode = 'add'
}) => {
  const getInitialFormData = useCallback(() => ({
    name: "",
    description: "",
    capacity: 1,
    pricePerNight: "",
    availability: 0,
    amenities: [""],
    images: [] as File[],
    // Additional comprehensive fields
    roomSize: "",
    bedType: "",
    viewType: "",
    floorRange: "",
    smokingPolicy: 'non-smoking' as 'smoking' | 'non-smoking' | 'both',
    bathroomType: "",
    balcony: false,
    airConditioning: true,
    wifi: true,
    maxOccupancy: 1,
    extraBedAvailable: false,
    extraBedPrice: "",
    roomFeatures: [""],
    cancellationPolicy: "",
    checkInTime: "15:00",
    checkOutTime: "11:00"
  }), []);

  const [formData, setFormData] = useState(getInitialFormData);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Populate form data when editing
  useEffect(() => {
    if (mode === 'edit' && roomType) {
      setFormData({
        name: roomType.name,
        description: roomType.description,
        capacity: roomType.capacity,
        pricePerNight: roomType.pricePerNight,
        availability: roomType.availability,
        amenities: roomType.amenities.length > 0 ? roomType.amenities : [""],
        images: roomType.images || [],
        // Additional fields with fallback values
        roomSize: roomType.roomSize || "",
        bedType: roomType.bedType || "",
        viewType: roomType.viewType || "",
        floorRange: roomType.floorRange || "",
        smokingPolicy: roomType.smokingPolicy || 'non-smoking',
        bathroomType: roomType.bathroomType || "",
        balcony: roomType.balcony || false,
        airConditioning: roomType.airConditioning !== undefined ? roomType.airConditioning : true,
        wifi: roomType.wifi !== undefined ? roomType.wifi : true,
        maxOccupancy: roomType.maxOccupancy || roomType.capacity,
        extraBedAvailable: roomType.extraBedAvailable || false,
        extraBedPrice: roomType.extraBedPrice || "",
        roomFeatures: roomType.roomFeatures && roomType.roomFeatures.length > 0 ? roomType.roomFeatures : [""],
        cancellationPolicy: roomType.cancellationPolicy || "",
        checkInTime: roomType.checkInTime || "15:00",
        checkOutTime: roomType.checkOutTime || "11:00"
      });
    } else {
      setFormData(getInitialFormData());
    }
    setErrors({});
  }, [mode, roomType, isOpen, getInitialFormData]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ""
      }));
    }

    if (type === 'checkbox') {
      const target = e.target as HTMLInputElement;
      setFormData(prev => ({
        ...prev,
        [name]: target.checked
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: name === 'capacity' || name === 'availability' || name === 'maxOccupancy'
          ? (value === '' ? 0 : parseInt(value) || 0)
          : value
      }));
    }
  };

  const handleAmenityChange = (index: number, value: string) => {
    const newAmenities = [...formData.amenities];
    newAmenities[index] = value;
    setFormData(prev => ({
      ...prev,
      amenities: newAmenities
    }));
  };

  const addAmenityField = () => {
    setFormData(prev => ({
      ...prev,
      amenities: [...prev.amenities, ""]
    }));
  };

  const removeAmenityField = (index: number) => {
    if (formData.amenities.length > 1) {
      const newAmenities = formData.amenities.filter((_, i) => i !== index);
      setFormData(prev => ({
        ...prev,
        amenities: newAmenities
      }));
    }
  };

  // Image handling functions
  const validateImageFile = (file: File): boolean => {
    const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif'];
    const maxSize = 10 * 1024 * 1024; // 10MB in bytes

    if (!allowedTypes.includes(file.type)) {
      setErrors(prev => ({
        ...prev,
        images: 'Only PNG, JPG, JPEG, and GIF files are allowed'
      }));
      return false;
    }

    if (file.size > maxSize) {
      setErrors(prev => ({
        ...prev,
        images: 'File size must be less than 10MB'
      }));
      return false;
    }

    return true;
  };

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    const validFiles: File[] = [];
    const fileArray = Array.from(files);

    // Clear previous image errors
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors.images;
      return newErrors;
    });

    for (const file of fileArray) {
      if (validateImageFile(file)) {
        validFiles.push(file);
      }
    }

    if (validFiles.length > 0) {
      setFormData(prev => ({
        ...prev,
        images: [...prev.images, ...validFiles]
      }));
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files);
    // Reset input value to allow selecting the same file again
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files);
    }
  };

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }));
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!formData.name.trim()) {
      newErrors.name = "Room type name is required";
    }

    if (!formData.description.trim()) {
      newErrors.description = "Description is required";
    }

    if (!formData.pricePerNight.trim()) {
      newErrors.pricePerNight = "Price per night is required";
    }

    if (formData.capacity < 1) {
      newErrors.capacity = "Capacity must be at least 1";
    }

    if (formData.availability < 0) {
      newErrors.availability = "Availability cannot be negative";
    }

    if (formData.maxOccupancy < 1) {
      newErrors.maxOccupancy = "Max occupancy must be at least 1";
    }

    if (formData.maxOccupancy < formData.capacity) {
      newErrors.maxOccupancy = "Max occupancy cannot be less than capacity";
    }
      if (formData.pricePerNight.trim() && !/^\$?\d+(\.\d{2})?$/.test(formData.pricePerNight.trim())) {
    newErrors.pricePerNight = "Price must be in valid format (e.g., $150 or 150.00)";
  }

  // Add room size validation
  if (formData.roomSize.trim() && !/^\d+\s*(sqm|sq ft|m²|ft²)$/i.test(formData.roomSize.trim())) {
    newErrors.roomSize = "Room size must include unit (e.g., 45 sqm, 480 sq ft)";
  }

  // Validate floor range format
  if (formData.floorRange.trim() && !/^(\d+(-\d+)?|ground floor)$/i.test(formData.floorRange.trim())) {
    newErrors.floorRange = "Floor range must be in format: '10-15' or 'Ground Floor'";
  }

    // Validate amenities - at least one non-empty amenity
    const validAmenities = formData.amenities.filter(amenity => amenity.trim() !== "");
    if (validAmenities.length === 0) {
      newErrors.amenities = "At least one amenity is required";
    }

    // Validate extra bed price if extra bed is available
    if (formData.extraBedAvailable && !formData.extraBedPrice.trim()) {
      newErrors.extraBedPrice = "Extra bed price is required when extra bed is available";
    }

    // Validate check-in and check-out times
    if (!formData.checkInTime) {
      newErrors.checkInTime = "Check-in time is required";
    }

    if (!formData.checkOutTime) {
      newErrors.checkOutTime = "Check-out time is required";
    }

    // Validate that check-out time is after check-in time
    if (formData.checkInTime && formData.checkOutTime) {
      const checkIn = new Date(`2000-01-01T${formData.checkInTime}`);
      const checkOut = new Date(`2000-01-01T${formData.checkOutTime}`);

      if (checkOut <= checkIn) {
        newErrors.checkOutTime = "Check-out time must be after check-in time";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    const filteredAmenities = formData.amenities.filter(amenity => amenity.trim() !== "");

    onSubmit({
      ...formData,
      amenities: filteredAmenities,
      images: formData.images
    });

    // Reset form only if adding (not editing)
    if (mode === 'add') {
      setFormData(getInitialFormData());
    }

    setErrors({});
    onClose();
  };

  const handleClose = () => {
    // Reset form and errors when closing
    if (mode === 'add') {
      setFormData(getInitialFormData());
    }
    setErrors({});
    onClose();
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    // Close modal when clicking on backdrop
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  if (!isOpen) return null;

  const modalTitle = mode === 'edit' ? 'Edit Room Type' : mode === 'view' ? 'View Room Type' : 'Add New Room Type';
  const submitButtonText = mode === 'edit' ? 'Update Room Type' : 'Add Room Type';
  const isViewMode = mode === 'view';

  return (
    <div
      className={`agent-create-container ${isOpen ? 'show' : ''}`}
      onClick={handleBackdropClick}
    >
      <div className="agent-create-form full-width">
        <div className="closeIcon" onClick={handleClose}>
          <FiX />
        </div>

        <h3>{modalTitle}</h3>

        <form onSubmit={handleSubmit} className="form-content">
          <div className="form-row">
            <div className="input-field-container">
              <div className="input-field">
                <label>Room Type Name *</label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="e.g., Deluxe Suite"
                  className={errors.name ? 'error' : ''}
                  readOnly={isViewMode}
                />
                {errors.name && <span className="error-message">{errors.name}</span>}
              </div>
            </div>
          </div>

          <div className="form-row">
            <div className="input-field-container">
              <div className="input-field">
                <label>Description *</label>
            <textarea
  name="description"
  value={formData.description}
  onChange={handleInputChange}
  placeholder="Describe the room type..."
  rows={3}
  className={errors.description ? 'error' : ''}
  readOnly={isViewMode}
/>
                {errors.description && <span className="error-message">{errors.description}</span>}
              </div>
            </div>
          </div>

          <div className="form-row double">
            <div className="input-field-container">
              <div className="input-field">
                <label>Capacity (Guests) *</label>
                <input
                  type="number"
                  name="capacity"
                  value={formData.capacity}
                  onChange={handleInputChange}
                  min="1"
                  placeholder="1"
                  className={errors.capacity ? 'error' : ''}
                />
                {errors.capacity && <span className="error-message">{errors.capacity}</span>}
              </div>
            </div>
            <div className="input-field-container">
              <div className="input-field">
                <label>Price Per Night *</label>
                <input
                  type="text"
                  name="pricePerNight"
                  value={formData.pricePerNight}
                  onChange={handleInputChange}
                  placeholder="$150"
                  className={errors.pricePerNight ? 'error' : ''}
                />
                {errors.pricePerNight && <span className="error-message">{errors.pricePerNight}</span>}
              </div>
            </div>
          </div>

          <div className="form-row">
            <div className="input-field-container">
              <div className="input-field">
                <label>Available Rooms *</label>
                <input
                  type="number"
                  name="availability"
                  value={formData.availability}
                  onChange={handleInputChange}
                  min="0"
                  placeholder="5"
                  className={errors.availability ? 'error' : ''}
                />
                {errors.availability && <span className="error-message">{errors.availability}</span>}
              </div>
            </div>
          </div>

          {/* Room Size and Bed Type */}
          <div className="form-row double">
            <div className="input-field-container">
              <div className="input-field">
                <label>Room Size</label>
                <input
                  type="text"
                  name="roomSize"
                  value={formData.roomSize}
                  onChange={handleInputChange}
                  placeholder="e.g., 45 sqm"
                  className={errors.roomSize ? 'error' : ''}
                />
                {errors.roomSize && <span className="error-message">{errors.roomSize}</span>}
              </div>
            </div>
            <div className="input-field-container">
              <div className="input-field">
                <label>Bed Type</label>
             <select
  name="bedType"
  value={formData.bedType}
  onChange={handleInputChange}
  className={errors.bedType ? 'error' : ''}
  disabled={isViewMode}
>
                  <option value="">Select bed type</option>
                  <option value="Single Bed">Single Bed</option>
                  <option value="Twin Beds">Twin Beds</option>
                  <option value="Double Bed">Double Bed</option>
                  <option value="Queen Size">Queen Size</option>
                  <option value="King Size">King Size</option>
                  <option value="Sofa Bed">Sofa Bed</option>
                  <option value="Bunk Bed">Bunk Bed</option>
                </select>
                {errors.bedType && <span className="error-message">{errors.bedType}</span>}
              </div>
            </div>
          </div>

          {/* View Type and Floor Range */}
          <div className="form-row double">
            <div className="input-field-container">
              <div className="input-field">
                <label>View Type</label>
                <select
                  name="viewType"
                  value={formData.viewType}
                  onChange={handleInputChange}
                  className={errors.viewType ? 'error' : ''}
                >
                  <option value="">Select view type</option>
                  <option value="Ocean View">Ocean View</option>
                  <option value="City View">City View</option>
                  <option value="Garden View">Garden View</option>
                  <option value="Mountain View">Mountain View</option>
                  <option value="Pool View">Pool View</option>
                  <option value="Courtyard View">Courtyard View</option>
                  <option value="No View">No View</option>
                </select>
                {errors.viewType && <span className="error-message">{errors.viewType}</span>}
              </div>
            </div>
            <div className="input-field-container">
              <div className="input-field">
                <label>Floor Range</label>
                <input
                  type="text"
                  name="floorRange"
                  value={formData.floorRange}
                  onChange={handleInputChange}
                  placeholder="e.g., 10-15, Ground Floor"
                  className={errors.floorRange ? 'error' : ''}
                />
                {errors.floorRange && <span className="error-message">{errors.floorRange}</span>}
              </div>
            </div>
          </div>

          {/* Max Occupancy and Bathroom Type */}
          <div className="form-row double">
            <div className="input-field-container">
              <div className="input-field">
                <label>Max Occupancy *</label>
                <input
                  type="number"
                  name="maxOccupancy"
                  value={formData.maxOccupancy}
                  onChange={handleInputChange}
                  min="1"
                  placeholder="4"
                  className={errors.maxOccupancy ? 'error' : ''}
                />
                {errors.maxOccupancy && <span className="error-message">{errors.maxOccupancy}</span>}
              </div>
            </div>
            <div className="input-field-container">
              <div className="input-field">
                <label>Bathroom Type</label>
                <select
                  name="bathroomType"
                  value={formData.bathroomType}
                  onChange={handleInputChange}
                  className={errors.bathroomType ? 'error' : ''}
                >
                  <option value="">Select bathroom type</option>
                  <option value="Private Bathroom">Private Bathroom</option>
                  <option value="Shared Bathroom">Shared Bathroom</option>
                  <option value="Ensuite Bathroom">Ensuite Bathroom</option>
                  <option value="Half Bathroom">Half Bathroom</option>
                </select>
                {errors.bathroomType && <span className="error-message">{errors.bathroomType}</span>}
              </div>
            </div>
          </div>

          {/* Smoking Policy */}
          <div className="form-row">
            <div className="input-field-container">
              <div className="input-field">
                <label>Smoking Policy</label>
                <select
                  name="smokingPolicy"
                  value={formData.smokingPolicy}
                  onChange={handleInputChange}
                  className={errors.smokingPolicy ? 'error' : ''}
                >
                  <option value="non-smoking">Non-Smoking</option>
                  <option value="smoking">Smoking Allowed</option>
                  <option value="both">Both Available</option>
                </select>
                {errors.smokingPolicy && <span className="error-message">{errors.smokingPolicy}</span>}
              </div>
            </div>
          </div>

          {/* Room Features Checkboxes */}
          <div className="form-row">
            <div className="input-field-container">
              <div className="input-field">
                <label>Room Features</label>
                <div className="checkbox-grid">
                  <div className="checkbox-item">
                    <input
                      type="checkbox"
                      id="balcony"
                      name="balcony"
                      checked={formData.balcony}
                      onChange={(e) => setFormData(prev => ({ ...prev, balcony: e.target.checked }))}
                    />
                    <label htmlFor="balcony">Balcony</label>
                  </div>
                  <div className="checkbox-item">
                    <input
                      type="checkbox"
                      id="airConditioning"
                      name="airConditioning"
                      checked={formData.airConditioning}
                      onChange={(e) => setFormData(prev => ({ ...prev, airConditioning: e.target.checked }))}
                    />
                    <label htmlFor="airConditioning">Air Conditioning</label>
                  </div>
                  <div className="checkbox-item">
                <input
  type="checkbox"
  id="wifi"
  name="wifi"
  checked={formData.wifi}
  onChange={(e) => setFormData(prev => ({ ...prev, wifi: e.target.checked }))}
  disabled={isViewMode}
/>
                    <label htmlFor="wifi">Free WiFi</label>
                  </div>
                  <div className="checkbox-item">
                    <input
                      type="checkbox"
                      id="extraBedAvailable"
                      name="extraBedAvailable"
                      checked={formData.extraBedAvailable}
                      onChange={(e) => setFormData(prev => ({ ...prev, extraBedAvailable: e.target.checked }))}
                    />
                    <label htmlFor="extraBedAvailable">Extra Bed Available</label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Extra Bed Price - Show only if extra bed is available */}
          {formData.extraBedAvailable && (
            <div className="form-row">
              <div className="input-field-container">
                <div className="input-field">
                  <label>Extra Bed Price</label>
                  <input
                    type="text"
                    name="extraBedPrice"
                    value={formData.extraBedPrice}
                    onChange={handleInputChange}
                    placeholder="e.g., $50/night"
                    className={errors.extraBedPrice ? 'error' : ''}
                  />
                  {errors.extraBedPrice && <span className="error-message">{errors.extraBedPrice}</span>}
                </div>
              </div>
            </div>
          )}

          {/* Check-in and Check-out Times */}
          <div className="form-row double">
            <div className="input-field-container">
              <div className="input-field">
                <label>Check-in Time</label>
                <input
                  type="time"
                  name="checkInTime"
                  value={formData.checkInTime}
                  onChange={handleInputChange}
                  className={errors.checkInTime ? 'error' : ''}
                />
                {errors.checkInTime && <span className="error-message">{errors.checkInTime}</span>}
              </div>
            </div>
            <div className="input-field-container">
              <div className="input-field">
                <label>Check-out Time</label>
                <input
                  type="time"
                  name="checkOutTime"
                  value={formData.checkOutTime}
                  onChange={handleInputChange}
                  className={errors.checkOutTime ? 'error' : ''}
                />
                {errors.checkOutTime && <span className="error-message">{errors.checkOutTime}</span>}
              </div>
            </div>
          </div>

          {/* Cancellation Policy */}
          <div className="form-row">
            <div className="input-field-container">
              <div className="input-field">
                <label>Cancellation Policy</label>
                <textarea
                  name="cancellationPolicy"
                  value={formData.cancellationPolicy}
                  onChange={handleInputChange}
                  placeholder="Describe the cancellation policy..."
                  rows={3}
                  className={errors.cancellationPolicy ? 'error' : ''}
                />
                {errors.cancellationPolicy && <span className="error-message">{errors.cancellationPolicy}</span>}
              </div>
            </div>
          </div>

          {/* Room Features List */}
          <div className="form-row">
            <div className="input-field-container">
              <div className="input-field">
                <label>Additional Room Features</label>
                <div className="amenities-list">
                  {formData.roomFeatures.map((feature, index) => (
                    <div key={index} className="amenity-row">
                      <input
                        type="text"
                        value={feature}
                        onChange={(e) => {
                          const newFeatures = [...formData.roomFeatures];
                          newFeatures[index] = e.target.value;
                          setFormData(prev => ({ ...prev, roomFeatures: newFeatures }));
                        }}
                        placeholder="e.g., Mini Bar, Safe, Coffee Machine"
                      />
                      {formData.roomFeatures.length > 1 && (
                        <button
                          type="button"
                          onClick={() => {
                            if (formData.roomFeatures.length > 1) {
                              const newFeatures = formData.roomFeatures.filter((_, i) => i !== index);
                              setFormData(prev => ({ ...prev, roomFeatures: newFeatures }));
                            }
                          }}
                          className="remove-amenity-btn"
                          aria-label="Remove feature"
                        >
                          <FiX />
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <div className="addMoreField">
            <p onClick={() => setFormData(prev => ({ ...prev, roomFeatures: [...prev.roomFeatures, ""] }))} role="button" tabIndex={0}>
              + Add another room feature
            </p>
          </div>

          <div className="form-row">
            <div className="input-field-container">
              <div className="input-field">
                <label>Amenities</label>
                <div className="amenities-list">
                  {formData.amenities.map((amenity, index) => (
                    <div key={index} className="amenity-row">
                      <input
                        type="text"
                        value={amenity}
                        onChange={(e) => handleAmenityChange(index, e.target.value)}
                        placeholder="e.g., Free WiFi"
                      />
                      {formData.amenities.length > 1 && (
                        <button
                          type="button"
                          onClick={() => removeAmenityField(index)}
                          className="remove-amenity-btn"
                          aria-label="Remove amenity"
                        >
                          <FiX />
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <div className="addMoreField">
            <p onClick={addAmenityField} role="button" tabIndex={0}>
              + Add another amenity
            </p>
          </div>

          {/* Enhanced File Upload Section */}
       <div
  className={`fileUpload ${dragActive ? 'drag-active' : ''} ${isViewMode ? 'disabled' : ''}`}
  onDragEnter={!isViewMode ? handleDrag : undefined}
  onDragLeave={!isViewMode ? handleDrag : undefined}
  onDragOver={!isViewMode ? handleDrag : undefined}
  onDrop={!isViewMode ? handleDrop : undefined}
  onClick={!isViewMode ? handleUploadClick : undefined}
>
            <input
              ref={fileInputRef}
              type="file"
              multiple
              accept="image/png,image/jpeg,image/jpg,image/gif"
              onChange={handleFileInputChange}
              style={{ display: 'none' }}
            />
            <div className="uploadIcon">
              <span>
                <FiUpload />
              </span>
            </div>
            <div className="desc">
              <span>Click to upload</span> room images or drag and drop
            </div>
            <div className="fileFormat">
              PNG, JPG, GIF up to 10MB
            </div>
          </div>

          {/* Display upload errors */}
          {errors.images && (
            <div className="error-message" style={{ textAlign: 'center', marginTop: '10px' }}>
              {errors.images}
            </div>
          )}

          {/* Display selected images */}
          {formData.images.length > 0 && (
            <div className="selected-images">
              <label>Selected Images ({formData.images.length})</label>
              <div className="images-preview">
                {formData.images.map((file, index) => (
                  <div key={index} className="image-preview-item">
                    <div className="image-info">
                      <FiImage />
                      <span className="image-name">{file.name}</span>
                      <span className="image-size">
                        ({(file.size / 1024 / 1024).toFixed(2)} MB)
                      </span>
                    </div>
                    <button
                      type="button"
                      onClick={() => removeImage(index)}
                      className="remove-image-btn"
                      aria-label="Remove image"
                    >
                      <FiX />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {!isViewMode && (
            <div className="SubmitBtn">
              <button
                type="submit"
                className="submit-button"
              >
                {submitButtonText}
              </button>
            </div>
          )}
        </form>
      </div>
    </div>
  );
};

export default RoomTypeModal;