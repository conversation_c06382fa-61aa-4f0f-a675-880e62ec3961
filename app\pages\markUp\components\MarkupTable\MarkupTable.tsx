"use client";
import React from "react";
import Image from "next/image";
import Link from "next/link";
import TableWithShimmer from "@/app/components/TableWithShimmer/TableWithShimmer";
import Pagination from "@/app/utilities/Pagination/Pagination";
import TableMenuTwo from "@/app/components/TableMenuTwo/TableMenuTwo";
import SearchBox from "@/app/components/SearchBox/SearchBox";
import profilePic from "@/public/images/profile.png";
import { DropdownItem } from "../Page";

// Define markup interface for type safety
export interface Markup {
  id: number;
  name: string;
  type: string;
  value: string;
  status: string;
  markup_code?: string;
  remarks?: string;
  walletAmount?: string;
}

interface MarkupTableProps {
  isTableLoading: boolean;
  markupList: Markup[];
  searchValue: string;
  handleSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  status: string;
  selectedDate: string;
  handleFilterSelect: (
    event: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>,
    item: string
  ) => void;
  isFilterOpen: boolean;
  handleToggleFilter: () => void;
  currentPage: number;
  itemsPerPage: number;
  currentItems: Markup[];
  handleShowDetails: (id: number) => void;
  handleMenuItemClick: (item: DropdownItem, id: number) => void;
  totalPages: number;
  handlePageChange: (pageNo: number) => void;
  handleItemsPerPageChange: (value: number) => void;
  pageName?: string;
  showFilter?: boolean;
}

const MarkupTable: React.FC<MarkupTableProps> = ({
  isTableLoading,
  markupList,
  searchValue,
  handleSearchChange,
  status,
  selectedDate,
  handleFilterSelect,
  isFilterOpen,
  handleToggleFilter,
  currentPage,
  itemsPerPage,
  currentItems,
  handleShowDetails,
  handleMenuItemClick,
  totalPages,
  handlePageChange,
  handleItemsPerPageChange,
  pageName = "markup",
  showFilter = true,
}) => {
  // Generate the menu items for each row
  const getMenuItems = (markup: Markup) => {
    return [
  
      { id: "edit-agent", label: "Edit Markup" },
      { id: "disable-agent", label: markup.status === "Active" ? "Disable Markup" : "Enable Markup" },
      { id: "delete-agent", label: "Delete Markup" },
    ];
  };

  return (
    <div className="user-list-table-container">
      <div className="tableBorder">
        <div className="table-header">
          <h5>{pageName === "homepage" ? "Wallet Balance" : "Hotel Markup List"}</h5>
          {pageName !== "homepage" && showFilter && (
            <div className="filter-search-container">
              <div className="filterButton" onClick={handleToggleFilter}>
                <button>
                  <i className="fa-solid fa-filter"></i>
                </button>
              </div>
              <div className={`filter-options-select-box ${isFilterOpen ? "show" : ""}`}>
                <div className="filterOption">
                  <span>Status {status ? `(${status})` : ""}</span>
                  <select
                    className="dropdown"
                    value={status}
                    onChange={(e) => handleFilterSelect(e, "status")}
                  >
                    <option value="">All</option>
                    <option value="Active">Active</option>
                    <option value="Inactive">Inactive</option>
                  </select>
                  <span className="material-icons">keyboard_arrow_down</span>
                </div>
           
              </div>
              <SearchBox
                value={searchValue}
                onChange={handleSearchChange}
                placeholders={["Search by hotel or markup name"]}
              />
            </div>
          )}
        </div>

        {isTableLoading ? (
          <TableWithShimmer
            no_of_rows={itemsPerPage < 8 ? itemsPerPage : 8}
            no_of_cols={pageName === "markup" ? 6 : 4}
            colWidths={pageName === "markup" ? [1.5, 1.5, 1, 1, 1, 0.5] : [1.5, 1.5, 1, 1]}
          />
        ) : (
          <div className="table-style table-vertical-scroll">
            <table>
              <thead>
                <tr>
                  {pageName === "markup" && (
                    <>
                      <th>Name</th>
                      <th>Type</th>
                      <th>Markup (₹ / %)</th>
                      <th>Status</th>
                      
                      <th>Action</th>
                    </>
                  )}

                  {pageName === "homepage" && (
                    <>
                      <th>Name</th>
                      <th>Type</th>
                      <th>Markup (₹ / %)</th>
                      <th>Status</th>
                    </>
                  )}
                </tr>
              </thead>
              <tbody>
                {currentItems.length > 0 ? (
                  currentItems.map((markup) => (
                    <tr 
                      key={markup.id} 
                      onClick={() => handleShowDetails(markup.id)}
                      className="markup-row"
                    >
                      <td>
                        <div className="name-profilePic">
                          <div className="profile-pic">
                            <Image
                              src={profilePic}
                              alt="Profile Picture"
                              fill
                              className="profileImage"
                              style={{ objectFit: "cover" }}
                              sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
                              loading="lazy"
                            />
                          </div>
                          <div className="name">{markup.name}</div>
                        </div>
                      </td>
                      <td><div className="available"><span>{markup.type}</span></div></td>
                      {pageName === "homepage" ? (
                        <td><span className="walletAmount">{markup.walletAmount }</span></td>
                      ) : (
                        <td><span className="walletAmount">{markup.value}</span></td>
                      )}
                      <td>
                        <span className={`contact ${markup.status.toLowerCase() === 'active' ? 'active' : 'inactive'}`}>
                          {markup.status}
                        </span>
                      </td>
                      
                      {pageName === "markup" && (
                        <>
                          {/* <td onClick={(e) => e.stopPropagation()}>
                            <div className="viewDetails">
                              <Link href="#" onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                handleShowDetails(markup.id);
                              }}>
                                View Details
                              </Link>
                            </div>
                            
                          </td> */}
                          <td onClick={(e) => e.stopPropagation()}>
                            <TableMenuTwo
                              items={getMenuItems(markup)}
                              onClick={(item) => handleMenuItemClick(item, markup.id)}
                              id={markup.id}
                            />
                          </td>
                        </>
                      )}
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={pageName === "markup" ? 6 : 4} style={{ textAlign: "center" }}>
                      No markups found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        )}

        <Pagination
          totalPages={Math.max(1, totalPages)}
          handlePage={handlePageChange}
          itemsPerPage={itemsPerPage}
          page={currentPage}
          handleItemsPerPageChange={handleItemsPerPageChange}
        />
      </div>
    </div>
  );
};

export default MarkupTable;