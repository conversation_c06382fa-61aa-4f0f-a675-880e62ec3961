import { NextResponse } from 'next/server';
import { writeFile } from 'fs/promises';
import path from 'path';

export async function POST(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const formData = await request.formData();
    
    // Get form fields
    const hotelName = formData.get('hotel_name') as string;
    const hotelLocation = formData.get('hotel_location') as string;
    const hotelChain = formData.get('hotel_chain') as string;
    const logoChanged = formData.get('logo_changed') === 'true';
    const removedPhotoIndexes = JSON.parse(formData.get('removed_photo_indexes') as string || '[]');

    // Handle logo update if changed
    let newLogoPath = '';
    if (logoChanged) {
      const logoFile = formData.get('hotel_logo') as File;
      if (logoFile) {
        const logoBytes = await logoFile.arrayBuffer();
        const logoBuffer = Buffer.from(logoBytes);
        const logoFileName = `${Date.now()}-${logoFile.name}`;
        newLogoPath = path.join(process.cwd(), 'public', 'uploads', 'logos', logoFileName);
        await writeFile(newLogoPath, logoBuffer);
      }
    }

    // Handle new profile photos
    const newPhotos = formData.getAll('profile_photos') as File[];
    const newPhotoPaths: string[] = [];

    for (const photo of newPhotos) {
      const photoBytes = await photo.arrayBuffer();
      const photoBuffer = Buffer.from(photoBytes);
      const photoFileName = `${Date.now()}-${photo.name}`;
      const photoPath = path.join(process.cwd(), 'public', 'uploads', 'photos', photoFileName);
      await writeFile(photoPath, photoBuffer);
      newPhotoPaths.push(photoPath);
    }

    // Here you would typically update the data in your database
    // This is a placeholder for your database logic
    const updateData = {
      name: hotelName,
      location: hotelLocation,
      chain: hotelChain,
      ...(logoChanged && newLogoPath && { logo: newLogoPath }),
      newPhotos: newPhotoPaths,
      removedPhotoIndexes,
    };

    // TODO: Add your database update logic here
    // const updatedHotel = await prisma.hotel.update({
    //   where: { id: parseInt(params.id) },
    //   data: updateData
    // });

    return NextResponse.json({
      success: true,
      message: 'Hotel updated successfully',
      data: updateData
    });

  } catch (error) {
    console.error('Error updating hotel:', error);
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : 'Failed to update hotel'
    }, { status: 500 });
  }
}