@use "sass:color";
// Replace @import with @use
@use '/styles/variables' as *;

//@import './variables.scss';

.create-form-overlay{
    position: fixed;
    // z-index: 1000;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.7);
    backdrop-filter: blur(3px);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    z-index: -1;
    transition: opacity 0.3s ease, z-index 0s linear 0.3s;
    overflow: hidden;
    &.show{
        opacity: 1;
        z-index: 1000;

    }
    .create-form-container{
        width: 40dvw;
        max-width: 90dvw;
        background-color: $white_color;
        border-radius: 6px;
        padding: 40px 10px;
        position: relative;
        //margin: 0 30px;
        margin: 0 10px;
        .create-form-header-div{
            display: flex;
            // position: relative;
            justify-content: center;
            h3{
                text-align: center;
                margin-bottom: 20px;
                //color: darken( $black_color2, 15%);
                color: color.adjust($black_color2, $lightness: -15%);
                font-weight: 600;
                font-size: 18px;
            }
            .closeIcon{
                position: absolute;
                top: 15px;
                right: 15px;
                font-size: 15px;
                cursor: pointer;
                font-weight: 600;
            }
        }
        .create-form-div{
            max-height: 80dvh;
            padding: 0px 60px;
            overflow-y: auto;

            .input-field-container{
                width: 100%;
                display: flex;
                flex-wrap: wrap;
                gap: 10px;

                .input-field{
                    display: flex;
                    flex-direction: column;
                    margin-bottom: 4px;
                   
                    label{
                        font-size: 11px;
                        color: $black_color2;
                        font-weight: 600;
                        margin-bottom: 5px;

                    }
    
                    input{
                        padding: 8px;
                        border: 2px solid $black_color4;
                        border-radius: 6px;
                                           
                       &::placeholder{
                        color: $black_color4;
                        font-size:11px;
                       }
                    }

                    // select {
                    //     background-color: #ffffff;
                    //     border: 2px solid $black_color4;
                    //     padding: 1px;
                    //     border-radius: 6px;
                    //     font-size: 11px; // Ensure the default font size matches the options
                    //     color: $black_color4;
                      
                    //     &:focus {
                    //       outline: none;
                    //       border-color: $black_color2; // Highlight border on focus
                    //     }
                      
                    //     option {
                    //       font-size: 11px; // Font size for the dropdown options
                    //       color: $black_color4; // Text color for options
                    //     }
                    //   }
                      
                    
                     

                    .custom-autocomplete-wrapper input{
                        all: unset;
                    }
    
                    .input {
                        padding: 0 8px 0 5px;
                        padding-top: 5px;
                        padding-bottom: 5px;
                        border: 2px solid $black_color4;
                        border-radius: 6px;
                        background-color: #fff;
                      
                        select {
                          width: 100%;
                          font-size: 11px;
                          background-color: #fff;
                          color: $black_color4;
                          border: 0;
                          outline: none;
                          padding: 8px 0px;
                        }
                      
                        // Change border color on focus
                        &:focus-within {
                          border-color: $black_color; // Replace with your desired border color
                        }
                      
                        
                      }
                      
                }

                .select-input-field{
                    display: flex;
                    flex-direction: column;
                    margin-bottom: 4px;
                   
                    label{
                        font-size: 11px;
                        color: $black_color2;
                        font-weight: 600;
                        margin-bottom: 5px;
                    }
    
                    input{
                        padding: 8px;
                        
                        border-radius: 6px;
                                           
                       &::placeholder{
                        color: $black_color4;
                        font-size:11px;
                       }
                    }

                    // select {
                    //     background-color: #ffffff;
                    //     border: 2px solid $black_color4;
                    //     padding: 1px;
                    //     border-radius: 6px;
                    //     font-size: 11px; // Ensure the default font size matches the options
                    //     color: $black_color4;
                      
                    //     &:focus {
                    //       outline: none;
                    //       border-color: $black_color2; // Highlight border on focus
                    //     }
                      
                    //     option {
                    //       font-size: 11px; // Font size for the dropdown options
                    //       color: $black_color4; // Text color for options
                    //     }
                    //   }
                      
                    
                     

                    .custom-autocomplete-wrapper input{
                        all: unset;
                    }
    
                    .input {
                        padding: 0 8px 0 5px;
                        padding-top: 5px;
                        padding-bottom: 5px;
                        border: 2px solid $black_color4;
                        border-radius: 6px;
                        background-color: #fff;
                      
                        select {
                          width: 100%;
                          font-size: 11px;
                          background-color: #fff;
                          color: $black_color4;
                          border: 0;
                          outline: none;
                          padding: 8px 0px;
                        }
                      
                        // Change border color on focus
                        &:focus-within {
                          border-color: $black_color; // Replace with your desired border color
                        }
                      
                        
                      }
                      
                }

               
                .table-input-container{
                    width: 100%;
                    overflow: hidden;
                    margin-bottom: 20px;
                    display: flex;
                    flex-direction: column;
                    gap: 5px;
                    h5{
                        font-weight: 600;
                        color: #787988;
                        font-size: 14px;
                        margin-bottom:2px;
                    }
                    .table-container{
                        width: 100%;
                        overflow-x: auto;
                        table{
                            width: 100%;
                            overflow-x: auto;
                            border-collapse: separate;
                            thead{
                                tr{
                                    th{
                                        padding: 3px 5px;
                                        font-size: 14px;
                                        color: #3F3F3F;
                                        font-weight: 700;
                                        background-color: #D3D3D3;
                                    }
                                }
                            }
                            tbody{
                                tr:nth-child(odd) { 
                                    background-color: #F0F0F0; 
                                }
                                tr:nth-child(even) { 
                                    background-color: #E8E8E8;
                                }
                                tr{

                                    
                                    td{
                                        text-align: center;
                                        

                                        input{
                                            height: 20px;
                                            padding-left: 10px;
                                            background: transparent;
                                            border: 0;
                                            max-width: 150px;
                                            border: none;
                                            outline: none;
                                            &::placeholder{
                                                font-size: 12px;
                                                color: #D4D4D4;
                                            }
                                            &:focus {
                                                outline: none;
                                                box-shadow: none;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                .table-input-container2{
                    width: 100%;
                    overflow: hidden;
                    margin-bottom: 20px;
                    display: flex;
                    flex-direction: column;
                    gap: 5px;
                    h5{
                        font-weight: 600;
                        color: #787988;
                        font-size: 14px;
                        margin-bottom:2px;
                    }
                    .table-container{
                        width: 100%;
                        overflow-x: auto;
                        table{
                            width: 100%;
                            overflow-x: auto;
                            border-collapse: separate;
                            thead{
                                tr{
                                    th{
                                        padding: 3px 5px;
                                        font-size: 14px;
                                        color: #3F3F3F;
                                        font-weight: 700;
                                        background-color: #D3D3D3;
                                    }
                                }
                            }
                            tbody{
                                tr:nth-child(odd) { 
                                    background-color: #fff; 
                                }
                                tr:nth-child(even) { 
                                    background-color: #fff;
                                }
                                tr{
                                    td{
                                        text-align: center;
                                        //min-width: 180px;
                                        input{
                                            height: 20px;
                                            padding-left: 10px;
                                            background: transparent;
                                            border: 0;
                                            max-width: 150px;
                                            border: none;
                                            outline: none;
                                            &::placeholder{
                                                font-size: 12px;
                                                color: #D4D4D4;
                                            }
                                            &:focus {
                                                outline: none;
                                                box-shadow: none;
                                            }
                                        }

                                      
                                    }

                                    .customerName{
                                        min-width: 160px;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    @media screen and (max-width:1285px){
        .create-form-container{
            width: 60dvw;
        }
    }
    @media screen and (max-width:900px){
        .create-form-container{
            width: 80dvw;
        }
    }
    @media screen and (max-width:450px){
        .create-form-container{
            width: 90dvw;
            padding: 15px 5px;
            .create-form-div{
                padding: 0px 10px;
            }
        }
    }

    @media (max-width: $breakpoint-sm) {
        .create-form-container{

            .create-form-div{
                padding: 0 25px;

                .input-field-container{

                    .input-field{
                        input{
                            padding: 6px;
                        }
                    }

                }

            }

        }

        
    }
}



  
.custom-autocomplete-wrapper .MuiAutocomplete-option {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%; /* Adjust based on your layout */
  }
  

  