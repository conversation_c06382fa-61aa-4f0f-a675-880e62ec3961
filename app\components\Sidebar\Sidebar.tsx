// "use client";
// import "./Sidebar.scss";
// import Link from "next/link";
// import Logo from "../../../public/images/logo.png";
// import Logo2 from "../../../public/images/Vector (3).png";
// import Image from "next/image";
// import { useEffect, useState } from "react";
// import { usePathname } from "next/navigation";
// import { useCommonContext } from "@/app/context/commonContext";

// function Sidebar() {
//   const { isMenuExpanded, setIsMenuExpanded } = useCommonContext();
//   const [isSecondSidebarVisible, setIsSecondSidebarVisible] = useState(false);

//   const currentRoute = usePathname();

//   useEffect(() => {
//     if (isMenuExpanded) {
//       setIsSecondSidebarVisible(false); // Hide second sidebar when the main one expands
//     } else {
//       setTimeout(() => {
//         setIsSecondSidebarVisible(true); // Show second sidebar after the first hides
//       }, 300); // Delay to match the transition duration of the first sidebar
//     }
//   }, [isMenuExpanded]);
  
//   return (
//     <div className="side-menu-bar-wrapper">
//       <div
//         className={`sidebar-mainContainer ${!isMenuExpanded ? "show" : "hide"}`}
//       >
//         <div className="headerPart">
//           <Image src={Logo2} alt="" />
//         </div>
//         <div className="sidebar-container">
//           <div
//             className="chevron-right"
//             onClick={() => setIsMenuExpanded(true)}
//           >
//             <span className="material-icons">chevron_right</span>
//           </div>
//           <ul>
//             <li>
//               <Link href="/pages/home">
//                 <div
//                   className={currentRoute === "/pages/home" ? "active" : "icon"}
//                 >
//                   <span className="material-icons">home</span>
//                 </div>
//               </Link>
//             </li>

//             <li>
//               <Link href="/pages/hotelMaster">
//                 <div
//                   className={
//                     currentRoute === "/pages/hotelMaster" ? "active" : "icon"
//                   }
//                 >
//                   <span className="material-icons">business</span>
//                 </div>
//               </Link>
//             </li>

//             <li>
//               <Link href="/pages/markUp">
//                 <div
//                   className={
//                     currentRoute === "/pages/markUp" ? "active" : "icon"
//                   }
//                 >
//                   <span className="material-icons">trending_up</span>
//                 </div>
//               </Link>
//             </li>

//             <li>
//               <Link href="/pages/booking">
//                 <div
//                   className={
//                     currentRoute === "/pages/booking" ? "active" : "icon"
//                   }
//                 >
//                   <span className="material-icons">event_available</span>
//                 </div>
//               </Link>
//             </li>

//             <li>
//               <Link href="/pages/hotelProvider">
//                 <div
//                   className={
//                     currentRoute === "/pages/hotelProvider" ? "active" : "icon"
//                   }
//                 >
//                   <span className="material-icons">hotel</span>
//                 </div>
//               </Link>
//             </li>
//               <li>
//               <Link href="/pages/roomMaster">
//                 <div
//                   className={
//                     currentRoute === "/pages/roomMaster" ? "active" : "icon"
//                   }
//                 >
//                   <span className="material-icons">hotel</span>
//                 </div>
//               </Link>
//             </li>

  
//           </ul>
//         </div>
//       </div>

//       {!isSecondSidebarVisible && (
//         <div
//           className={`sidebar2-mainContainer ${
//             isMenuExpanded ? "show" : "hide"
//           }`}
//         >
//           <div className="headerPart">
//             <div className="image">
//               <Image src={Logo} width={100} height={100} alt="" />
//             </div>
//           </div>
//           <div className="sidebar-container2">
//             <div className="sidebarHeader">
//               <span className="header">MAIN MENU</span>
//               <div
//                 className="chevron-left"
//                 onClick={() => setIsMenuExpanded(false)}
//               >
//                 <span className="material-icons">chevron_left</span>
//               </div>
//             </div>
//             <ul>
//               <li className={currentRoute === "/pages/home" ? "active" : ""} onClick={()=>{setIsMenuExpanded(false)}}>
//                 <Link href="/pages/home">
//                   <span className="material-icons">home</span>Home
//                 </Link>
//               </li>

//               <li className={currentRoute === "/pages/hotelMaster" ? "active" : ""} onClick={()=>{setIsMenuExpanded(false)}}>
//                 <Link href="/pages/hotelMaster">
//                   <span className="material-icons">business</span>Hotel Master
//                 </Link>
//               </li>

//               <li className={currentRoute === "/pages/markUp" ? "active" : ""} onClick={()=>{setIsMenuExpanded(false)}}>
//                 <Link href="/pages/markUp">
//                   <span className="material-icons">trending_up</span>Mark Up
//                 </Link>
//               </li>

//               <li className={currentRoute === "/pages/booking" ? "active" : ""} onClick={()=>{setIsMenuExpanded(false)}}>
//                 <Link href="/pages/booking">
//                   <span className="material-icons">event_available</span>Booking
//                 </Link>
//               </li>

//               <li className={currentRoute === "/pages/hotelProvider" ? "active" : ""} onClick={()=>{setIsMenuExpanded(false)}}>
//                 <Link href="/pages/hotelProvider">
//                   <span className="material-icons">hotel</span>Hotel Provider
//                 </Link>
//               </li>

          
//               <li
//                 className={currentRoute === "/pages/roomMaster" ? "active" : ""}
//                 onClick={()=>{setIsMenuExpanded(false)}}>
//                 <Link href="/pages/roomMaster">
//                   <span className="material-icons">group</span>
//                   Room Master
//                 </Link>
//               </li>

          
//             </ul>
//           </div>
//         </div>
//       )}
//     </div>
//   );
// }

// export default Sidebar;

"use client";
import "./Sidebar.scss";
import Link from "next/link";
import Logo from "../../../public/images/logo.png";
import Logo2 from "../../../public/images/Vector (3).png";
import Image from "next/image";
import { useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import { useCommonContext } from "@/app/context/commonContext";

function Sidebar() {
  const { isMenuExpanded, setIsMenuExpanded } = useCommonContext();
  const [isSecondSidebarVisible, setIsSecondSidebarVisible] = useState(false);

  const currentRoute = usePathname();

  useEffect(() => {
    if (isMenuExpanded) {
      setIsSecondSidebarVisible(false); // Hide second sidebar when the main one expands
    } else {
      const timer = setTimeout(() => {
        setIsSecondSidebarVisible(true); // Show second sidebar after the first hides
      }, 300); // Delay to match the transition duration of the first sidebar
      
      // Cleanup timeout on unmount or dependency change
      return () => clearTimeout(timer);
    }
  }, [isMenuExpanded]);
  
  return (
    <div className="side-menu-bar-wrapper">
      <div
        className={`sidebar-mainContainer ${!isMenuExpanded ? "show" : "hide"}`}
      >
        <div className="headerPart">
          {/* <Image src={Logo2} alt="Logo" /> */}
        </div>
        <div className="sidebar-container">
          <div
            className="chevron-right"
            onClick={() => setIsMenuExpanded(true)}
          >
            <span className="material-icons">chevron_right</span>
          </div>
          <ul>
            <li>
              <Link href="/pages/home">
                <div
                  className={currentRoute === "/pages/home" ? "active" : "icon"}
                >
                  <span className="material-icons">home</span>
                </div>
              </Link>
            </li>

            <li>
              <Link href="/pages/hotelMaster">
                <div
                  className={
                    currentRoute === "/pages/hotelMaster" ? "active" : "icon"
                  }
                >
                  <span className="material-icons">business</span>
                </div>
              </Link>
            </li>

            <li>
              <Link href="/pages/markUp">
                <div
                  className={
                    currentRoute === "/pages/markUp" ? "active" : "icon"
                  }
                >
                  <span className="material-icons">trending_up</span>
                </div>
              </Link>
            </li>

            <li>
              <Link href="/pages/booking">
                <div
                  className={
                    currentRoute === "/pages/booking" ? "active" : "icon"
                  }
                >
                  <span className="material-icons">event_available</span>
                </div>
              </Link>
            </li>

            <li>
              <Link href="/pages/hotelProvider">
                <div
                  className={
                    currentRoute === "/pages/hotelProvider" ? "active" : "icon"
                  }
                >
                  <span className="material-icons">hotel</span>
                </div>
              </Link>
            </li>
        
          </ul>
        </div>
      </div>

      {!isSecondSidebarVisible && (
        <div
          className={`sidebar2-mainContainer ${
            isMenuExpanded ? "show" : "hide"
          }`}
        >
          <div className="headerPart">
            <div className="image">
              <Image src={Logo} width={100} height={100} alt="Main Logo" />
            </div>
          </div>
          <div className="sidebar-container2">
            <div className="sidebarHeader">
              <span className="header">MAIN MENU</span>
              <div
                className="chevron-left"
                onClick={() => setIsMenuExpanded(false)}
              >
                <span className="material-icons">chevron_left</span>
              </div>
            </div>
            <ul>
              <li 
                className={currentRoute === "/pages/home" ? "active" : ""} 
                onClick={() => setIsMenuExpanded(false)}
              >
                <Link href="/pages/home">
                  <span className="material-icons">home</span>Home
                </Link>
              </li>

              <li 
                className={currentRoute === "/pages/hotelMaster" ? "active" : ""} 
                onClick={() => setIsMenuExpanded(false)}
              >
                <Link href="/pages/hotelMaster">
                  <span className="material-icons">business</span>Hotel Master
                </Link>
              </li>

              <li 
                className={currentRoute === "/pages/markUp" ? "active" : ""} 
                onClick={() => setIsMenuExpanded(false)}
              >
                <Link href="/pages/markUp">
                  <span className="material-icons">trending_up</span>Mark Up
                </Link>
              </li>

              <li 
                className={currentRoute === "/pages/booking" ? "active" : ""} 
                onClick={() => setIsMenuExpanded(false)}
              >
                <Link href="/pages/booking">
                  <span className="material-icons">event_available</span>Booking
                </Link>
              </li>

              <li 
                className={currentRoute === "/pages/hotelProvider" ? "active" : ""} 
                onClick={() => setIsMenuExpanded(false)}
              >
                <Link href="/pages/hotelProvider">
                  <span className="material-icons">hotel</span>Hotel Provider
                </Link>
              </li>

              <li
                className={currentRoute === "/pages/roomMaster" ? "active" : ""}
                onClick={() => setIsMenuExpanded(false)}
              >
             
              </li>
            </ul>
          </div>
        </div>
      )}
    </div>
  );
}

export default Sidebar;