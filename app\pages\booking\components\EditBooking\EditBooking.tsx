// import React, { useState, useEffect } from "react";


// import "./EditBooking.scss";
// import { useForm, Controller } from "react-hook-form";
// import { Autocomplete, TextField } from "@mui/material";
// import { useCommonContext } from "@/app/context/commonContext";
// import { useAlert } from "@/app/utilities/Alert/Alert";
// import { Edit } from "lucide-react";

// interface EditBookingProps {
//   showCreate: boolean;
//   handleCloseCreate: VoidFunction;
//   bookingId?: string;
//   bookingData?: any;
//   onUpdateSuccess?: VoidFunction;
// }

// interface RoomType {
//   id: number;
//   name: string;
// }

// interface HotelType {
//   id: number;
//   name: string;
// }

// interface PaymentStatusType {
//   id: number;
//   status: string;
// }

// interface BookingStatusType {
//   id: number;
//   status: string;
// }

// interface PaymentMethodType {
//   id: number;
//   method: string;
// }

// interface BookingFormData {
//   bookingId: string;
//   guestName: string;
//   hotelId: number;
//   hotelName: string;
//   roomTypeId: number;
//   roomType: string;
//   checkIn: string;
//   checkOut: string;
//   guests: number;
//   bookingDate: string;
//   paymentStatusId: number;
//   paymentStatus: string;
//   bookingStatusId: number;
//   bookingStatus: string;
//   amountPaid: string;
//   paymentMethodId: number;
//   paymentMethod: string;
//   is_active: boolean;
// }

// function EditBooking({ showCreate, handleCloseCreate, bookingId, bookingData, onUpdateSuccess }: EditBookingProps) {
//   const { fire } = useAlert();
//   const { setIsLoading } = useCommonContext();
//   const [isSubmitting, setIsSubmitting] = useState(false);
  
//   // Options for dropdowns
//   const roomTypeOptions: RoomType[] = [
//     { id: 1, name: "Deluxe Ocean View" },
//     { id: 2, name: "Standard Room" },
//     { id: 3, name: "Suite" },
//     { id: 4, name: "Executive Room" }
//   ];

//   const hotelOptions: HotelType[] = [
//     { id: 1, name: "Seaside Resort & Spa" },
//     { id: 2, name: "City Center Hotel" },
//     { id: 3, name: "Mountain View Resort" }
//   ];

//   const paymentStatusOptions: PaymentStatusType[] = [
//     { id: 1, status: "Paid" },
//     { id: 2, status: "Pending" },
//     { id: 3, status: "Refunded" },
//     { id: 4, status: "Canceled" }
//   ];

//   const bookingStatusOptions: BookingStatusType[] = [
//     { id: 1, status: "Confirmed" },
//     { id: 2, status: "Pending" },
//     { id: 3, status: "Awaiting Payment" },
//     { id: 4, status: "Canceled" },
//     { id: 5, status: "Completed" }
//   ];

//   const paymentMethodOptions: PaymentMethodType[] = [
//     { id: 1, method: "Credit Card" },
//     { id: 2, method: "Debit Card" },
//     { id: 3, method: "Bank Transfer" },
//     { id: 4, method: "PayPal" },
//     { id: 5, method: "Cash" }
//   ];

//   const {
//     register,
//     handleSubmit,
//     setValue,
//     control,
//     formState: { errors },
//     reset
//   } = useForm<BookingFormData>();

//   // Set form default values when component mounts or bookingId changes
//   useEffect(() => {
//     if (showCreate && bookingData) {
//       // Find the corresponding IDs from our option arrays
//       const hotelId = hotelOptions.find(h => h.name === bookingData.hotelName)?.id || 1;
//       const roomTypeId = roomTypeOptions.find(r => r.name === bookingData.roomType)?.id || 1;
//       const paymentStatusId = paymentStatusOptions.find(p => p.status === bookingData.paymentStatus)?.id || 1;
//       const bookingStatusId = bookingStatusOptions.find(b => b.status === bookingData.bookingStatus)?.id || 1;
//       const paymentMethodId = paymentMethodOptions.find(m => m.method === bookingData.paymentMethod)?.id || 1;
      
//       // Format amount correctly
//       const amountPaid = bookingData.amountPaid.replace('$', '').trim();
      
//       reset({
//         bookingId: bookingData.id,
//         guestName: bookingData.guestName,
//         hotelId,
//         hotelName: bookingData.hotelName,
//         roomTypeId,
//         roomType: bookingData.roomType,
//         checkIn: bookingData.checkIn,
//         checkOut: bookingData.checkOut,
//         guests: bookingData.guests,
//         bookingDate: bookingData.bookingDate,
//         paymentStatusId,
//         paymentStatus: bookingData.paymentStatus,
//         bookingStatusId,
//         bookingStatus: bookingData.bookingStatus,
//         amountPaid,
//         paymentMethodId,
//         paymentMethod: bookingData.paymentMethod,
//         is_active: bookingData.is_active
//       });
//     }
//   }, [showCreate, bookingData, reset]);

//   const onSubmit = (data: BookingFormData) => {
//     setIsSubmitting(true);
//     setIsLoading(true);
    
//     // Here you would normally send the data to your API
//     console.log("Submitting booking data:", data);
    
//     // Simulate API call with a 1-second delay
//     setTimeout(() => {
//       setIsLoading(false);
//       setIsSubmitting(false);
      
//       fire({
//         position: "center",
//         icon: "success",
//         title: "Success",
//         text: "Booking updated successfully",
//         confirmButtonText: "Ok", 
//         onConfirm: () => {
//           if (onUpdateSuccess) {
//             onUpdateSuccess();
//           }
//           handleCloseCreate();
//         },
//       });
//     }, 1000);
//   };

//   return (
//     <div className={`create-form-overlay ${showCreate ? "show" : ""}`}>
//       <div className="create-form-container">
//         <div className="create-form-header-div">
//           <h3>Edit Booking</h3>
//           <span
//             className="material-icons closeIcon"
//             onClick={handleCloseCreate}
//           >
//             close
//           </span>
//         </div>
//         <div className="create-form-div scroll-bar-1">
//           <form onSubmit={handleSubmit(onSubmit)}>
//             <div className="input-field-container">
//               <div className="input-field wf-50">
//                 <label htmlFor="bookingId">Booking ID</label>
//                 <input
//                   id="bookingId"
//                   type="text"
//                   placeholder="Booking ID"
//                   {...register("bookingId", {
//                     // required: "Booking ID is required",
//                   })}
//                   readOnly
//                 />
//                 <p className="error-message">{errors.bookingId?.message}</p>
//               </div>
              
//               <div className="input-field wf-50">
//                 <label htmlFor="bookingDate">Booking Date</label>
//                 <input
//                   id="bookingDate"
//                   type="date"
//                   {...register("bookingDate", {
//                     required: "Booking date is required",
//                   })}
//                 />
//                 <p className="error-message">{errors.bookingDate?.message}</p>
//               </div>

//               <div className="input-field wf-50">
//                 <label htmlFor="guestName">Guest Name</label>
//                 <input
//                   id="guestName"
//                   type="text"
//                   placeholder="Guest Name"
//                   {...register("guestName", {
//                     required: "Guest name is required",
//                   })}
//                 />
//                 <p className="error-message">{errors.guestName?.message}</p>
//               </div>

//               <div className="select-input-field wf-50">
//                 <label htmlFor="hotelId">Hotel</label>
//                 <Controller
//                   name="hotelId"
//                   control={control}
//                   rules={{ required: "Hotel is required" }}
//                   render={({ field }) => (
//                     <Autocomplete
//                       id="hotelId"
//                       options={hotelOptions}
//                       getOptionLabel={(option) => option.name}
//                       value={hotelOptions.find(option => option.id === field.value) || null}
//                       onChange={(_, value) => {
//                         setValue("hotelId", value?.id || 0);
//                         setValue("hotelName", value?.name || "");
//                       }}
//                       renderInput={(params) => (
//                         <TextField
//                           {...params}
//                           placeholder="Select Hotel"
//                           error={!!errors?.hotelId}
//                           variant="outlined"
//                           InputProps={{
//                             ...params.InputProps,
//                             style: {
//                               padding: "2px",
//                               borderRadius: "6px",
//                               backgroundColor: "#ffffff",
//                             },
//                           }}
//                           sx={{
//                             "& .MuiInputBase-input::placeholder": {
//                               opacity: 1,
//                             },
//                             "& .MuiOutlinedInput-root": {
//                               "& fieldset": {
//                                 border: "2px solid #D6D6D6",
//                               },
//                               "&:hover fieldset": {
//                                 border: "2px solid #D6D6D6",
//                               },
//                               "&.Mui-focused fieldset": {
//                                 border: "2px solid #1E1E1E",
//                               },
//                               "& input": {
//                                 padding: "10px",
//                               },
//                               "&.Mui-error fieldset": {
//                                 border: "2px solid #D6D6D6",
//                               },
//                               "&.Mui-focused.Mui-error fieldset": {
//                                 border: "2px solid #1E1E1E",
//                               },
//                             },
//                           }}
//                         />
//                       )}
//                     />
//                   )}
//                 />
//                 <p className="error-message">{errors.hotelId?.message}</p>
//               </div>

//               <div className="select-input-field wf-50">
//                 <label htmlFor="roomTypeId">Room Type</label>
//                 <Controller
//                   name="roomTypeId"
//                   control={control}
//                   rules={{ required: "Room type is required" }}
//                   render={({ field }) => (
//                     <Autocomplete
//                       id="roomTypeId"
//                       options={roomTypeOptions}
//                       getOptionLabel={(option) => option.name}
//                       value={roomTypeOptions.find(option => option.id === field.value) || null}
//                       onChange={(_, value) => {
//                         setValue("roomTypeId", value?.id || 0);
//                         setValue("roomType", value?.name || "");
//                       }}
//                       renderInput={(params) => (
//                         <TextField
//                           {...params}
//                           placeholder="Select Room Type"
//                           error={!!errors?.roomTypeId}
//                           variant="outlined"
//                           InputProps={{
//                             ...params.InputProps,
//                             style: {
//                               padding: "2px",
//                               borderRadius: "6px",
//                               backgroundColor: "#ffffff",
//                             },
//                           }}
//                           sx={{
//                             "& .MuiInputBase-input::placeholder": {
//                               opacity: 1,
//                             },
//                             "& .MuiOutlinedInput-root": {
//                               "& fieldset": {
//                                 border: "2px solid #D6D6D6",
//                               },
//                               "&:hover fieldset": {
//                                 border: "2px solid #D6D6D6",
//                               },
//                               "&.Mui-focused fieldset": {
//                                 border: "2px solid #1E1E1E",
//                               },
//                               "& input": {
//                                 padding: "10px",
//                               },
//                               "&.Mui-error fieldset": {
//                                 border: "2px solid #D6D6D6",
//                               },
//                               "&.Mui-focused.Mui-error fieldset": {
//                                 border: "2px solid #1E1E1E",
//                               },
//                             },
//                           }}
//                         />
//                       )}
//                     />
//                   )}
//                 />
//                 <p className="error-message">{errors.roomTypeId?.message}</p>
//               </div>

//               <div className="input-field wf-50">
//                 <label htmlFor="guests">Number of Guests</label>
//                 <input
//                   id="guests"
//                   type="number"
//                   placeholder="Number of Guests"
//                   min="1"
//                   {...register("guests", {
//                     required: "Number of guests is required",
//                     min: {
//                       value: 1,
//                       message: "At least 1 guest is required",
//                     },
//                     valueAsNumber: true
//                   })}
//                 />
//                 <p className="error-message">{errors.guests?.message}</p>
//               </div>

//               <div className="input-field wf-50">
//                 <label htmlFor="checkIn">Check-in Date</label>
//                 <input
//                   id="checkIn"
//                   type="date"
//                   {...register("checkIn", {
//                     required: "Check-in date is required",
//                   })}
//                 />
//                 <p className="error-message">{errors.checkIn?.message}</p>
//               </div>

//               <div className="input-field wf-50">
//                 <label htmlFor="checkOut">Check-out Date</label>
//                 <input
//                   id="checkOut"
//                   type="date"
//                   {...register("checkOut", {
//                     required: "Check-out date is required",
//                     validate: (value, formValues) => {
//                       if (value <= formValues.checkIn) {
//                         return "Check-out date must be after check-in date";
//                       }
//                       return true;
//                     }
//                   })}
//                 />
//                 <p className="error-message">{errors.checkOut?.message}</p>
//               </div>

//               <div className="input-field wf-50">
//                 <label htmlFor="amountPaid">Amount Paid</label>
//                 <input
//                   id="amountPaid"
//                   type="text"
//                   placeholder="0.00"
//                   {...register("amountPaid", {
//                     required: "Amount is required",
//                     pattern: {
//                       value: /^\d+(\.\d{1,2})?$/,
//                       message: "Please enter a valid amount",
//                     },
//                   })}
//                 />
//                 <p className="error-message">{errors.amountPaid?.message}</p>
//               </div>

//               <div className="select-input-field wf-50">
//                 <label htmlFor="paymentMethodId">Payment Method</label>
//                 <Controller
//                   name="paymentMethodId"
//                   control={control}
//                   rules={{ required: "Payment method is required" }}
//                   render={({ field }) => (
//                     <Autocomplete
//                       id="paymentMethodId"
//                       options={paymentMethodOptions}
//                       getOptionLabel={(option) => option.method}
//                       value={paymentMethodOptions.find(option => option.id === field.value) || null}
//                       onChange={(_, value) => {
//                         setValue("paymentMethodId", value?.id || 0);
//                         setValue("paymentMethod", value?.method || "");
//                       }}
//                       renderInput={(params) => (
//                         <TextField
//                           {...params}
//                           placeholder="Select Payment Method"
//                           error={!!errors?.paymentMethodId}
//                           variant="outlined"
//                           InputProps={{
//                             ...params.InputProps,
//                             style: {
//                               padding: "2px",
//                               borderRadius: "6px",
//                               backgroundColor: "#ffffff",
//                             },
//                           }}
//                           sx={{
//                             "& .MuiInputBase-input::placeholder": {
//                               opacity: 1,
//                             },
//                             "& .MuiOutlinedInput-root": {
//                               "& fieldset": {
//                                 border: "2px solid #D6D6D6",
//                               },
//                               "&:hover fieldset": {
//                                 border: "2px solid #D6D6D6",
//                               },
//                               "&.Mui-focused fieldset": {
//                                 border: "2px solid #1E1E1E",
//                               },
//                               "& input": {
//                                 padding: "10px",
//                               },
//                               "&.Mui-error fieldset": {
//                                 border: "2px solid #D6D6D6",
//                               },
//                               "&.Mui-focused.Mui-error fieldset": {
//                                 border: "2px solid #1E1E1E",
//                               },
//                             },
//                           }}
//                         />
//                       )}
//                     />
//                   )}
//                 />
//                 <p className="error-message">{errors.paymentMethodId?.message}</p>
//               </div>

//               <div className="select-input-field wf-50">
//                 <label htmlFor="paymentStatusId">Payment Status</label>
//                 <Controller
//                   name="paymentStatusId"
//                   control={control}
//                   rules={{ required: "Payment status is required" }}
//                   render={({ field }) => (
//                     <Autocomplete
//                       id="paymentStatusId"
//                       options={paymentStatusOptions}
//                       getOptionLabel={(option) => option.status}
//                       value={paymentStatusOptions.find(option => option.id === field.value) || null}
//                       onChange={(_, value) => {
//                         setValue("paymentStatusId", value?.id || 0);
//                         setValue("paymentStatus", value?.status || "");
//                       }}
//                       renderInput={(params) => (
//                         <TextField
//                           {...params}
//                           placeholder="Select Payment Status"
//                           error={!!errors?.paymentStatusId}
//                           variant="outlined"
//                           InputProps={{
//                             ...params.InputProps,
//                             style: {
//                               padding: "2px",
//                               borderRadius: "6px",
//                               backgroundColor: "#ffffff",
//                             },
//                           }}
//                           sx={{
//                             "& .MuiInputBase-input::placeholder": {
//                               opacity: 1,
//                             },
//                             "& .MuiOutlinedInput-root": {
//                               "& fieldset": {
//                                 border: "2px solid #D6D6D6",
//                               },
//                               "&:hover fieldset": {
//                                 border: "2px solid #D6D6D6",
//                               },
//                               "&.Mui-focused fieldset": {
//                                 border: "2px solid #1E1E1E",
//                               },
//                               "& input": {
//                                 padding: "10px",
//                               },
//                               "&.Mui-error fieldset": {
//                                 border: "2px solid #D6D6D6",
//                               },
//                               "&.Mui-focused.Mui-error fieldset": {
//                                 border: "2px solid #1E1E1E",
//                               },
//                             },
//                           }}
//                         />
//                       )}
//                     />
//                   )}
//                 />
//                 <p className="error-message">{errors.paymentStatusId?.message}</p>
//               </div>

//               <div className="select-input-field wf-50">
//                 <label htmlFor="bookingStatusId">Booking Status</label>
//                 <Controller
//                   name="bookingStatusId"
//                   control={control}
//                   rules={{ required: "Booking status is required" }}
//                   render={({ field }) => (
//                     <Autocomplete
//                       id="bookingStatusId"
//                       options={bookingStatusOptions}
//                       getOptionLabel={(option) => option.status}
//                       value={bookingStatusOptions.find(option => option.id === field.value) || null}
//                       onChange={(_, value) => {
//                         setValue("bookingStatusId", value?.id || 0);
//                         setValue("bookingStatus", value?.status || "");
//                       }}
//                       renderInput={(params) => (
//                         <TextField
//                           {...params}
//                           placeholder="Select Booking Status"
//                           error={!!errors?.bookingStatusId}
//                           variant="outlined"
//                           InputProps={{
//                             ...params.InputProps,
//                             style: {
//                               padding: "2px",
//                               borderRadius: "6px",
//                               backgroundColor: "#ffffff",
//                             },
//                           }}
//                           sx={{
//                             "& .MuiInputBase-input::placeholder": {
//                               opacity: 1,
//                             },
//                             "& .MuiOutlinedInput-root": {
//                               "& fieldset": {
//                                 border: "2px solid #D6D6D6",
//                               },
//                               "&:hover fieldset": {
//                                 border: "2px solid #D6D6D6",
//                               },
//                               "&.Mui-focused fieldset": {
//                                 border: "2px solid #1E1E1E",
//                               },
//                               "& input": {
//                                 padding: "10px",
//                               },
//                               "&.Mui-error fieldset": {
//                                 border: "2px solid #D6D6D6",
//                               },
//                               "&.Mui-focused.Mui-error fieldset": {
//                                 border: "2px solid #1E1E1E",
//                               },
//                             },
//                           }}
//                         />
//                       )}
//                     />
//                   )}
//                 />
//                 <p className="error-message">{errors.bookingStatusId?.message}</p>
//               </div>

//               <div className="input-field wf-50">
//                 <label htmlFor="is_active" className="checkbox-label">
//                   <input
//                     id="is_active"
//                     type="checkbox"
//                     {...register("is_active")}
//                   />
//                   <span className="checkbox-text">Active Booking</span>
//                 </label>
//               </div>
//             </div>
//             <div className="SubmitBtn">
//               <button 
//                 type="submit" 
//                 className="submitButton" 
//                 disabled={isSubmitting}
//               >
//                 {isSubmitting ? "Updating..." : "Update Booking"}
//               </button>
//             </div>
//           </form>
//         </div>
//       </div>
//     </div>
//   );
// }

// export default EditBooking;

import React, { useState, useEffect } from "react";
import "./EditBooking.scss";
import { useForm, Controller } from "react-hook-form";
import { Autocomplete, TextField } from "@mui/material";
import { useCommonContext } from "@/app/context/commonContext";
import { useAlert } from "@/app/utilities/Alert/Alert";

// Add FileUpload component import
// import FileUpload from "@/app/components/FileUpload/FileUpload";
import FileUpload from "@/app/components/Fileupload/FileUpload";

interface EditBookingProps {
  showCreate: boolean;
  handleCloseCreate: VoidFunction;
  bookingId?: string;
  bookingData?: any;
  onUpdateSuccess?: VoidFunction;
}

interface RoomType {
  id: number;
  name: string;
}

interface HotelType {
  id: number;
  name: string;
}

interface PaymentStatusType {
  id: number;
  status: string;
}

interface BookingStatusType {
  id: number;
  status: string;
}

interface PaymentMethodType {
  id: number;
  method: string;
}

interface BookingFormData {
  bookingId: string;
  guestName: string;
  hotelId: number;
  hotelName: string;
  roomTypeId: number;
  roomType: string;
  checkIn: string;
  checkOut: string;
  guests: number;
  bookingDate: string;
  paymentStatusId: number;
  paymentStatus: string;
  bookingStatusId: number;
  bookingStatus: string;
  amountPaid: string;
  paymentMethodId: number;
  paymentMethod: string;
  is_active: boolean;
  // Add profile image field
  profile_photo?: File | null;
}

function EditBooking({ showCreate, handleCloseCreate, bookingData, onUpdateSuccess }: EditBookingProps) {
  const { fire } = useAlert();
  const { setIsLoading } = useCommonContext();
  const [isSubmitting, setIsSubmitting] = useState(false);
  // Add state for profile image
  const [profileImage, setProfileImage] = useState<File | null>(null);
  
  // Define allowed file types and max size for image upload
  const ALLOWED_TYPES = ["image/jpeg", "image/png", "image/jpg"];
  const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
  
  // Options for dropdowns
  const roomTypeOptions: RoomType[] = [
    { id: 1, name: "Deluxe Ocean View" },
    { id: 2, name: "Standard Twin" },
    { id: 3, name: "Executive Suite" },
    { id: 4, name: "Standard Room" },
    { id: 5, name: "Suite" },
    { id: 6, name: "Executive Room" }
  ];

  const hotelOptions: HotelType[] = [
    { id: 1, name: "Seaside Resort & Spa" },
    { id: 2, name: "City Center Hotel" },
    { id: 3, name: "Mountain View Lodge" }
  ];

  const paymentStatusOptions: PaymentStatusType[] = [
    { id: 1, status: "Paid" },
    { id: 2, status: "Pending" },
    { id: 3, status: "Refunded" },
    { id: 4, status: "Canceled" }
  ];

  const bookingStatusOptions: BookingStatusType[] = [
    { id: 1, status: "Confirmed" },
    { id: 2, status: "Pending" },
    { id: 3, status: "Awaiting Payment" },
    { id: 4, status: "Canceled" },
    { id: 5, status: "Completed" }
  ];

  const paymentMethodOptions: PaymentMethodType[] = [
    { id: 1, method: "Credit Card" },
    { id: 2, method: "Debit Card" },
    { id: 3, method: "Bank Transfer" },
    { id: 4, method: "PayPal" },
    { id: 5, method: "Cash" }
  ];

  const {
    register,
    handleSubmit,
    setValue,
    control,
    formState: { errors },
    reset,
    watch
  } = useForm<BookingFormData>();

  // Watch the check-in date to validate check-out date
  const checkInDate = watch("checkIn");

  // Add handler for file upload
  const onFileUpload = (file: File | null) => {
    setProfileImage(file);
    setValue("profile_photo", file, { 
      shouldValidate: true 
    });
  };

  // Set form default values when component mounts or bookingData changes
  useEffect(() => {
    if (showCreate && bookingData) {
      // Find the corresponding IDs from our option arrays
      const hotelId = hotelOptions.find(h => h.name === bookingData.hotelName)?.id || 1;
      const roomTypeId = roomTypeOptions.find(r => r.name === bookingData.roomType)?.id || 1;
      const paymentStatusId = paymentStatusOptions.find(p => p.status === bookingData.paymentStatus)?.id || 1;
      const bookingStatusId = bookingStatusOptions.find(b => b.status === bookingData.bookingStatus)?.id || 1;
      const paymentMethodId = paymentMethodOptions.find(m => m.method === bookingData.paymentMethod)?.id || 1;
      
      // Format amount correctly - remove '$' if present
      const amountPaid = bookingData.amountPaid.replace(/[$,]/g, '').trim();
      
      // If bookingData has a profile image URL, we might want to display it
      if (bookingData.profileImageUrl) {
        // Here you might want to display the existing image
        // This depends on how your FileUpload component handles existing images
      }
      
      reset({
        bookingId: bookingData.id,
        guestName: bookingData.guestName,
        hotelId,
        hotelName: bookingData.hotelName,
        roomTypeId,
        roomType: bookingData.roomType,
        checkIn: bookingData.checkIn,
        checkOut: bookingData.checkOut,
        guests: bookingData.guests,
        bookingDate: bookingData.bookingDate,
        paymentStatusId,
        paymentStatus: bookingData.paymentStatus,
        bookingStatusId,
        bookingStatus: bookingData.bookingStatus,
        amountPaid,
        paymentMethodId,
        paymentMethod: bookingData.paymentMethod,
        is_active: bookingData.is_active,
        profile_photo: null // Reset profile photo field
      });
    }
  }, [showCreate, bookingData, reset, hotelOptions, roomTypeOptions, paymentStatusOptions, bookingStatusOptions, paymentMethodOptions]);

  const onSubmit = (data: BookingFormData) => {
    setIsSubmitting(true);
    setIsLoading(true);
    
    // Format the amount to include $ for consistency with display format
    const formattedData = {
      ...data,
      amountPaid: `$${parseFloat(data.amountPaid).toFixed(2)}`
    };
    
    // Here you would normally send the data to your API
    console.log("Submitting booking data:", formattedData);
    console.log("Profile image:", profileImage);
    
    // Simulate API call with a 1-second delay
    setTimeout(() => {
      setIsLoading(false);
      setIsSubmitting(false);
      
      if (onUpdateSuccess) {
        onUpdateSuccess();
      } else {
        handleCloseCreate();
      }
    }, 1000);
  };

  return (
    <div className={`create-form-overlay ${showCreate ? "show" : ""}`}>
      <div className="create-form-container">
        <div className="create-form-header-div">
          <h3>Edit Booking</h3>
          <span
            className="material-icons closeIcon"
            onClick={handleCloseCreate}
          >
            close
          </span>
        </div>
        <div className="create-form-div scroll-bar-1">
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="input-field-container">

           
              
              <div className="input-field wf-50">
                <label htmlFor="bookingId">Booking ID</label>
                <input
                  id="bookingId"
                  type="text"
                  placeholder="Booking ID"
                  {...register("bookingId")}
                  readOnly
                  className="read-only-field"
                />
              </div>
              
              <div className="input-field wf-50">
                <label htmlFor="bookingDate">Booking Date</label>
                <input
                  id="bookingDate"
                  type="date"
                  {...register("bookingDate", {
                    required: "Booking date is required",
                  })}
                />
                {errors.bookingDate && <p className="error-message">{errors.bookingDate.message}</p>}
              </div>

              <div className="input-field wf-50">
                <label htmlFor="guestName">Guest Name</label>
                <input
                  id="guestName"
                  type="text"
                  placeholder="Guest Name"
                  {...register("guestName", {
                    required: "Guest name is required",
                  })}
                />
                {errors.guestName && <p className="error-message">{errors.guestName.message}</p>}
              </div>

              <div className="select-input-field wf-50">
                <label htmlFor="hotelId">Hotel</label>
                <Controller
                  name="hotelId"
                  control={control}
                  rules={{ required: "Hotel is required" }}
                  render={({ field }) => (
                    <Autocomplete
                      id="hotelId"
                      options={hotelOptions}
                      getOptionLabel={(option) => option.name}
                      value={hotelOptions.find(option => option.id === field.value) || null}
                      onChange={(_, value) => {
                        setValue("hotelId", value?.id || 0);
                        setValue("hotelName", value?.name || "");
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Select Hotel"
                          error={!!errors?.hotelId}
                          variant="outlined"
                          InputProps={{
                            ...params.InputProps,
                            style: {
                              padding: "2px",
                              borderRadius: "6px",
                              backgroundColor: "#ffffff",
                            },
                          }}
                          sx={{
                            "& .MuiInputBase-input::placeholder": {
                              opacity: 1,
                            },
                            "& .MuiOutlinedInput-root": {
                              "& fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&:hover fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                              "& input": {
                                padding: "10px",
                              },
                              "&.Mui-error fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused.Mui-error fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                            },
                          }}
                        />
                      )}
                    />
                  )}
                />
                {errors.hotelId && <p className="error-message">{errors.hotelId.message}</p>}
              </div>

              <div className="select-input-field wf-50">
                <label htmlFor="roomTypeId">Room Type</label>
                <Controller
                  name="roomTypeId"
                  control={control}
                  rules={{ required: "Room type is required" }}
                  render={({ field }) => (
                    <Autocomplete
                      id="roomTypeId"
                      options={roomTypeOptions}
                      getOptionLabel={(option) => option.name}
                      value={roomTypeOptions.find(option => option.id === field.value) || null}
                      onChange={(_, value) => {
                        setValue("roomTypeId", value?.id || 0);
                        setValue("roomType", value?.name || "");
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Select Room Type"
                          error={!!errors?.roomTypeId}
                          variant="outlined"
                          InputProps={{
                            ...params.InputProps,
                            style: {
                              padding: "2px",
                              borderRadius: "6px",
                              backgroundColor: "#ffffff",
                            },
                          }}
                          sx={{
                            "& .MuiInputBase-input::placeholder": {
                              opacity: 1,
                            },
                            "& .MuiOutlinedInput-root": {
                              "& fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&:hover fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                              "& input": {
                                padding: "10px",
                              },
                              "&.Mui-error fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused.Mui-error fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                            },
                          }}
                        />
                      )}
                    />
                  )}
                />
                {errors.roomTypeId && <p className="error-message">{errors.roomTypeId.message}</p>}
              </div>

              <div className="input-field wf-50">
                <label htmlFor="guests">Number of Guests</label>
                <input
                  id="guests"
                  type="number"
                  placeholder="Number of Guests"
                  min="1"
                  {...register("guests", {
                    required: "Number of guests is required",
                    min: {
                      value: 1,
                      message: "At least 1 guest is required",
                    },
                    valueAsNumber: true
                  })}
                />
                {errors.guests && <p className="error-message">{errors.guests.message}</p>}
              </div>

              <div className="input-field wf-50">
                <label htmlFor="checkIn">Check-in Date</label>
                <input
                  id="checkIn"
                  type="date"
                  {...register("checkIn", {
                    required: "Check-in date is required",
                  })}
                />
                {errors.checkIn && <p className="error-message">{errors.checkIn.message}</p>}
              </div>

              <div className="input-field wf-50">
                <label htmlFor="checkOut">Check-out Date</label>
                <input
                  id="checkOut"
                  type="date"
                  {...register("checkOut", {
                    required: "Check-out date is required",
                    validate: value => {
                      if (!checkInDate) return true;
                      return value > checkInDate || "Check-out date must be after check-in date";
                    }
                  })}
                />
                {errors.checkOut && <p className="error-message">{errors.checkOut.message}</p>}
              </div>

              <div className="input-field wf-50">
                <label htmlFor="amountPaid">Amount Paid</label>
                <input
                  id="amountPaid"
                  type="text"
                  placeholder="0.00"
                  {...register("amountPaid", {
                    required: "Amount is required",
                    pattern: {
                      value: /^\d+(\.\d{1,2})?$/,
                      message: "Please enter a valid amount (e.g., 100.50)",
                    },
                  })}
                />
                {errors.amountPaid && <p className="error-message">{errors.amountPaid.message}</p>}
              </div>

              <div className="select-input-field wf-50">
                <label htmlFor="paymentMethodId">Payment Method</label>
                <Controller
                  name="paymentMethodId"
                  control={control}
                  rules={{ required: "Payment method is required" }}
                  render={({ field }) => (
                    <Autocomplete
                      id="paymentMethodId"
                      options={paymentMethodOptions}
                      getOptionLabel={(option) => option.method}
                      value={paymentMethodOptions.find(option => option.id === field.value) || null}
                      onChange={(_, value) => {
                        setValue("paymentMethodId", value?.id || 0);
                        setValue("paymentMethod", value?.method || "");
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Select Payment Method"
                          error={!!errors?.paymentMethodId}
                          variant="outlined"
                          InputProps={{
                            ...params.InputProps,
                            style: {
                              padding: "2px",
                              borderRadius: "6px",
                              backgroundColor: "#ffffff",
                            },
                          }}
                          sx={{
                            "& .MuiInputBase-input::placeholder": {
                              opacity: 1,
                            },
                            "& .MuiOutlinedInput-root": {
                              "& fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&:hover fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                              "& input": {
                                padding: "10px",
                              },
                              "&.Mui-error fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused.Mui-error fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                            },
                          }}
                        />
                      )}
                    />
                  )}
                />
                {errors.paymentMethodId && <p className="error-message">{errors.paymentMethodId.message}</p>}
              </div>

              <div className="select-input-field wf-50">
                <label htmlFor="paymentStatusId">Payment Status</label>
                <Controller
                  name="paymentStatusId"
                  control={control}
                  rules={{ required: "Payment status is required" }}
                  render={({ field }) => (
                    <Autocomplete
                      id="paymentStatusId"
                      options={paymentStatusOptions}
                      getOptionLabel={(option) => option.status}
                      value={paymentStatusOptions.find(option => option.id === field.value) || null}
                      onChange={(_, value) => {
                        setValue("paymentStatusId", value?.id || 0);
                        setValue("paymentStatus", value?.status || "");
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Select Payment Status"
                          error={!!errors?.paymentStatusId}
                          variant="outlined"
                          InputProps={{
                            ...params.InputProps,
                            style: {
                              padding: "2px",
                              borderRadius: "6px",
                              backgroundColor: "#ffffff",
                            },
                          }}
                          sx={{
                            "& .MuiInputBase-input::placeholder": {
                              opacity: 1,
                            },
                            "& .MuiOutlinedInput-root": {
                              "& fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&:hover fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                              "& input": {
                                padding: "10px",
                              },
                              "&.Mui-error fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused.Mui-error fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                            },
                          }}
                        />
                      )}
                    />
                  )}
                />
                {errors.paymentStatusId && <p className="error-message">{errors.paymentStatusId.message}</p>}
              </div>

              <div className="select-input-field wf-50">
                <label htmlFor="bookingStatusId">Booking Status</label>
                <Controller
                  name="bookingStatusId"
                  control={control}
                  rules={{ required: "Booking status is required" }}
                  render={({ field }) => (
                    <Autocomplete
                      id="bookingStatusId"
                      options={bookingStatusOptions}
                      getOptionLabel={(option) => option.status}
                      value={bookingStatusOptions.find(option => option.id === field.value) || null}
                      onChange={(_, value) => {
                        setValue("bookingStatusId", value?.id || 0);
                        setValue("bookingStatus", value?.status || "");
                      }}
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Select Booking Status"
                          error={!!errors?.bookingStatusId}
                          variant="outlined"
                          InputProps={{
                            ...params.InputProps,
                            style: {
                              padding: "2px",
                              borderRadius: "6px",
                              backgroundColor: "#ffffff",
                            },
                          }}
                          sx={{
                            "& .MuiInputBase-input::placeholder": {
                              opacity: 1,
                            },
                            "& .MuiOutlinedInput-root": {
                              "& fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&:hover fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                              "& input": {
                                padding: "10px",
                              },
                              "&.Mui-error fieldset": {
                                border: "2px solid #D6D6D6",
                              },
                              "&.Mui-focused.Mui-error fieldset": {
                                border: "2px solid #1E1E1E",
                              },
                            },
                          }}
                        />
                      )}
                    />
                  )}
                />
                {errors.bookingStatusId && <p className="error-message">{errors.bookingStatusId.message}</p>}
              </div>

          
              <div className="wf-100">
                <FileUpload
                  uploadedFile={profileImage}
                  isOpen={showCreate}
                  allowedTypes={ALLOWED_TYPES}
                  maxSize={MAX_FILE_SIZE}
                  onFileUpload={onFileUpload}
                  label="Guest Profile Image"
                  requiredMessage="Please upload a Profile Image."
                  maxFileSizeMessage="Profile image size is too large."
                  invalidTypeMessage="Invalid Profile image type."
                />
                {errors.profile_photo && (
                  <p className="error-message">
                    {errors.profile_photo.message}
                  </p>
                )}
              </div>
            </div>
            <div className="SubmitBtn">
              <button 
                type="submit" 
                className="submitButton" 
                disabled={isSubmitting}
              >
                {isSubmitting ? "Updating..." : "Update Booking"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}

export default EditBooking;