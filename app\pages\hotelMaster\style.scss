@use '/styles/variables' as *;

.agentList{
    width: 100%;
    background-color: $white_color1;
    height: auto;




  .table-style{
     table{

      tbody{
        tr{
          td{

             .name-profilePic {
          display: flex;
          flex-direction: row;
          align-items: center;
          min-width: 140px;

          .name {
            color: $black_color;
            font-size: 12px;
            font-weight: 700;
          }

          .profile-pic {
            position: relative;
            width: 40px;
            height: 40px;
            background-color: $black_color2;
            border-radius: 50%;
            margin-right: 5px;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            img {
              object-fit: contain;
            }
          }
        }


            .empId{
              color: $black_color3;
              font-weight: 600;
              font-size: 12px;
            }

            .walletAmount{
              color: $green_color2;
              font-weight: 600;
              font-size: 12px;
            }



            .contact{
              color: $black_color;
              font-weight: 600;
              font-size: 12px;
            }


            .user-not-available{
              width: 95px;
              color: $red_color;
              font-size: 10px;
              font-weight: 500;
              text-align: center;


             }
            .viewDetails{
              a{
                font-size: 14px;
              color: #637CBD;
              cursor: pointer;
              text-underline-offset: 4px;
              font-weight: 600;

             &:hover{
                  color: #4a5a9a;
              }
              }
             }

            // Hotel ID styling
            .hotel-id {
              color: $primary_color;
              font-weight: 700;
              font-size: 16px;
              background-color: rgba($primary_color, 0.1);
              padding: 6px 12px;
              border-radius: 6px;
              display: inline-block;
            }

            // Location styling
            .location {
              color: $black_color;
              font-weight: 500;
              font-size: 14px;
              display: flex;
              align-items: center;

              &::before {
                content: "📍";
                margin-right: 6px;
                font-size: 12px;
              }
            }

            // Chain name styling
            .chain-name {
              color: $black_color2;
              font-weight: 600;
              font-size: 14px;
            }

            // Provider count styling
            .provider-count {
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: 4px;

              .count-badge {
                background-color: $primary_color;
                color: $white_color1;
                font-weight: 700;
                font-size: 16px;
                padding: 6px 10px;
                border-radius: 50%;
                min-width: 28px;
                height: 28px;
                display: flex;
                align-items: center;
                justify-content: center;
              }

              .count-label {
                color: $black_color3;
                font-size: 11px;
                font-weight: 500;
                text-transform: uppercase;
                letter-spacing: 0.5px;
              }
            }

            // Mapping status container
            .mapping-status-container {
              display: flex;
              flex-direction: column;
              gap: 6px;
              padding: 8px;
              min-width: 160px;
            }

            // Mapping status styling
            .mapping-status {
              display: flex;
              align-items: center;
              gap: 8px;
              padding: 6px 10px;
              border-radius: 8px;
              font-size: 11px;
              font-weight: 600;
              text-transform: capitalize;
              letter-spacing: 0.3px;
              min-width: 100px;
              justify-content: flex-start;

              .status-dot {
                width: 8px;
                height: 8px;
                border-radius: 50%;
                flex-shrink: 0;
              }

              .status-text {
                white-space: nowrap;
                font-size: 11px;
                font-weight: 600;
              }

              // Completed status - GREEN
              &.completed {
                background-color: rgba(#10b981, 0.1);
                color: #059669;
                border: 1px solid rgba(#10b981, 0.3);

                .status-dot {
                  background-color: #10b981;
                  box-shadow: 0 0 0 2px rgba(#10b981, 0.2);
                }
              }

              // In Progress status - YELLOW/ORANGE
              &.in_progress {
                background-color: rgba(#f59e0b, 0.1);
                color: #d97706;
                border: 1px solid rgba(#f59e0b, 0.3);

                .status-dot {
                  background-color: #f59e0b;
                  box-shadow: 0 0 0 2px rgba(#f59e0b, 0.2);
                  animation: pulse 2s infinite;
                }
              }

              // Pending status - RED
              &.pending {
                background-color: rgba(#ef4444, 0.1);
                color: #dc2626;
                border: 1px solid rgba(#ef4444, 0.3);

                .status-dot {
                  background-color: #ef4444;
                  box-shadow: 0 0 0 2px rgba(#ef4444, 0.2);
                }
              }
            }

          }

        }

      }
        }

     }
}

// Pulse animation for in-progress status
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

