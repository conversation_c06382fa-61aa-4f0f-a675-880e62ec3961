@use '/styles/variables' as *;

.agentList{
    width: 100%;
    background-color: $white_color1;
    height: auto;




  .table-style{
     table{

      // Table header styling
      thead {
        tr {
          th {
            font-size: 14px;
            font-weight: 600;
            color: $black_color2;
            padding: 16px 0px;
            text-align: left;
            white-space: nowrap;

            @media (max-width: 1200px) {
              font-size: 13px;
              padding: 14px 0px;
            }

            @media (max-width: 768px) {
              font-size: 12px;
              padding: 12px 0px;
            }

            @media (max-width: 480px) {
              font-size: 11px;
              padding: 10px 0px;
            }
          }
        }
      }

      tbody{
        tr{
          td{

             .name-profilePic {
          display: flex;
          flex-direction: row;
          align-items: center;
          min-width: 140px;

          .name {
            color: $black_color;
            font-size: 14px;
            font-weight: 700;
          }

          .profile-pic {
            position: relative;
            width: 40px;
            height: 40px;
            background-color: $black_color2;
            border-radius: 50%;
            margin-right: 5px;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            img {
              object-fit: contain;
            }
          }
        }


            .empId{
              color: $black_color3;
              font-weight: 600;
              font-size: 14px;
            }

            .walletAmount{
              color: $green_color2;
              font-weight: 600;
              font-size: 14px;
            }



            .contact{
              color: $black_color;
              font-weight: 600;
              font-size: 14px;
            }


            .user-not-available{
              width: 95px;
              color: $red_color;
              font-size: 10px;
              font-weight: 500;
              text-align: center;


             }
            .viewDetails{
              a{
                font-size: 14px;
              color: #637CBD;
              cursor: pointer;
              text-underline-offset: 4px;
              font-weight: 600;


             &:hover{
                  color: darken(#637CBD, 10%);

              }
              }
             }

            // Hotel ID styling
            .hotel-id {
              color: $primary_color;
              font-weight: 700;
              font-size: 16px;
              background-color: rgba($primary_color, 0.1);
              padding: 6px 12px;
              border-radius: 6px;
              display: inline-block;
            }

            // Location styling
            .location {
              color: $black_color;
              font-weight: 500;
              font-size: 14px;
              display: flex;
              align-items: center;

              &::before {
                content: "📍";
                margin-right: 6px;
                font-size: 12px;
              }
            }

            // Chain name styling
            .chain-name {
              color: $black_color2;
              font-weight: 600;
              font-size: 14px;
            }

            // Provider count styling
            .provider-count {
              display: flex;
              flex-direction: column;
              align-items: center;
              gap: 4px;

              .count-badge {
                background-color: $primary_color;
                color: $white_color1;
                font-weight: 700;
                font-size: 16px;
                padding: 6px 10px;
                border-radius: 50%;
                min-width: 28px;
                height: 28px;
                display: flex;
                align-items: center;
                justify-content: center;
              }

              .count-label {
                color: $black_color3;
                font-size: 11px;
                font-weight: 500;
                text-transform: uppercase;
                letter-spacing: 0.5px;
              }
            }

            // Mapping status styling
            .mapping-status {
              display: flex;
              align-items: center;
              gap: 8px;
              padding: 6px 12px;
              border-radius: 12px;
              font-size: 12px;
              font-weight: 600;
              text-transform: uppercase;
              letter-spacing: 0.5px;

              .status-dot {
                width: 10px;
                height: 10px;
                border-radius: 50%;
                flex-shrink: 0;
              }

              .status-text {
                white-space: nowrap;
                font-size: 12px;
              }

              // Completed status
              &.completed {
                background-color: rgba($green_color2, 0.1);
                color: $green_color2;
                border: 1px solid rgba($green_color2, 0.2);

                .status-dot {
                  background-color: $green_color2;
                  box-shadow: 0 0 0 2px rgba($green_color2, 0.2);
                }
              }

              // In progress status
              &.in_progress {
                background-color: rgba($yellow_color, 0.1);
                color: darken($yellow_color, 20%);
                border: 1px solid rgba($yellow_color, 0.2);

                .status-dot {
                  background-color: $yellow_color;
                  box-shadow: 0 0 0 2px rgba($yellow_color, 0.2);
                  animation: pulse 2s infinite;
                }
              }

              // Pending status
              &.pending {
                background-color: rgba($red_color, 0.1);
                color: $red_color;
                border: 1px solid rgba($red_color, 0.2);

                .status-dot {
                  background-color: $red_color;
                  box-shadow: 0 0 0 2px rgba($red_color, 0.2);
                }
              }
            }

          }

        }

      }






        }

     }
}

// Pulse animation for in-progress status
@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// Responsive font size adjustments
@media (max-width: 1200px) {
  .agentList .table-style table tbody tr td {
    .name {
      font-size: 13px;
    }

    .hotel-id {
      font-size: 15px;
      padding: 5px 10px;
    }

    .location, .chain-name, .empId, .walletAmount, .contact {
      font-size: 13px;
    }

    .provider-count {
      .count-badge {
        font-size: 14px;
        min-width: 26px;
        height: 26px;
        padding: 5px 8px;
      }

      .count-label {
        font-size: 10px;
      }
    }

    .mapping-status {
      font-size: 11px;
      padding: 5px 10px;

      .status-dot {
        width: 8px;
        height: 8px;
      }

      .status-text {
        font-size: 11px;
      }
    }

    .viewDetails a {
      font-size: 13px;
    }
  }
}

@media (max-width: 768px) {
  .agentList .table-style table tbody tr td {
    .name {
      font-size: 12px;
    }

    .hotel-id {
      font-size: 14px;
      padding: 4px 8px;
    }

    .location, .chain-name, .empId, .walletAmount, .contact {
      font-size: 12px;
    }

    .location::before {
      font-size: 10px;
      margin-right: 4px;
    }

    .provider-count {
      .count-badge {
        font-size: 12px;
        min-width: 22px;
        height: 22px;
        padding: 4px 6px;
      }

      .count-label {
        font-size: 9px;
      }
    }

    .mapping-status {
      font-size: 10px;
      padding: 4px 8px;

      .status-dot {
        width: 6px;
        height: 6px;
      }

      .status-text {
        font-size: 10px;
      }
    }

    .viewDetails a {
      font-size: 12px;
    }
  }
}

@media (max-width: 480px) {
  .agentList .table-style table tbody tr td {
    .name {
      font-size: 11px;
    }

    .hotel-id {
      font-size: 12px;
      padding: 3px 6px;
    }

    .location, .chain-name, .empId, .walletAmount, .contact {
      font-size: 11px;
    }

    .provider-count {
      .count-badge {
        font-size: 11px;
        min-width: 20px;
        height: 20px;
        padding: 3px 5px;
      }

      .count-label {
        font-size: 8px;
      }
    }

    .mapping-status {
      font-size: 9px;
      padding: 3px 6px;

      .status-text {
        display: none; // Hide text on very small screens, show only dots
      }

      .status-dot {
        width: 8px;
        height: 8px;
      }
    }

    .viewDetails a {
      font-size: 11px;
    }
  }
}

