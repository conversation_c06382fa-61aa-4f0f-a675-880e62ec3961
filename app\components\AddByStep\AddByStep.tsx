 
"use client";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Field,
} from "@mui/material";
import {  useEffect, useState } from "react";
import { SubmitHandler, useForm } from "react-hook-form";
 
interface Props {
  id: number | null;
  showCreate: boolean;
  handleCloseCreate: VoidFunction;
}

export interface createCompany{
    username:string
    password:string
    company_name:string
    phone_number:string
    secondary_phone_number:string
    email:string
}

export interface Plan{
  id: number,
  name: string
}




  
 
function AddByStep({ id, showCreate, handleCloseCreate }: Props) {
//   const [options, setOptions] = useState<plan[]>([]);
  const [step, setStep] = useState(0);
  const [selectedPlan, setSelectedPlan] = useState<number | null>(null);
//   const [planError, setPlanError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [options, ] = useState<Plan[]>([
    { id: 1, name: "Basic Plan" },
    { id: 2, name: "Premium Plan" },
    { id: 3, name: "Enterprise Plan" },
  ]);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<createCompany>();
 

 
 
  const onSubmit: SubmitHandler<createCompany> = async (data) => {
console.log("data", data);
setLoading(false);

  };
 
  useEffect(() => {
    if (!showCreate) {
      reset();
      setStep(0);
    }
  }, [showCreate, reset]);
 
  // Step navigation
  const nextStep = () => setStep((prev) => prev + 1);
  const previousStep = () => setStep((prev) => (prev > 0 ? prev - 1 : prev));
 
  const steps = ["Details", "Plans"];

  const CustomStepIcon = ({ active, completed, index }: { active: boolean; completed: boolean; index: number }) => {
    return (
      <div
        style={{
          width: 24,
          height: 24,
          borderRadius: "50%",
          backgroundColor: completed ? "#1DABD9" : active ? "#1DABD9" : "#bdbdbd",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          color: "white",
          fontSize: "14px",
          fontWeight: "bold",
        }}
      >
        {index + 1}
      </div>
    );
  };
  
  
 

 
 
  return (
    <div className={`create-form-overlay ${showCreate ? "show" : ""}`}>
      <div className="create-form-container">
        <div className="create-form-header-div">
          <h3>{id ? "Update" : "Add"} Demo</h3>
          <span
            className="material-icons closeIcon"
            onClick={handleCloseCreate}
          >
            close
          </span>
        </div>
 
        <Stepper
          alternativeLabel
          activeStep={step}
          sx={{
            marginBottom: "20px",
            "& .MuiStepConnector-line": {
              borderColor: "#1DABD9", // Change connector line color
            },
            "& .MuiStepLabel-label": {
              fontSize: "14px",
              color: "#1DABD9", // Default step text color
            },
            "& .Mui-active .MuiStepLabel-label": {
              fontWeight: "bold",
              color: "#1DABD9", // Change active step text color
            },
            "& .Mui-completed .MuiStepLabel-label": {
              color: "#1DABD9", // Change completed step text color
            },
            "& .MuiStepIcon-root": {
              color: "#bdbdbd", // Default step icon color
            },
            "& .Mui-active .MuiStepIcon-root": {
              color: "#1DABD9", // Change active step icon color
            },
            "& .Mui-completed .MuiStepIcon-root": {
              color: "#1DABD9", // Change completed step icon color
            },
          }}
        >
          {steps.map((label, index) => (
            <Step key={label}>
             <StepLabel slots={{ stepIcon: (props) => <CustomStepIcon {...props} index={index} /> }}>
        {label}
      </StepLabel>
            </Step>
          ))}
        </Stepper>
 
        <div className="create-form-div scroll-bar-1">
          <form onSubmit={handleSubmit(onSubmit)}>
            {/* Step 1: Company Details */}
            {step === 0 && (
              <>
                <div className="input-field-container">
                  <div className="input-field wf-50">
                    <label htmlFor="cName">Demo Name</label>
                    <input
                      id="cName"
                      {...register("company_name", {
                        required: "Please enter the company's name.",
                      })}
                      type="text"
                      placeholder="Type..."
                    />
                    <p className="error-message">
                      {errors.company_name?.message}
                    </p>
                  </div>
 
                  <div className="input-field wf-50">
                    <label htmlFor="contact">Contact No</label>
                    <input
                      {...register("phone_number", {
                        required: "Please enter a contact number.",
                      })}
                      id="contact"
                      type="text"
                      placeholder="Type..."
                    />
                    <p className="error-message">
                      {errors.phone_number?.message}
                    </p>
                  </div>
 
                  <div className="input-field wf-50">
                    <label htmlFor="email">Email</label>
                    <input
                      {...register("email", {
                        required: "Please enter the company's email.",
                      })}
                      id="email"
                      type="text"
                      placeholder="Type..."
                    />
                    <p className="error-message">{errors.email?.message}</p>
                  </div>
 
                  <div className="input-field wf-50">
                    <label htmlFor="username">User Name</label>
                    <input
                      {...register("username", {
                        required: "Please enter the username.",
                      })}
                      id="username"
                      type="text"
                      placeholder="Type..."
                    />
                    <p className="error-message">{errors.username?.message}</p>
                  </div>
                </div>
 
                <div className="SubmitBtn">
                  <button
                    type="button"
                    className="submitButton"
                    onClick={nextStep}
                  >
                    Next
                  </button>
                </div>
              </>
            )}
 
            {/* Step 2: Plan Selection */}
            {step === 1 && (
              <>
                <div className="input-field-container">
                  <div className="select-input-field wf-100">
                    <label htmlFor="company">Plan</label>
                    <Autocomplete
                      id="company"
                      options={options}
                      getOptionLabel={(option) => option?.name}
                      value={
                        options.find((option) => option.id === selectedPlan) ||
                        null
                      }
                      onChange={(_, value) =>
                        setSelectedPlan(value?.id ?? null)
                      }
                      renderInput={(params) => (
                        <TextField
                          {...params}
                          placeholder="Select Plan"
                        //   error={!!planError}
                          variant="outlined"
                        />
                      )}
                    />
                    {/* {planError && (
                      <p className="error-message" style={{ color: "#f44336" }}>
                        {planError}
                      </p>
                    )} */}
                  </div>
                </div>
 
                <div className="SubmitBtn" style={{display:'flex', justifyContent:'space-between'}}>
                  <button
                    type="button"
                    className="submitButton"
                    onClick={previousStep}
                  >
                    Previous
                  </button>
                  <button
                    type="submit"
                    
                    className="submitButton"
                    disabled={loading}
                  >
                    {loading ? "Processing..." : "Submit"}
                  </button>
                </div>
              </>
            )}
          </form>
        </div>
      </div>
    </div>
  );
}
 
export default AddByStep;
 